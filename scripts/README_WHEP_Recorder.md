# WHEP Video Stream Recorder

这个项目包含两个Python脚本，用于订阅WHEP (WebRTC-HTTP Egress Protocol) 视频流并录制前10秒的内容。

## 文件说明

1. **whep_video_recorder.py** - 完整的WHEP录制器，使用aiortc库
2. **simple_whep_recorder.py** - 简化版本，使用FFmpeg
3. **requirements.txt** - Python依赖项列表

## 目标流地址

```
https://issac.qj-robots.com/rtc/v1/whep/?app=live&stream=camera_recognition
```

## 安装依赖

### 方法1: 使用完整版本 (推荐用于开发)

```bash
# 安装Python依赖
pip install -r scripts/requirements.txt

# 运行完整版本
python scripts/whep_video_recorder.py
```

### 方法2: 使用简化版本 (推荐用于快速测试)

```bash
# 安装基础依赖
pip install requests

# 确保系统安装了FFmpeg
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
# Windows: 下载FFmpeg并添加到PATH

# 运行简化版本
python scripts/simple_whep_recorder.py
```

## 使用方法

### 基本使用

两个脚本都可以直接运行，会自动录制10秒视频：

```bash
# 使用完整版本
python scripts/whep_video_recorder.py

# 使用简化版本
python scripts/simple_whep_recorder.py
```

### 自定义参数

可以修改脚本中的参数：

```python
# 在main()函数中修改这些参数
recorder = WHEPVideoRecorder(
    whep_url="https://issac.qj-robots.com/rtc/v1/whep/?app=live&stream=camera_recognition",
    output_file="recordings/my_custom_name.mp4",  # 自定义输出文件名
    duration=10  # 录制时长（秒）
)
```

## 输出文件

录制的视频文件将保存在 `recordings/` 目录下：

- 默认文件名格式: `whep_recording_YYYYMMDD_HHMMSS.mp4`
- 自定义文件名: 可在代码中指定

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认WHEP服务器是否可访问
   - 检查防火墙设置

2. **FFmpeg未找到** (简化版本)
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   
   # Windows
   # 下载FFmpeg并添加到系统PATH
   ```

3. **依赖安装失败**
   ```bash
   # 升级pip
   pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r scripts/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

4. **权限问题**
   ```bash
   # 确保有写入权限
   chmod +x scripts/*.py
   mkdir -p recordings
   ```

### 调试模式

脚本包含详细的日志输出，可以帮助诊断问题：

```python
# 在脚本开头修改日志级别
logging.basicConfig(level=logging.DEBUG)
```

## 技术说明

### WHEP协议

WHEP (WebRTC-HTTP Egress Protocol) 是一个标准协议，用于通过HTTP从WebRTC服务器获取媒体流。

### 工作流程

1. 创建WebRTC offer (SDP)
2. 发送POST请求到WHEP端点
3. 接收SDP answer
4. 建立WebRTC连接
5. 录制媒体流

### 脚本差异

| 特性 | 完整版本 | 简化版本 |
|------|----------|----------|
| 依赖项 | 较多 (aiortc等) | 较少 (requests) |
| WebRTC支持 | 原生支持 | 通过FFmpeg |
| 错误处理 | 详细 | 基础 |
| 自定义性 | 高 | 中等 |
| 安装复杂度 | 高 | 低 |

## 许可证

此脚本仅用于学习和测试目的。请确保遵守相关的服务条款和法律法规。
