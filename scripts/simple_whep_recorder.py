#!/usr/bin/env python3
"""
Simple WHEP Video Stream Recorder

A simplified version that attempts to record WHEP stream using different approaches.
This script provides multiple methods to try recording the video stream.

Requirements:
- requests: For HTTP requests
- subprocess: For calling external tools
- urllib: For URL handling

Usage:
    python simple_whep_recorder.py
"""

import requests
import subprocess
import time
import logging
import os
from datetime import datetime
from pathlib import Path
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleWHEPRecorder:
    def __init__(self, whep_url: str, output_file: str = None, duration: int = 10):
        """
        Initialize Simple WHEP Video Recorder
        
        Args:
            whep_url: WHEP endpoint URL
            output_file: Output video file path (default: auto-generated)
            duration: Recording duration in seconds (default: 10)
        """
        self.whep_url = whep_url
        self.duration = duration
        
        # Generate output filename if not provided
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_file = f"recordings/whep_recording_{timestamp}.mp4"
        else:
            self.output_file = output_file
            
        # Ensure output directory exists
        Path(self.output_file).parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"WHEP URL: {self.whep_url}")
        logger.info(f"Output file: {self.output_file}")
        logger.info(f"Recording duration: {self.duration} seconds")

    def check_ffmpeg(self):
        """Check if FFmpeg is available"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("FFmpeg is available")
                return True
            else:
                logger.warning("FFmpeg not found or not working")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("FFmpeg not found")
            return False

    def record_with_ffmpeg(self):
        """Try to record using FFmpeg (if available)"""
        if not self.check_ffmpeg():
            raise Exception("FFmpeg is required but not available")
        
        try:
            logger.info("Attempting to record with FFmpeg...")
            
            # FFmpeg command to record from WHEP endpoint
            # Note: This might need adjustment based on the actual WHEP implementation
            cmd = [
                'ffmpeg',
                '-y',  # Overwrite output file
                '-t', str(self.duration),  # Duration
                '-i', self.whep_url,  # Input URL
                '-c:v', 'libx264',  # Video codec
                '-c:a', 'aac',  # Audio codec (if any)
                '-f', 'mp4',  # Output format
                self.output_file
            ]
            
            logger.info(f"Running command: {' '.join(cmd)}")
            
            # Run FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.duration + 30)
            
            if result.returncode == 0:
                logger.info("FFmpeg recording completed successfully")
                return True
            else:
                logger.error(f"FFmpeg failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg recording timed out")
            return False
        except Exception as e:
            logger.error(f"FFmpeg recording error: {e}")
            return False

    def test_whep_endpoint(self):
        """Test if WHEP endpoint is accessible"""
        try:
            logger.info("Testing WHEP endpoint accessibility...")
            
            # Try a simple GET request first
            response = requests.get(self.whep_url, timeout=10)
            logger.info(f"GET response status: {response.status_code}")
            
            # Try OPTIONS request to check supported methods
            options_response = requests.options(self.whep_url, timeout=10)
            logger.info(f"OPTIONS response status: {options_response.status_code}")
            logger.info(f"Allowed methods: {options_response.headers.get('Allow', 'Not specified')}")
            
            return True
            
        except requests.RequestException as e:
            logger.error(f"WHEP endpoint test failed: {e}")
            return False

    def create_simple_sdp_offer(self):
        """Create a simple SDP offer for WHEP"""
        sdp_offer = """v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
t=0 0
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:4ZcD
a=ice-pwd:2/1muCWoOi3Fx13XYYKzsqg
a=fingerprint:sha-256 75:74:5A:A6:A4:E5:52:F4:A7:67:4C:01:C7:EE:91:3F:21:3D:A2:E3:53:7B:6F:30:86:F2:30:FF:A6:22:D2:04
a=setup:actpass
a=mid:0
a=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level
a=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time
a=extmap:3 urn:ietf:params:rtp-hdrext:sdes:mid
a=extmap:4 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id
a=extmap:5 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id
a=sendrecv
a=rtcp-mux
a=rtpmap:96 VP8/90000
a=rtcp-fb:96 goog-remb
a=rtcp-fb:96 transport-cc
a=rtcp-fb:96 ccm fir
a=rtcp-fb:96 nack
a=rtcp-fb:96 nack pli"""
        return sdp_offer

    def try_whep_handshake(self):
        """Try WHEP handshake"""
        try:
            logger.info("Attempting WHEP handshake...")
            
            # Create SDP offer
            sdp_offer = self.create_simple_sdp_offer()
            
            # Send POST request with SDP offer
            headers = {
                'Content-Type': 'application/sdp',
                'Accept': 'application/sdp'
            }
            
            response = requests.post(
                self.whep_url,
                data=sdp_offer,
                headers=headers,
                timeout=10
            )
            
            logger.info(f"WHEP handshake response status: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 201:
                logger.info("WHEP handshake successful!")
                logger.info(f"SDP Answer: {response.text[:200]}...")
                return True, response.text, response.headers.get('Location')
            else:
                logger.error(f"WHEP handshake failed: {response.text}")
                return False, None, None
                
        except requests.RequestException as e:
            logger.error(f"WHEP handshake error: {e}")
            return False, None, None

    def record(self):
        """Main recording method"""
        try:
            logger.info("Starting WHEP video recording...")
            
            # Test endpoint accessibility
            if not self.test_whep_endpoint():
                logger.warning("WHEP endpoint test failed, but continuing...")
            
            # Try WHEP handshake
            success, sdp_answer, location = self.try_whep_handshake()
            if success:
                logger.info("WHEP handshake completed successfully")
            else:
                logger.warning("WHEP handshake failed, trying alternative methods...")
            
            # Try recording with FFmpeg
            if self.record_with_ffmpeg():
                logger.info(f"Recording saved to: {self.output_file}")
                return True
            else:
                logger.error("All recording methods failed")
                return False
                
        except Exception as e:
            logger.error(f"Recording failed: {e}")
            return False

def main():
    """Main function"""
    # WHEP endpoint URL
    whep_url = "https://issac.qj-robots.com/rtc/v1/whep/?app=live&stream=camera_recognition"
    
    # Create recorder instance
    recorder = SimpleWHEPRecorder(
        whep_url=whep_url,
        output_file="recordings/camera_recognition_10s.mp4",
        duration=10
    )
    
    try:
        # Start recording
        if recorder.record():
            print(f"✅ Recording completed successfully!")
            print(f"📁 Output file: {recorder.output_file}")
            
            # Check if file was created and has content
            if os.path.exists(recorder.output_file):
                file_size = os.path.getsize(recorder.output_file)
                print(f"📊 File size: {file_size} bytes")
                if file_size > 0:
                    print("🎉 Recording appears to have content!")
                else:
                    print("⚠️  Warning: Output file is empty")
            else:
                print("❌ Output file was not created")
                
            return 0
        else:
            print(f"❌ Recording failed")
            return 1
        
    except Exception as e:
        print(f"❌ Recording failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
