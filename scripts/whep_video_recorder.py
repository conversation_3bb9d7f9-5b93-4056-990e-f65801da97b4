#!/usr/bin/env python3
"""
WHEP Video Stream Recorder

This script subscribes to a WHEP video stream and records the first 10 seconds
to a local file.

Requirements:
- aiortc: For WebRTC support
- aiohttp: For HTTP requests
- opencv-python: For video processing
- asyncio: For async operations

Usage:
    python whep_video_recorder.py
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from pathlib import Path
import cv2
import numpy as np

from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from aiortc.contrib.media import MediaRecorder

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WHEPVideoRecorder:
    def __init__(self, whep_url: str, output_file: str = None, duration: int = 10):
        """
        Initialize WHEP Video Recorder
        
        Args:
            whep_url: WHEP endpoint URL
            output_file: Output video file path (default: auto-generated)
            duration: Recording duration in seconds (default: 10)
        """
        self.whep_url = whep_url
        self.duration = duration
        self.pc = RTCPeerConnection()
        self.session = None
        self.recorder = None
        
        # Generate output filename if not provided
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_file = f"whep_recording_{timestamp}.mp4"
        else:
            self.output_file = output_file
            
        # Ensure output directory exists
        Path(self.output_file).parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"WHEP URL: {self.whep_url}")
        logger.info(f"Output file: {self.output_file}")
        logger.info(f"Recording duration: {self.duration} seconds")

    async def create_offer(self):
        """Create WebRTC offer"""
        try:
            # Add transceiver for receiving video
            self.pc.addTransceiver("video", direction="recvonly")
            
            # Create offer
            offer = await self.pc.createOffer()
            await self.pc.setLocalDescription(offer)
            
            logger.info("Created WebRTC offer")
            return offer
        except Exception as e:
            logger.error(f"Failed to create offer: {e}")
            raise

    async def send_whep_request(self, offer):
        """Send WHEP request to establish connection"""
        try:
            headers = {
                'Content-Type': 'application/sdp',
                'Accept': 'application/sdp'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.whep_url,
                    data=offer.sdp,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        answer_sdp = await response.text()
                        location = response.headers.get('Location')
                        
                        logger.info(f"WHEP request successful, Location: {location}")
                        return answer_sdp, location
                    else:
                        error_text = await response.text()
                        raise Exception(f"WHEP request failed: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"WHEP request error: {e}")
            raise

    async def set_remote_description(self, answer_sdp):
        """Set remote description from WHEP response"""
        try:
            answer = RTCSessionDescription(sdp=answer_sdp, type="answer")
            await self.pc.setRemoteDescription(answer)
            logger.info("Set remote description successfully")
        except Exception as e:
            logger.error(f"Failed to set remote description: {e}")
            raise

    async def setup_recording(self):
        """Setup media recording"""
        try:
            # Create media recorder
            self.recorder = MediaRecorder(self.output_file)
            
            # Setup track handler
            @self.pc.on("track")
            def on_track(track):
                logger.info(f"Received track: {track.kind}")
                if track.kind == "video":
                    self.recorder.addTrack(track)
                    
            logger.info("Recording setup complete")
        except Exception as e:
            logger.error(f"Failed to setup recording: {e}")
            raise

    async def start_recording(self):
        """Start the recording process"""
        try:
            logger.info("Starting recording...")
            await self.recorder.start()
            
            # Record for specified duration
            await asyncio.sleep(self.duration)
            
            logger.info("Recording completed")
            await self.recorder.stop()
            
        except Exception as e:
            logger.error(f"Recording error: {e}")
            raise

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.recorder:
                await self.recorder.stop()
            await self.pc.close()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    async def record(self):
        """Main recording method"""
        try:
            logger.info("Starting WHEP video recording...")
            
            # Setup recording
            await self.setup_recording()
            
            # Create WebRTC offer
            offer = await self.create_offer()
            
            # Send WHEP request
            answer_sdp, location = await self.send_whep_request(offer)
            
            # Set remote description
            await self.set_remote_description(answer_sdp)
            
            # Start recording
            await self.start_recording()
            
            logger.info(f"Recording saved to: {self.output_file}")
            
        except Exception as e:
            logger.error(f"Recording failed: {e}")
            raise
        finally:
            await self.cleanup()

async def main():
    """Main function"""
    # WHEP endpoint URL
    whep_url = "https://issac.qj-robots.com/rtc/v1/whep/?app=live&stream=camera_recognition"
    
    # Create recorder instance
    recorder = WHEPVideoRecorder(
        whep_url=whep_url,
        output_file="recordings/camera_recognition_10s.mp4",
        duration=10
    )
    
    try:
        # Start recording
        await recorder.record()
        print(f"✅ Recording completed successfully!")
        print(f"📁 Output file: {recorder.output_file}")
        
    except Exception as e:
        print(f"❌ Recording failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    # Run the async main function
    exit_code = asyncio.run(main())
    exit(exit_code)
