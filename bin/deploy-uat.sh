#!/bin/bash

# git reset --hard
git commit -am "deploy uat"
git push


# rm -rf node_modules package-lock.json

# npm install

# npm i @rollup/rollup-linux-x64-gnu

# node --max-old-space-size=4096 $(which npm) ci

npm run build:prod

ssh -i /Users/<USER>/.ssh/liuzhihao root@82.157.75.43 "rm -rf /data/qj-open-platform-admin"

scp -i /Users/<USER>/.ssh/liuzhihao -r dist/ root@82.157.75.43:/data/qj-open-platform-admin

echo "deploy success"