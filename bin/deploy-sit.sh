#!/bin/bash

# git reset --hard

git pull -f

# rm -rf node_modules package-lock.json

# npm install

# npm i @rollup/rollup-linux-x64-gnu

# node --max-old-space-size=4096 $(which npm) ci

yarn build:prod

qj-sit "rm -rf /data/qj-open-platform-admin"

scp -i ~/.ssh/liuzhihao -r dist/ root@120.46.186.221:/data/qj-open-platform-admin

echo "deploy success"

exit 0

rm -rf /data/sd-lal-admin

mv dist /data/sd-lal-admin

echo "deploy success"