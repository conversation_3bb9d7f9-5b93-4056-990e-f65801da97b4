<script setup> import { NConfigProvider, NMessageProvider, NDialogProvider, NModalProvider } from 'naive-ui' import { onMounted, ref, provide } from 'vue' // 如果您需要使用 store，请使用 Pinia 的 useStore // import { useStore } from 'pinia' const isDebugMode = ref(false) // const store = useStore() // 如果您需要使用 store，请取消注释这行 // 提供 isDebugMode 给所有子组件 provide('isDebugMode', isDebugMode) </script> <template> <n-config-provider> <n-message-provider> <n-dialog-provider> <n-modal-provider> <router-view></router-view> </n-modal-provider> </n-dialog-provider> </n-message-provider> </n-config-provider> </template> <script> export default { name: 'App', // 组件逻辑可以在这里添加 } </script> <style> @font-face { font-family: 'CustomChinese'; src: url('/fe574996dbfee5d1e43c5927d23cfe4a.woff2') format('woff2'); font-display: swap; } @font-face { font-family: 'CustomEnglish'; src: url('/main.woff2') format('woff2'); font-display: swap; } :root { --font-chinese: 'CustomChinese'; --font-english: 'CustomEnglish'; --font-fallback: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; } html { font-family: var(--font-chinese), var(--font-english), var(--font-fallback); } body, #app, input, button, textarea, select { font-family: inherit; } /* 为英文内容设置字体 */ [lang="en"], [lang="en-US"] { font-family: var(--font-english), var(--font-chinese), var(--font-fallback); } /* 为中文内容设置字体 */ [lang="zh"], [lang="zh-CN"] { font-family: var(--font-chinese), var(--font-english), var(--font-fallback); } /* 其他样式保持不变 */ /* 为确保中文字符使用正确的字体 */ :lang(zh), :lang(zh-CN) { font-family: 'CustomChinese', 'CustomEnglish', sans-serif; } /* 重置默认的边距和填充 */ html, body { margin: 0; padding: 0; height: 100%; width: 100%; } /* 确保 #app 占满整个视口 */ #app { min-height: 100vh; width: 100%; } /* 调试模式样式 */ .debug-mode * { outline: 0.2px solid rgba(182, 174, 174, 0.5) !important; position: relative !important; } .debug-mode *::before { content: attr(class); position: absolute; top: -18px; left: 50%; transform: translateX(-50%); background-color: rgba(255, 255, 255, 0.8); padding: 0 5px; font-size: 12px; color: red; white-space: nowrap; z-index: 9999; } /* 修改消息样式 */ :deep(.n-message) { top: 24px !important; right: 24px !important; } /* 添加消息动画样式 */ :deep(.n-message-container) { pointer-events: none; } :deep(.n-message) { pointer-events: all; transition: all 0.3s ease-in-out; } :deep(.n-message-enter-from), :deep(.n-message-leave-to) { opacity: 0; transform: translateX(100%); } :deep(.n-message-enter-to), :deep(.n-message-leave-from) { opacity: 1; transform: translateX(0); } </style>import { createApp } from 'vue' import { createRouter, createWebHistory } from 'vue-router' import { createPinia } from 'pinia' import naive from 'naive-ui' import App from '@/App.vue' import IndexPage from '@/views/IndexPage.vue' import LoginPage from '@/views/system/LoginPage.vue' import NotFoundPage from '@/views/system/NotFoundPage.vue' import { useMainStore } from '@/stores/mainStore' import TasksPage from '@/views/tasks/TasksPage.vue' import TaskResultPage from '@/views/tasks/TaskResultPage.vue' import RegisterPage from '@/views/system/RegisterPage.vue' import JoinPage from '@/views/contacts/JoinPage.vue' import InvitePage from '@/views/contacts/InvitePage.vue' import AppDetail from '@/views/app/AppDetail.vue' const router = createRouter({ history: createWebHistory(), routes: [ { path: '/', component: IndexPage }, { path: '/login', component: LoginPage }, { path: '/register', component: RegisterPage }, { path: '/logout', component: LoginPage }, { path: '/tasks', component: TasksPage }, { path: '/join', component: JoinPage }, { path: '/invite', component: InvitePage }, { path: '/task', component: TaskResultPage }, { path: '/app/:id', name: 'AppDetail', component: AppDetail, props: true // 启用 props 传递路由参数 }, { path: '/:pathMatch(.*)*', component: NotFoundPage } ] }) // 定义白名单路由 const whiteList = ['/login', '/join','/invite','/register','/task'] // 添加 /join 到白名单 router.beforeEach(async (to, from, next) => { const token = localStorage.getItem('access_token') // 在白名单中的路由直接放行 if (whiteList.includes(to.path)) { next() return } if (token) { const mainStore = useMainStore() try { if (!mainStore.isUserLoggedIn) { await mainStore.checkLoginStatus() } if (to.path === '/' && !mainStore.menusLoaded) { await mainStore.fetchMenus() } next() } catch (error) { console.error('路由守卫错误:', error) localStorage.removeItem('token') next(`/login?redirect=${to.path}`) } } else { next(`/login?redirect=${to.path}`) } }) const app = createApp(App) const pinia = createPinia() app.use(pinia) app.use(router) app.use(naive) app.mount('#app')import { createApp } from 'vue' import { createRouter, createWebHistory } from 'vue-router' import naive from 'naive-ui' import App from '@/App.vue' import WecomLoginPage from '@/views/system/WecomLoginPage.vue' import LoginPage from '@/views/system/LoginPage.vue' import NotFoundPage from '@/views/system/NotFoundPage.vue' import { createPinia } from 'pinia' import MainPage from '@/views/MainPage.vue' const pinia = createPinia() const router = createRouter({ history: createWebHistory(), routes: [ { path: '/', component: MainPage }, { path: '/login', component: LoginPage }, { path: '/wecom', component: WecomLoginPage }, { path: '/:pathMatch(.*)*', component: NotFoundPage } ] }) const app = createApp(App) app.use(router) app.use(naive) app.use(pinia) app.mount('#app')import { doGet, doPost, doPut } from '@/utils/requests' export const applicationApi = { // 获取应用列表 async getApplications() { const response = await doGet('/app-center/app/info/list') // 转换后端数据格式为前端使用的格式 return response.data.map(app => ({ id: app.appId, name: app.appName, iconName: app.iconName, iconColor: app.iconColor, enabled: app.appState !== 'DISABLED', owner: app.ownerName, myRole: app.roleName, latestUpdate: app.versionDesc })) }, // 创建应用 async createApplication(application) { console.log('发送创建应用请求:', application) // 添加日志便于调试 const requestBody = { appName: application.name, appDesc: application.description || '', iconName: application.iconName, iconColor: application.iconColor } const response = await doPost('/app-center/app/info', requestBody) console.log('创建应用响应:', response) }, // 更新应用 async updateApplication(appId, data) { const requestBody = { appName: data.appName, appDesc: data.appDesc, iconName: data.iconName, iconColor: data.iconColor, docUrl: data.docUrl, appState: data.appState, ownerId: data.ownerId, ownerName: data.ownerName } await doPut(`/app-center/app/info/${appId}`, requestBody) // 不需要处理返回值，直接返回 }, // 删除应用 async deleteApplication(id) { // TODO: 实现真实的删除接口 return { success: true } }, // 获取应用详情 async getApplicationDetail(appId) { const response = await doGet(`/app-center/app/info/detail/${appId}`) return { id: response.data.id, appId: response.data.appCode, appName: response.data.appName, iconName: response.data.iconName, iconColor: response.data.iconColor, appState: response.data.appState, description: response.data.appDesc, appSecret: response.data.appSecret, createTime: response.data.createTime, updateTime: response.data.updateTime, latestReleaseVersion: response.data.latestReleaseVersion, owner: response.data.ownerName, ownerId: response.data.ownerId, tenantId: response.data.tenantId, docUrl: response.data.docUrl } }, async getAppCallbackConfig(appId) { const response = await doGet(`/app-center/app/callback/config/detail/${appId}`) if (response.data) { return { id: response.data.id || '', appId: response.data.appId || '', aesKey: response.data.aesKey || '', callbackUrl: response.data.callbackUrl || '' } } return { id: '', appId: '', aesKey: '', callbackUrl: '' } }, async updateAppCallbackConfig(data) { const requestBody = { id: data.id, aesKey: data.aesKey, callbackUrl: data.callbackUrl } await doPut(`/app-center/app/callback/config`, requestBody) // 不需要处理返回值，直接返回 }, // 添加验证回调配置的方法 validateCallback(data) { return doPost('/app-center/app/callback/config/validate', data) }, // 更新应用回调配置 updateAppCallbackConfig(data) { return doPut('/app-center/app/callback/config', data) } }import { doGet } from '@/utils/requests' /** * 获取应用上传参数 * @returns {Promise} 返回上传参数的请求Promise */ export function getUploadParams() { return doGet('/open-apis/base/upload/params') }import { doGet, doPost, doPut } from '@/utils/requests' export const contactsApi = { // 获取组织机构树 getOrganizations() { return doGet('/auth-center/system/department/list') }, // 获取部门成员 getMembers(departmentId, keyword = '', page = 1, size = 20) { return doGet('/auth-center/system/user/page', { department_id: departmentId, keyword, page, size }) }, // 批量更新成员状态 batchUpdateMemberStatus(userIds, status) { return doPut('/auth-center/system/user/status', { user_ids: userIds, status }) }, // 创建组织 createOrganization(data) { return doPost('/auth-center/system/department', { name: data.name, parentId: data.parentId || 0, type: data.type }) }, // 添加新成员 createMember(data) { return doPost('/auth-center/system/user', data) } }import request from '@/utils/requests'; export const menuApi = { getMenus() { return request.get('/system/menu'); }, createMenu(data) { return request.post('/system/menu', data); }, updateMenu(data) { return request.put(`/system/menu/${data.id}`, data); }, deleteMenus(ids) { return request.delete(`/system/menu?ids=${ids}`); } };import { doGet, doPost, doPut, doDelete } from '@/utils/requests' // 获取角色列表 export function getRoles() { return doGet('/system/role') } // 获取菜单列表 export function getMenus() { return doGet('/system/menus') } // 保存新角色 export function saveRole(data) { return doPost('/system/role', data) } // 更新角色 export function updateRole(data) { return doPut(`/system/role/${data.id}`, data) } // 删除角色 export function deleteRole(id) { return doDelete(`/system/role/${id}`) } // 获取角色详情 export function getRoleDetail(id) { return doGet(`/system/role/${id}`) }import { doGet, doPost, doPut } from '@/utils/requests' export function getDepartments(page = 1, size = 500) { return doGet('/system/department', { page, size }) } export function getDepartmentMembers(deptId, page = 1, size = 50) { return doGet(`/system/department/members/${deptId}`, { page, size }) } export function getRoles() { return doGet('/system/role') } export function getUserDetails(userId) { return doGet(`/system/user/${userId}`) } export function createUser(userData) { return doPost('/system/user', userData) } export function updateUser(userData) { return doPut('/system/user', userData) } // Add more API functions as needed<template> <div class="result-cards"> <!-- 添加重试按钮 --> <div v-if="result.taskStatus === 'TIMEOUT'" class="retry-container"> <n-alert type="warning" title="提示"> 获取结果超时 </n-alert> <n-button type="primary" @click="$emit('retry')" class="retry-button" > 重试 </n-button> </div> <!-- 原有的卡片内容 --> <div class="cards-container"> <n-card v-for="(label, index) in result.labels" :key="index" class="result-card" > <div class="card-content"> <!-- 物体名称和置信度 --> <div class="card-header"> <div class="object-name">{{ label }}</div> <n-tag v-if="result.scores && result.scores[index] !== undefined" :type="getScoreType(result.scores[index])" size="small" > 置信度: {{ result.scores[index] }} </n-tag> </div> <!-- 预览图片 --> <div class="image-container" v-if="imagesList?.[index]"> <img :src="imagesList[index]" :alt="label" class="preview-thumbnail" @click="showImagePreview(imagesList[index])" /> </div> <!-- 边界框信息 --> <div class="box-info" v-if="result.boxes && result.boxes[index]"> <div class="box-details"> <div class="box-item"> <span class="label">左上:</span> <span class="value"> ({{ result.boxes[index][0] }}, {{ result.boxes[index][1] }}) </span> </div> <div class="box-item"> <span class="label">右下:</span> <span class="value"> ({{ result.boxes[index][2] }}, {{ result.boxes[index][3] }}) </span> </div> <div class="box-item"> <span class="label">尺寸:</span> <span class="value"> {{ result.boxes[index][2] - result.boxes[index][0] }} × {{ result.boxes[index][3] - result.boxes[index][1] }} </span> </div> </div> </div> <!-- 角度信息 --> <div class="angle-info" v-if="result.angles?.[index]"> <div class="angle-details"> <!-- 角度值 --> <div class="angle-item"> <span class="label">角度:</span> <span class="value">{{ result.angles[index].angle }}°</span> </div> <!-- 点坐标 --> <div class="angle-points"> <span class="label">角点:</span> <div class="points-grid"> <!-- 左上角 --> <div class="point-item"> <span class="point-label">左上</span> <span>({{ result.angles[index].anglesPoint[0][0] }}, {{ result.angles[index].anglesPoint[0][1] }})</span> </div> <!-- 左下角 --> <div class="point-item"> <span class="point-label">左下</span> <span>({{ result.angles[index].anglesPoint[1][0] }}, {{ result.angles[index].anglesPoint[1][1] }})</span> </div> <!-- 右上角 --> <div class="point-item"> <span class="point-label">右上</span> <span>({{ result.angles[index].anglesPoint[2][0] }}, {{ result.angles[index].anglesPoint[2][1] }})</span> </div> <!-- 右下角 --> <div class="point-item"> <span class="point-label">右下</span> <span>({{ result.angles[index].anglesPoint[3][0] }}, {{ result.angles[index].anglesPoint[3][1] }})</span> </div> </div> </div> </div> </div> <!-- 抓取点信息 --> <div class="grasp-info" v-if="result.grasps?.[index]"> <div class="grasp-details"> <!-- 抓取角度 --> <div class="grasp-item"> <span class="label">抓取角度:</span> <span class="value">{{ result.grasps[index].graspAngle }}°</span> </div> <!-- 新增: 抓取深度 --> <div class="grasp-item" v-if="result.grasps[index].graspDepth !== undefined"> <span class="label">抓取深度:</span> <span class="value">{{ result.grasps[index].graspDepth }}</span> </div> <!-- 新增: 抓取高度 --> <div class="grasp-item" v-if="result.grasps[index].graspHeight !== undefined"> <span class="label">抓取高度:</span> <span class="value">{{ result.grasps[index].graspHeight }}</span> </div> <!-- 新增: 抓取宽度 --> <div class="grasp-item" v-if="result.grasps[index].graspWidth !== undefined"> <span class="label">抓取宽度:</span> <span class="value">{{ result.grasps[index].graspWidth }}</span> </div> <!-- 抓取点坐标 --> <div class="grasp-points"> <span class="label">抓取点:</span> <div class="points-grid"> <div v-for="(point, pIndex) in result.grasps[index].graspPoint" :key="pIndex" class="point-item" > <span class="point-label">点{{ pIndex + 1 }}</span> <span>{{ point[0] }}</span> </div> </div> </div> </div> </div> <!-- 问答列表 --> <div class="qa-list" v-if="result.answers?.[index]"> <div class="answer-content"> <div v-for="(value, key) in parseAnswer(result.answers[index])" :key="key" class="answer-item" > <span class="answer-key">{{ key }}:</span> <span class="answer-value">{{ value }}</span> </div> </div> </div> <!-- 关键点信息 --> <div class="point-info" v-if="result.points?.[index]"> <div class="point-details"> <!-- 关键点标签 --> <div class="point-labels"> <span class="label">关键点:</span> <div class="labels-list"> <div v-for="(labels, pIndex) in result.points[index].pointLabels" :key="pIndex" class="point-label-group" > <div v-for="(label, lIndex) in labels" :key="lIndex" class="point-label-item" > {{ label }} </div> </div> </div> </div> <!-- 关键点边界框 --> <div class="point-boxes"> <span class="label">关键点位置:</span> <div class="boxes-list"> <div v-for="(box, pIndex) in result.points[index].pointBoxes" :key="pIndex" class="point-box-item" > <div class="box-coordinates"> <div class="coordinate-item" data-label="左上:"> ({{ box[0] }}, {{ box[1] }}) </div> <div class="coordinate-item" data-label="右下:"> ({{ box[2] }}, {{ box[3] }}) </div> <div class="coordinate-item" data-label="尺寸:"> {{ box[2] - box[0] }} × {{ box[3] - box[1] }} </div> </div> </div> </div> </div> </div> </div> </div> </n-card> </div> <!-- 分割遮罩展示 --> <div class="masks-container" v-if="result.maskImage?.length"> <n-divider>分割遮罩</n-divider> <div class="masks-grid"> <div v-for="(maskImage, index) in result.maskImage" :key="index" class="mask-item" > <img :src="maskImage" alt="分割遮罩" class="mask-image" @click="showImagePreview(maskImage)" /> <n-button size="small" @click="downloadMaskData(result.maskData?.[index])" :loading="downloading" > 下载遮罩数据 </n-button> </div> </div> </div> <!-- 图片预览模态框 --> <n-modal v-model:show="showPreview" :mask-closable="true" transform-origin="center" class="preview-modal" > <div class="preview-container"> <img :src="previewImage" class="preview-image" @wheel.prevent="handleZoom" @mousedown="startDrag" @mousemove="onDrag" @mouseup="stopDrag" @mouseleave="stopDrag" :style="imageStyle" /> </div> </n-modal> </div> </template> <script setup> import { ref, computed, onMounted } from 'vue' import { NCard, NDivider, NButton, NModal, NTag, NCollapse, NCollapseItem } from 'naive-ui' import messages from '@/utils/messages' const props = defineProps({ result: { type: Object, required: true }, imagesList: { type: Array, default: () => [] } }) // 添加 emit 定义 const emit = defineEmits(['mounted', 'retry']) // 图片预览相关状态 const showPreview = ref(false) const previewImage = ref('') const scale = ref(1) const position = ref({ x: 0, y: 0 }) const isDragging = ref(false) const dragStart = ref({ x: 0, y: 0 }) // 下载状态 const downloading = ref(false) // 计算图片样式 const imageStyle = computed(() => ({ transform: `scale(${scale.value}) translate(${position.value.x}px, ${position.value.y}px)`, cursor: isDragging.value ? 'grabbing' : 'grab' })) // 显示图片预览 const showImagePreview = (url) => { previewImage.value = url showPreview.value = true // 重置缩放和位置 scale.value = 1 position.value = { x: 0, y: 0 } } // 处理缩放 const handleZoom = (e) => { const delta = e.deltaY > 0 ? -0.1 : 0.1 const newScale = scale.value + delta if (newScale >= 0.5 && newScale <= 3) { scale.value = newScale } } // 拖动相关方法 const startDrag = (e) => { isDragging.value = true dragStart.value = { x: e.clientX - position.value.x, y: e.clientY - position.value.y } } const onDrag = (e) => { if (isDragging.value) { position.value = { x: e.clientX - dragStart.value.x, y: e.clientY - dragStart.value.y } } } const stopDrag = () => { isDragging.value = false } // 优化后的下载遮罩数据方法 const downloadMaskData = async (url) => { if (downloading.value) return try { downloading.value = true const response = await fetch(url) const data = await response.json() // 使用更高效的序列化方式 const jsonString = JSON.stringify(data) const blob = new Blob([jsonString], { type: 'application/json' }) // 使用更简洁的下载方式 const downloadUrl = URL.createObjectURL(blob) const link = document.createElement('a') link.href = downloadUrl link.download = `mask_data_${Date.now()}.json` link.click() // 清理资源 URL.revokeObjectURL(downloadUrl) downloading.value = false } catch (error) { downloading.value = false messages.error('下载遮罩数据失败') console.error('下载遮罩数据失败:', error) } } // 根据置信度返回标签类型 const getScoreType = (score) => { if (score >= 0.9) return 'success' if (score >= 0.7) return 'warning' return 'error' } // 在组件挂载时打印结果数据，方便调试 onMounted(() => { // 打印完整的结果数据 console.log('ResultCards mounted with data:', { result: props.result, boxes: props.result.boxes, scores: props.result.scores, labels: props.result.labels, hasBoxes: props.result.boxes && props.result.boxes.length > 0, hasScores: props.result.scores && props.result.scores.length > 0, boxesType: props.result.boxes ? typeof props.result.boxes : 'undefined', scoresType: props.result.scores ? typeof props.result.scores : 'undefined' }) // 如果有数据但不显示，打印每个索引的数据 if (props.result.labels) { props.result.labels.forEach((label, index) => { console.log(`Data for index ${index}:`, { label, score: props.result.scores?.[index], box: props.result.boxes?.[index] }) }) } emit('mounted') }) // 修改判断是否为对象的方法 const isObject = (value) => { return value !== null && typeof value === 'object' && !Array.isArray(value) } // 修改解析 JSON 字符串的方法 const parseAnswer = (answer) => { if (typeof answer === 'string') { try { return JSON.parse(answer) } catch (e) { console.error('JSON parse error:', e) return {} } } return answer || {} } </script> <style scoped> .result-cards { width: 100%; } .cards-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(450px, 1fr)); gap: 16px; margin-bottom: 24px; } .result-card { height: 100%; } .card-content { display: flex; flex-direction: column; gap: 12px; height: 100%; } .object-name { font-size: 16px; font-weight: 500; color: #333; text-align: center; } .image-container { width: 100%; height: 120px; /* 固定高度 */ overflow: hidden; border-radius: 8px; background: #f5f7fa; display: flex; /* 添加flex布局 */ justify-content: center; /* 水平居中 */ align-items: center; /* 垂直居中 */ } .cropped-image { width: 100%; height: 100%; object-fit: cover; } .qa-result { background: #f8f9fa; padding: 12px; border-radius: 8px; } .question { font-weight: 500; color: #666; margin-bottom: 8px; } .answer { color: #333; } .masks-container { margin-top: 24px; } .masks-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 16px; } .mask-item { display: flex; flex-direction: column; align-items: center; gap: 8px; } .mask-image { width: 100%; aspect-ratio: 1; object-fit: contain; background: #f5f7fa; border-radius: 8px; } .preview-container { width: 90vw; height: 90vh; display: flex; justify-content: center; align-items: center; overflow: hidden; background: #000; } .preview-image { max-width: 100%; max-height: 100%; object-fit: contain; transition: transform 0.1s ease; user-select: none; } .cropped-image, .mask-image { cursor: pointer; transition: transform 0.2s ease; } .cropped-image:hover, .mask-image:hover { transform: scale(1.05); } .card-header { display: flex; justify-content: space-between; align-items: center; gap: 8px; } .box-info { background: #f8f9fa; border-radius: 8px; padding: 12px; margin-top: 12px; } .box-details { display: flex; flex-direction: column; gap: 8px; } .box-item { display: flex; justify-content: space-between; align-items: center; font-size: 14px; } .box-item .label { color: #666; font-weight: 500; } .box-item .value { color: #333; font-family: monospace; } /* 确保卡片内容垂直对齐 */ .card-content { display: flex; flex-direction: column; gap: 12px; height: 100%; } /* 优化折叠面板样式 */ :deep(.n-collapse) { background: transparent; border: none; } :deep(.n-collapse-item__header) { padding: 8px 0; } :deep(.n-collapse-item__content-inner) { padding: 8px 16px; background: white; border-radius: 4px; } .preview-thumbnail { width: 100%; /* 宽度100% */ height: 100%; /* 高度100% */ object-fit: contain; /* 保持图片比例 */ background: #f5f7fa; cursor: pointer; transition: transform 0.2s ease; } .preview-thumbnail:hover { transform: scale(1.02); } /* 修改预览模态框样式 */ :deep(.preview-modal) { background: none !important; } .preview-container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.9); display: flex; justify-content: center; align-items: center; z-index: 1000; } .preview-image { max-width: 90vw; max-height: 90vh; object-fit: contain; transition: transform 0.1s ease; user-select: none; } /* 添加问答列表样式 */ .qa-list { margin-top: 12px; background: #f8f9fa; border-radius: 8px; padding: 12px; width: 100%; } .answer-content { display: flex; flex-direction: column; gap: 8px; width: 100%; } .answer-item { display: flex; align-items: center; justify-content: space-between; gap: 12px; width: 100%; } .answer-key { color: #666; font-weight: 500; white-space: nowrap; } .answer-value { color: #18a058; text-align: right; word-break: break-word; } /* 添加角度信息样式 */ .angle-info { background: #f8f9fa; border-radius: 8px; padding: 12px; margin-top: 12px; } .angle-details { display: flex; flex-direction: column; gap: 8px; } .angle-item { display: flex; justify-content: space-between; align-items: center; font-size: 14px; } .angle-points { display: flex; flex-direction: column; gap: 4px; } .points-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; margin-top: 4px; min-width: 400px; } .point-item { display: flex; flex-direction: column; align-items: center; gap: 2px; font-family: monospace; font-size: 12px; color: #666; background: #fff; padding: 4px 6px; border-radius: 4px; min-width: 190px; } .point-label { color: #666; font-weight: 500; font-size: 11px; } .label { color: #666; font-weight: 500; } .value { color: #333; font-family: monospace; } /* 添加抓取点信息样式 */ .grasp-info { background: #f8f9fa; border-radius: 8px; padding: 12px; margin-top: 12px; } .grasp-details { display: flex; flex-direction: column; gap: 8px; } .grasp-item { display: flex; justify-content: space-between; align-items: center; font-size: 14px; } .grasp-points { display: flex; flex-direction: column; gap: 4px; } /* 添加关键点信息样式 */ .point-info { background: #f8f9fa; border-radius: 8px; padding: 12px; margin-top: 12px; } .point-details { display: flex; flex-direction: column; gap: 12px; } .point-labels, .point-boxes { display: flex; flex-direction: column; gap: 8px; } .labels-list, .boxes-list { display: flex; flex-direction: column; gap: 8px; } .point-label-group { display: flex; flex-wrap: wrap; gap: 8px; } .point-label-item { background: #e8f5e9; color: #18a058; padding: 4px 8px; border-radius: 4px; font-size: 12px; } .point-box-item { background: white; padding: 8px 12px; border-radius: 4px; font-family: monospace; font-size: 12px; width: 100%; box-sizing: border-box; } .box-coordinates { display: flex; flex-direction: column; gap: 4px; width: 100%; box-sizing: border-box; } .coordinate-item { display: flex; justify-content: space-between; align-items: center; color: #333; padding: 2px 0; width: 100%; box-sizing: border-box; background: white; } .coordinate-item::before { content: attr(data-label); color: #666; font-weight: 500; } /* 移除不需要的样式 */ .box-size { display: none; } .point-boxes { width: 100%; box-sizing: border-box; } .boxes-list { width: 100%; box-sizing: border-box; } .retry-container { display: flex; flex-direction: column; align-items: center; gap: 16px; margin-bottom: 24px; padding: 24px; background: #fff; border-radius: 8px; } .retry-button { min-width: 120px; } </style><template> <pre class="result-code"><code>{{ displayText }}</code></pre> </template> <script setup> import { ref, watch, onUnmounted } from 'vue' const props = defineProps({ text: { type: String, required: true } }) const displayText = ref('') let currentIndex = 0 let typingTimer = null const typeText = () => { if (currentIndex < props.text.length) { displayText.value += props.text[currentIndex] currentIndex++ typingTimer = setTimeout(typeText, 10) } } watch(() => props.text, (newText) => { // 清理之前的定时器 if (typingTimer) { clearTimeout(typingTimer) } // 重置状态 currentIndex = 0 displayText.value = '' // 开始新的打字效果 if (newText) { typeText() } }, { immediate: true }) // 组件卸载时清理 onUnmounted(() => { if (typingTimer) { clearTimeout(typingTimer) } }) </script> <style scoped> .result-code { background: #f5f7fa; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: monospace; font-size: 14px; line-height: 1.5; margin: 0; white-space: pre-wrap; } </style>import { createRouter, createWebHistory } from 'vue-router' import IndexPage from '@/views/IndexPage.vue' import LoginPage from '@/views/LoginPage.vue' import AppDetail from '@/views/app/AppDetail.vue' const router = createRouter({ history: createWebHistory(import.meta.env.BASE_URL), routes: [ { path: '/', component: IndexPage }, { path: '/login', component: LoginPage }, { path: '/join', name: 'Join', component: () => import('@/views/contacts/InvitePage.vue') }, { path: '/app/:id', name: 'AppDetail', component: AppDetail, props: true } ] }) export default routerimport { defineStore } from 'pinia' import { doGet, doPost } from '@/utils/requests' import { h } from 'vue' import { NIcon } from 'naive-ui' import { SpeedometerOutline, PersonOutline, BuildOutline, MenuOutline, WalletOutline, ChatbubbleOutline, StarOutline, HeadsetOutline, SearchOutline, SyncOutline, BookOutline, HelpCircleOutline, BriefcaseOutline, FilterOutline } from '@vicons/ionicons5' const iconMap = { 'Odometer': SpeedometerOutline, 'UserFilled': PersonOutline, 'Tools': BuildOutline, 'Menu': MenuOutline, 'Money': WalletOutline, 'Message': ChatbubbleOutline, 'StarFilled': StarOutline, 'Service': HeadsetOutline, 'Search': SearchOutline, 'Refresh': SyncOutline, 'Reading': BookOutline, 'HelpFilled': HelpCircleOutline, 'Suitcase': BriefcaseOutline, 'Filter': FilterOutline } function renderIcon(icon) { return () => { const IconComponent = iconMap[icon] || HelpCircleOutline return h(NIcon, null, { default: () => h(IconComponent) }) } } export const useMainStore = defineStore('main', { state: () => ({ menus: [], user: null, isLoggedIn: false, menusLoaded: false, menusFetching: false, }), actions: { async fetchMenus() { this.menusFetching = true try { const response = await doGet('/auth-center/system/menus') if (response.code === 200 && Array.isArray(response.data)) { this.menus = this.buildMenuTree(response.data) this.menusLoaded = true } else { throw new Error('Invalid menu data') } } catch (error) { // 如果获取失败，使用默认菜单 this.menus = this.getDefaultMenus() } finally { this.menusFetching = false } }, buildMenuTree(menuList) { const menuMap = new Map() const rootMenus = [] // 第一遍遍历，创建所有菜单项 menuList.forEach(menu => { menuMap.set(menu.id, { label: menu.menuLabel, key: menu.menuPath || String(menu.id), icon: menu.menuIcon ? renderIcon(menu.menuIcon) : null, viewPath: menu.viewPath, // 确保这里的 viewPath 是正确的 children: [] }) }) // 第二遍遍历，构建树结构 menuList.forEach(menu => { const menuItem = menuMap.get(menu.id) if (menu.parentId && menu.parentId !== 1) { const parentMenu = menuMap.get(menu.parentId) if (parentMenu) { parentMenu.children.push(menuItem) } } else { rootMenus.push(menuItem) } }) // 移除没有子菜单的 children 属性 const cleanupEmptyChildren = (menus) => { return menus.map(menu => { if (menu.children.length === 0) { const { children, ...rest } = menu return rest } return { ...menu, children: cleanupEmptyChildren(menu.children) } }) } return cleanupEmptyChildren(rootMenus) }, setUser(user) { this.user = user this.isLoggedIn = !!user if (user) { localStorage.setItem('user', JSON.stringify(user)) } else { localStorage.removeItem('user') } }, async logout() { try { await doPost('/auth-center/system/logout') } catch (error) { console.error('登出失败:', error) } finally { this.user = null this.isLoggedIn = false this.menusLoaded = false this.menus = [] localStorage.removeItem('access_token') // 不再需要清除 localStorage 中的菜单数据 } }, async checkLoginStatus() { const storedUser = localStorage.getItem('user') if (storedUser) { this.setUser(JSON.parse(storedUser)) return true } try { const response = await doGet('/system/user') if (response.data) { this.setUser(response.data) return true } } catch (error) { console.error('获取用户信息失败:', error) } this.logout() return false }, getDefaultMenus() { return [ { label: '我的待办', key: '/tasks', viewPath: 'tasks/TasksPage' // 确保这里的文件名正确 }, ] } }, getters: { getMenus: (state) => state.menus, getUser: (state) => state.user, isUserLoggedIn: (state) => state.isLoggedIn, } })import { defineStore } from 'pinia' export const useWorkflowStore = defineStore('workflow', { state: () => ({ workflows: [ { id: 1, name: '新客户开发流程', description: '针对新客户的销售流程', group: '销售', enabled: true }, { id: 2, name: '老客户维护流程', description: '针对老客户的维护流程', group: '销售', enabled: true }, { id: 3, name: '客户投诉处理流程', description: '处理客户投诉的标准流程', group: '客户服务', enabled: true }, { id: 4, name: '产品退换货流程', description: '处理产品退换货的流程', group: '客户服务', enabled: false }, { id: 5, name: '新员工入职流程', description: '新员工入职的标准流程', group: '人力资源', enabled: true }, { id: 6, name: '员工绩效评估流程', description: '定期进行的员工绩效评估流程', group: '人力资源', enabled: true }, { id: 7, name: '供应商筛选流程', description: '筛选新供应商的标准流程', group: '采购', enabled: true }, { id: 8, name: '采购审批流程', description: '大额采购的审批流程', group: '采购', enabled: true }, { id: 9, name: '新产品开发流程', description: '新产品从构思到上市的开发流程', group: '研发', enabled: true }, { id: 10, name: '质量控制流程', description: '产品质量控制的标准流程', group: '生产', enabled: true }, ] }), actions: { fetchWorkflows() { // 由于数据已经在 state 中，我们不需要异步操作 // 但为了保持接口一致，我们返回一个 resolved promise return Promise.resolve(this.workflows) }, addWorkflow(workflow) { const newId = Math.max(...this.workflows.map(w => w.id)) + 1 this.workflows.push({ ...workflow, id: newId }) }, updateWorkflow(updatedWorkflow) { const index = this.workflows.findIndex(w => w.id === updatedWorkflow.id) if (index !== -1) { this.workflows[index] = updatedWorkflow } }, deleteWorkflow(id) { this.workflows = this.workflows.filter(w => w.id !== id) } } })import mitt from 'mitt' const eventBus = mitt() export default eventBusimport { createDiscreteApi } from 'naive-ui' import { h } from 'vue' import { NAlert } from 'naive-ui' // 定义不同类型消息的标题 const MESSAGE_TITLES = { success: '成功', info: '信息', warning: '警告', error: '错误' } // 统一的消息渲染函数 const renderMessage = (props) => { const { type } = props return h( NAlert, { closable: props.closable, onClose: props.onClose, type: type === 'loading' ? 'default' : type, title: MESSAGE_TITLES[type] || '系统提示', style: { boxShadow: 'var(--n-box-shadow)', maxWidth: 'calc(100vw - 32px)', minWidth: '250px', width: 'fit-content' } }, { default: () => props.content } ) } // 创建一个全局的消息 API const { message } = createDiscreteApi(['message'], { configProviderProps: { theme: null, themeOverrides: { Message: { padding: '12px 20px', maxWidth: '420px', // 为每种类型设置对应的背景色和文字颜色 successColorSuppl: 'rgba(63, 195, 128, 0.1)', successTextColor: '#18a058', infoColorSuppl: 'rgba(24, 160, 245, 0.1)', infoTextColor: '#2080f0', warningColorSuppl: 'rgba(250, 173, 20, 0.1)', warningTextColor: '#f0a020', errorColorSuppl: 'rgba(255, 0, 0, 0.1)', errorTextColor: '#ff0000' } } }, messageProviderProps: { placement: 'top-right', duration: 3000, max: 3 } }) // 统一的消息配置 const defaultOptions = { closable: false, duration: 3000 } export default { success(content, options = {}) { message.success(content, { render: renderMessage, ...defaultOptions, ...options }) }, info(content, options = {}) { message.info(content, { render: renderMessage, ...defaultOptions, ...options }) }, warning(content, options = {}) { message.warning(content, { render: renderMessage, ...defaultOptions, ...options }) }, error(content, options = {}) { message.error(content, { render: renderMessage, ...defaultOptions, ...options }) } }import axios from 'axios' import message from '@/utils/messages' import { createDiscreteApi } from 'naive-ui' import eventBus from '@/utils/eventBus' const { loadingBar } = createDiscreteApi(['loadingBar']) const instance = axios.create({ baseURL: '/open-api', timeout: 10000, }) // 请求拦截器 instance.interceptors.request.use( (config) => { loadingBar.start() // 开始加载进度条 const token = localStorage.getItem('access_token') if (token) { config.headers['Authorization'] = `${token}` } return config }, (error) => { loadingBar.error() // 加载出错 message.error('请求配置错误') return Promise.reject(error) } ) // 响应拦截器 instance.interceptors.response.use( (response) => { loadingBar.finish() // 完成加载进度条 if (response.data.code !== 0) { message.error(response.data.message || '请求失败') return Promise.reject(new Error(response.data.message || '网络错误-请稍后重试')) } // 返回包含 headers 的响应数据 return { code: response.data.code, data: response.data.data, message: response.data.message, headers: response.headers } }, (error) => { loadingBar.error() // 加载出错 if (axios.isCancel(error)) { message.info('请求已取消') } else if (error.response) { switch (error.response.status) { case 400: message.error('用户输入错误') break case 401: message.error(error.response.data.message || '授权已过期，请重新登录') localStorage.removeItem('access_token') eventBus.emit('auth:logout') // 触发登出事件 break case 403: message.error('拒绝访问') break case 404: message.error('请求的资源不存在') break case 500: message.error('服务器内部错误') break case 503: message.error('服务暂时不可用，请稍后再试') break default: message.error(`连接错误 ${error.response.status}`) } } else if (error.request) { message.error('网络错误，请检查您的网络连接') } else { message.error('发生未知错误，请稍后重试') } return Promise.reject(error) } ) export const doGet = (url, params) => instance.get(url, { params }) export const doPost = (url, data) => instance.post(url, data) export const doPut = (url, data) => instance.put(url, data) export const doDelete = (url) => instance.delete(url) export default instance<script setup> import { NLayout, NLayoutHeader, NLayoutContent, NImage, NDropdown, NSpace, NIcon, NPopover } from 'naive-ui' import { PersonCircleOutline } from '@vicons/ionicons5' import { ApiApp } from '@vicons/tabler' import { AppSwitcher, UserMultiple,Money as AttachMoneyOutlined } from '@vicons/carbon' import { SettingsOutline } from '@vicons/ionicons5' import { useRouter } from 'vue-router' import { useMainStore } from '@/stores/mainStore' import logoImage from '@/assets/images/qj-logo-Dgc-aGJV.png' import { onMounted, computed, ref, markRaw } from 'vue' import AppCenter from '@/views/app/AppCenter.vue' import ContactsPage from '@/views/contacts/ContactsPage.vue' const router = useRouter() const mainStore = useMainStore() // 添加当前选中的菜单状态 const currentMenu = ref('app-center') // 默认选中通讯录 // 登录状态检查 onMounted(async () => { if (!mainStore.isUserLoggedIn) { const isLoggedIn = await mainStore.checkLoginStatus() if (!isLoggedIn) { router.push('/login') return } } }) // 用户菜单选项 const userMenuOptions = [ { label: '重置密码', key: 'reset-password' }, { label: '退出系统', key: 'logout' } ] // 用户菜单处理 const handleUserMenuSelect = (key) => { if (key === 'reset-password') { console.log('重置密码') } else if (key === 'logout') { mainStore.logout() router.push('/login') } } // 问候语计算 const greeting = computed(() => { const hour = new Date().getHours() if (hour >= 21 || hour < 6) return '您辛苦' if (hour >= 18) return '晚上好' if (hour >= 13) return '下午好' if (hour >= 11) return '中午好' if (hour >= 9) return '上午好' return '早上好' }) // 应用切换菜单选项 const appSwitchOptions = [ { label: '应用中心', icon: markRaw(ApiApp), key: 'app-center', color: '#1677ff' }, { label: '通讯录', icon: markRaw(UserMultiple), key: 'contacts', color: '#52c41a' }, { label: '财务中心', icon: markRaw(AttachMoneyOutlined), key: 'finance', color: '#722ed1' }, { label: '基础设置', icon: markRaw(SettingsOutline), key: 'settings', color: '#fa8c16' } ] // 当前显示的组件 const currentComponent = ref('app-center') // 修改应用切换菜单控制 const showAppMenu = ref(false) const handleAppSwitchSelect = (key) => { showAppMenu.value = false currentMenu.value = key currentComponent.value = key console.log('切换到:', key) } // 在组件挂载时设置默认显示的组件 onMounted(() => { currentComponent.value = 'app-center' currentMenu.value = 'app-center' }) // 组件映射表 const componentMap = { 'app-center': markRaw(AppCenter), 'contacts': markRaw(ContactsPage), 'finance': null, // 待实现 'settings': null // 待实现 } </script> <template> <n-layout position="absolute"> <n-layout-header class="header"> <div class="logo-container"> <n-image :src="logoImage" width="380" preview-disabled /> </div> <div class="user-info"> <n-space align="center" :size="20"> <div class="app-menu-wrapper"> <n-popover trigger="click" placement="bottom" :show="showAppMenu" @update:show="showAppMenu = $event" > <template #trigger> <div class="app-switch-trigger"> <n-icon size="24" class="app-switch-icon"> <AppSwitcher /> </n-icon> </div> </template> <div class="app-menu-grid"> <div v-for="option in appSwitchOptions" :key="option.key" class="app-menu-item" :class="{ 'app-menu-item-active': currentMenu === option.key }" @click="handleAppSwitchSelect(option.key)" > <n-icon size="24" :color="option.color"> <component :is="option.icon" /> </n-icon> <span>{{ option.label }}</span> </div> </div> </n-popover> </div> <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect"> <n-space align="center" class="user-dropdown-trigger"> <n-icon size="30" :component="PersonCircleOutline" /> <span>{{ mainStore.user?.nickname || '用户' }},</span> <span>{{ greeting }} </span> </n-space> </n-dropdown> </n-space> </div> </n-layout-header> <n-layout-content content-style="padding: 14px;" class="main-content"> <div class="content-wrapper"> <component :is="componentMap[currentComponent]" v-if="componentMap[currentComponent]" /> <div v-else class="placeholder"> <n-empty description="功能开发中..." /> </div> </div> </n-layout-content> </n-layout> </template> <style scoped> .header { display: flex; justify-content: space-between; align-items: center; height: 64px; padding: 0 24px; background-color: #fff; border-bottom: 1px solid #e8e8e8; } .logo-container { margin-top: 12px; } .main-content { height: calc(100vh - 64px); background-color: #f5f5f5; overflow: hidden; position: relative; flex: 1; display: flex; flex-direction: column; } :deep(.content-wrapper) { position: absolute; top: 14px; left: 14px; right: 14px; bottom: 14px; overflow-y: auto; display: flex; flex-direction: column; flex: 1; } /* Webkit 浏览器的滚动条样式 */ :deep(.content-wrapper::-webkit-scrollbar) { width: 6px; background-color: transparent; } :deep(.content-wrapper::-webkit-scrollbar-thumb) { background-color: transparent; border-radius: 3px; transition: background-color 0.3s; } /* 鼠标悬停或滚动时显示滚动条 */ :deep(.content-wrapper:hover::-webkit-scrollbar-thumb), :deep(.content-wrapper:active::-webkit-scrollbar-thumb) { background-color: #d9d9d9; } /* 滚动条轨道 */ :deep(.content-wrapper::-webkit-scrollbar-track) { background-color: transparent; } .user-info { display: flex; align-items: center; } .user-dropdown-trigger { cursor: pointer; padding: 4px 8px; border-radius: 4px; transition: background-color 0.3s; } .user-dropdown-trigger:hover { background-color: rgba(0, 0, 0, 0.06); } .app-menu-wrapper { position: relative; } :deep(.n-popover) { padding: 0 !important; border-radius: 12px !important; } :deep(.n-popover-content-wrapper) { transform-origin: top center !important; } .app-menu-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px; padding: 8px; width: 240px; } .app-menu-item { position: relative; z-index: 1000; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 16px 8px; border-radius: 8px; cursor: pointer; transition: all 0.3s; background: transparent; } .app-menu-item:hover { background-color: rgba(0, 0, 0, 0.04); } .app-menu-item .n-icon { margin-bottom: 8px; font-size: 24px; transition: transform 0.3s; } .app-menu-item:hover .n-icon { transform: scale(1.1); } .app-menu-item span { font-size: 12px; color: #333; text-align: center; white-space: nowrap; margin-top: 4px; } .app-switch-trigger { display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 4px; border-radius: 4px; transition: background-color 0.3s; } .app-switch-trigger:hover { background-color: rgba(0, 0, 0, 0.06); } .app-switch-icon { transition: transform 0.3s; } .app-switch-icon.active { transform: rotate(180deg); } .placeholder { height: 100%; display: flex; align-items: center; justify-content: center; background: #fff; border-radius: 8px; } </style><script setup> import { NLayout, NLayoutSider, NMenu, NLayoutHeader, NLayoutContent, NImage, NDropdown, NSpace, NIcon, NBreadcrumb, NBreadcrumbItem } from 'naive-ui' import { PersonCircleOutline } from '@vicons/ionicons5' import { useRouter, useRoute } from 'vue-router' import { useMainStore } from '@/stores/mainStore' import logoImage from '@/assets/images/qj-logo-Dgc-aGJV.png' // 导入图片 import { onMounted, computed, ref, watch, markRaw } from 'vue' import UsersPage from './system/UsersPage.vue' // 使用 markRaw 标记 UsersPage 组件 const UsersPageComponent = markRaw(UsersPage) const modules = import.meta.glob('@/views/**/*.vue', { eager: true }) const router = useRouter() const route = useRoute() const mainStore = useMainStore() const activeKey = ref(null) const breadcrumbs = ref([]) const currentComponent = ref(null) onMounted(async () => { if (!mainStore.isUserLoggedIn) { const isLoggedIn = await mainStore.checkLoginStatus() if (!isLoggedIn) { router.push('/login') return } } // 每次都重新获取菜单数据 // await mainStore.fetchMenus() console.log('Loaded menus:', mainStore.getMenus) // 设置默认路由为 /tasks，但只在工作区中加载 const defaultPath = '/tasks' activeKey.value = defaultPath updateBreadcrumbs(defaultPath) await loadComponent(defaultPath) }) const handleMenuClick = async (key) => { console.log('Menu clicked:', key) activeKey.value = key updateBreadcrumbs(key) await loadComponent(key) } const updateBreadcrumbs = (path) => { const matchedMenu = findMenuByPath(mainStore.getMenus, path) if (matchedMenu) { breadcrumbs.value = [{ label: '首页', key: '/' }, ...matchedMenu.breadcrumb] } else { breadcrumbs.value = [{ label: '首页', key: '/' }] } } const findMenuByPath = (menus, path) => { console.log('Searching for path:', path, 'in menus:', menus) for (const menu of menus) { console.log('Checking menu:', menu) if (menu.key === path) { console.log('Menu found:', menu) return { ...menu, breadcrumb: [{ label: menu.label, key: menu.key }] } } if (menu.children) { const found = findMenuByPath(menu.children, path) if (found) { console.log('Menu found in children:', found) return { ...found, breadcrumb: [{ label: menu.label, key: menu.key }, ...found.breadcrumb] } } } } console.log('Menu not found for path:', path) return null } const loadComponent = async (path) => { console.log('Loading component for path:', path) console.log('Current menus:', mainStore.getMenus) const matchedMenu = findMenuByPath(mainStore.getMenus, path) console.log('Matched menu:', matchedMenu) if (matchedMenu && matchedMenu.viewPath) { try { if (matchedMenu.viewPath === 'system/UsersPage') { currentComponent.value = UsersPageComponent // 使用标记过的组件 } else { // 原有的动态导入逻辑 const modulePath = `@/views/${matchedMenu.viewPath}.vue` console.log('Attempting to import:', modulePath) console.log('Available modules:', Object.keys(modules)) // 尝试不同的路径格式 const possiblePaths = [ modulePath, modulePath.replace('@/', '/src/'), `/src/views/${matchedMenu.viewPath}.vue`, `./src/views/${matchedMenu.viewPath}.vue` ] let module for (const path of possiblePaths) { if (modules[path]) { module = modules[path] break } } if (module) { console.log('Module found:', module) currentComponent.value = markRaw(module.default) console.log('Current component set:', currentComponent.value) } else { throw new Error(`Module not found: ${modulePath}`) } } } catch (error) { console.error('Failed to load component:', error) currentComponent.value = null } } else { console.log('No matching menu or viewPath found') currentComponent.value = null } } watch(() => route.path, async (newPath) => { updateBreadcrumbs(newPath) await loadComponent(newPath) }) const userMenuOptions = [ { label: '重置密码', key: 'reset-password' }, { label: '退出系统', key: 'logout' } ] const handleUserMenuSelect = (key) => { if (key === 'reset-password') { console.log('重置密码') } else if (key === 'logout') { mainStore.logout() router.push('/login') } } const greeting = computed(() => { const hour = new Date().getHours() if (hour >= 21 || hour < 6) return '您辛苦' if (hour >= 18) return '晚上好' if (hour >= 13) return '下午好' if (hour >= 11) return '中午好' if (hour >= 9) return '上午好' return '早上好' }) </script> <template> <n-layout position="absolute"> <n-layout-header class="header"> <div class="logo-container"> <n-image :src="logoImage" width="380" preview-disabled /> </div> <div class="user-info"> <n-space align="center"> <span>{{ greeting }}，</span> <span>{{ mainStore.user?.nickname || '用户' }}</span> <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect"> <n-icon size="30" :component="PersonCircleOutline" /> </n-dropdown> </n-space> </div> </n-layout-header> <n-layout has-sider position="absolute" style="top: 64px;"> <n-layout-sider bordered content-style="padding: 14px;"> <n-menu :options="mainStore.getMenus" @update:value="handleMenuClick" :value="activeKey" :indent="24" :collapsed-icon-size="22" :collapsed-width="64" :inverted="true" /> </n-layout-sider> <n-layout-content> <div class="content-wrapper"> <n-breadcrumb> <n-breadcrumb-item v-for="item in breadcrumbs" :key="item.key"> {{ item.label }} </n-breadcrumb-item> </n-breadcrumb> <div class="router-view-container"> <component :is="currentComponent" v-if="currentComponent" /> <div v-else>未找到对应的组件: {{ activeKey }}</div> </div> </div> </n-layout-content> </n-layout> </n-layout> </template> <style scoped> .header { display: flex; justify-content: space-between; align-items: center; height: 64px; padding: 0 24px; background-color: #fff; border-bottom: 1px solid #e8e8e8; } .logo-container { margin-top: 12px; } .user-info { display: flex; align-items: center; } .n-layout-sider { background-color: #08223a; } .n-layout-content { background-color: #f0f2f5; } :deep(.n-menu-item) { color: rgba(255, 255, 255, 0.65); } :deep(.n-menu-item:hover) { color: #fff; background-color: #1890ff; } :deep(.n-menu-item-content--selected) { color: #fff; background-color: #1890ff; } /* 移除父菜单的选中效果 */ :deep(.n-menu-item-content--child-active) { color: rgba(255, 255, 255, 0.65); background-color: transparent; } /* 移除默认的绿背景 */ :deep(.n-menu-item-content::before) { background-color: transparent !important; } :deep(.n-menu-item-content--selected::before), :deep(.n-menu-item-content--child-active::before) { background-color: transparent !important; } .content-wrapper { padding: 14px; } .router-view-container { margin-top: 14px; } :deep(.n-breadcrumb) { margin-bottom: 14px; } </style><script setup> import { NCard, NIcon, NTag, NDivider } from 'naive-ui' import { AccessibilitySharp, LogoChrome, ServerOutline, CloudDoneOutline, AnalyticsOutline, BuildOutline, BusinessOutline, CartOutline, DocumentTextOutline, FileTrayFullOutline, GridOutline, LogoAmplify, LogoApple, LogoDesignernews, LogoEdge, LogoElectron } from '@vicons/ionicons5' import { markRaw } from 'vue' const props = defineProps({ app: { type: Object, required: true } }) const emit = defineEmits(['click']) // 图标映射表 const iconMap = { AccessibilitySharp: markRaw(AccessibilitySharp), LogoChrome: markRaw(LogoChrome), ServerOutline: markRaw(ServerOutline), CloudDoneOutline: markRaw(CloudDoneOutline), AnalyticsOutline: markRaw(AnalyticsOutline), BuildOutline: markRaw(BuildOutline), BusinessOutline: markRaw(BusinessOutline), CartOutline: markRaw(CartOutline), DocumentTextOutline: markRaw(DocumentTextOutline), FileTrayFullOutline: markRaw(FileTrayFullOutline), GridOutline: markRaw(GridOutline), LogoAmplify: markRaw(LogoAmplify), LogoApple: markRaw(LogoApple), LogoDesignernews: markRaw(LogoDesignernews), LogoEdge: markRaw(LogoEdge), LogoElectron: markRaw(LogoElectron) } // 获取图标组件 const getIconComponent = (iconName) => { const icon = iconMap[iconName] if (!icon) { console.warn(`Icon not found: ${iconName}, using default icon`) return iconMap.AccessibilitySharp } return icon } </script> <template> <n-card hoverable class="app-card" @click="emit('click', app)"> <div class="app-content"> <div class="app-main"> <n-icon size="48" :component="getIconComponent(app.iconName)" class="app-logo" :color="app.iconColor" /> <div class="app-info"> <div class="app-name">{{ app.name }}</div> <div class="app-meta"> <span>所有者：{{ app.owner }}</span> <span class="divider">|</span> <span>我的角色：{{ app.myRole }}</span> </div> </div> <div class="app-status"> <n-tag :type="app.enabled ? 'success' : 'warning'" size="small"> {{ app.enabled ? '已启用' : '已禁用' }} </n-tag> </div> </div> <n-divider /> <div class="app-update"> <span class="update-label">最新动态：</span> <span class="update-text">{{ app.latestUpdate }}</span> </div> </div> </n-card> </template> <style scoped> .app-card { height: 140px; cursor: pointer; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); border: 1px solid #e8e8e8; border-radius: 8px; overflow: hidden; } .app-card:hover { transform: translateY(-2px); box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2); border-color: #52c41a; } .app-content { height: 100%; display: flex; flex-direction: column; padding: 12px; box-sizing: border-box; } .app-main { display: flex; align-items: flex-start; gap: 8px; position: relative; flex: 1; min-height: 0; margin-bottom: 8px; } .app-logo { flex-shrink: 0; } .app-info { flex: 1; min-width: 0; padding-right: 60px; } .app-name { font-size: 16px; font-weight: 500; margin-bottom: 8px; color: #333; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; } .app-meta { font-size: 14px; color: #666; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; } .divider { margin: 0 8px; color: #e8e8e8; } .app-status { position: absolute; top: 0; right: 0; } :deep(.n-divider) { margin: 8px 0; } .app-update { padding: 0; font-size: 13px; color: #666; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: flex; align-items: center; gap: 4px; min-height: 20px; margin-top: auto; } .update-label { flex-shrink: 0; color: #999; } .update-text { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; } </style><script setup> import { NForm, NFormItem, NInput, NIcon } from 'naive-ui' import { ref, markRaw } from 'vue' import { AccessibilitySharp, LogoChrome, ServerOutline, CloudDoneOutline, AnalyticsOutline, BuildOutline, BusinessOutline, CartOutline, DocumentTextOutline, FileTrayFullOutline, GridOutline, LogoAmplify, LogoApple, LogoDesignernews, LogoEdge, LogoElectron } from '@vicons/ionicons5' // 先定义颜色选项 const colors = [ '#1677ff', '#52c41a', '#722ed1', '#eb2f96', '#fa8c16', '#13c2c2', '#2f54eb', '#faad14', '#a0d911', '#eb2f96', '#f5222d' ] // 再定义图标列表 const iconList = [ { name: 'AccessibilitySharp', component: markRaw(AccessibilitySharp) }, { name: 'LogoChrome', component: markRaw(LogoChrome) }, { name: 'ServerOutline', component: markRaw(ServerOutline) }, { name: 'CloudDoneOutline', component: markRaw(CloudDoneOutline) }, { name: 'AnalyticsOutline', component: markRaw(AnalyticsOutline) }, { name: 'BuildOutline', component: markRaw(BuildOutline) }, { name: 'BusinessOutline', component: markRaw(BusinessOutline) }, { name: 'CartOutline', component: markRaw(CartOutline) }, { name: 'DocumentTextOutline', component: markRaw(DocumentTextOutline) }, { name: 'FileTrayFullOutline', component: markRaw(FileTrayFullOutline) }, { name: 'GridOutline', component: markRaw(GridOutline) }, { name: 'LogoAmplify', component: markRaw(LogoAmplify) }, { name: 'LogoApple', component: markRaw(LogoApple) }, { name: 'LogoDesignernews', component: markRaw(LogoDesignernews) }, { name: 'LogoEdge', component: markRaw(LogoEdge) }, { name: 'LogoElectron', component: markRaw(LogoElectron) } ] const props = defineProps({ initialData: { type: Object, default: () => ({ name: '', description: '', iconName: '', iconColor: '', docUrl: '' }) } }) const emit = defineEmits(['submit', 'cancel']) const formRef = ref(null) // 然后再初始化表单数据 const formModel = ref({ name: props.initialData.name || '', description: props.initialData.description || '', iconName: props.initialData.iconName || iconList[0].name, iconColor: props.initialData.iconColor || colors[0], docUrl: props.initialData.docUrl || '' }) // 表单验证规则 const rules = { name: { required: true, message: '请输入应用名称', trigger: ['blur', 'input'] }, description: { required: true, message: '请输入应用描述', trigger: ['blur', 'input'] }, iconName: { required: true, message: '请选择应用图标' }, iconColor: { required: true, message: '请选择图标颜色' }, docUrl: { validator: (rule, value) => { if (!value) return true if (value.length > 1024) return new Error('文档地址长度不能超过1024个字符') if (!value.startsWith('http')) return new Error('请输入正确的文档地址URL') return true }, trigger: ['blur', 'input'] } } // 获取图标组件 const getIconComponent = (iconName) => { const icon = iconList.find(i => i.name === iconName) if (!icon) { console.warn(`Icon not found: ${iconName}, using default icon`) return AccessibilitySharp } return icon.component } const handleSubmit = () => { console.log('表单提交触发', formModel.value) formRef.value?.validate(async (errors) => { if (!errors) { emit('submit', formModel.value) } else { console.log('表单验证错误:', errors) } }) } // 将 handleSubmit 方法暴露给父组件 defineExpose({ handleSubmit }) </script> <template> <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="100" require-mark-placement="right-hanging" > <n-form-item label="图标预览" class="preview-form-item"> <div class="icon-preview"> <n-icon v-if="formModel.iconName && formModel.iconColor" :component="getIconComponent(formModel.iconName)" :color="formModel.iconColor" size="48" /> <div v-else class="preview-placeholder"> 选择图标和颜色后预览 </div> </div> </n-form-item> <n-form-item label="应用名称" path="name"> <n-input v-model:value="formModel.name" placeholder="请输入应用名称" /> </n-form-item> <n-form-item label="应用描述" path="description"> <n-input v-model:value="formModel.description" type="textarea" placeholder="请输入应用描述" :rows="3" /> </n-form-item> <n-form-item label="图标颜色" path="iconColor"> <div class="color-grid"> <div v-for="color in colors.slice(0, 8)" :key="color" class="option-button" :class="{ 'option-selected': formModel.iconColor === color }" @click="formModel.iconColor = color" :style="{ backgroundColor: color }" /> </div> </n-form-item> <n-form-item label="应用图标" path="iconName"> <div class="icon-grid"> <div v-for="icon in iconList" :key="icon.name" class="option-button" :class="{ 'option-selected': formModel.iconName === icon.name }" @click="formModel.iconName = icon.name" > <n-icon :component="icon.component" size="24" /> </div> </div> </n-form-item> <n-form-item label="文档地址" path="docUrl"> <n-input v-model:value="formModel.docUrl" placeholder="请输入文档地址（选填）" clearable /> </n-form-item> </n-form> </template> <style scoped> .preview-form-item { margin-bottom: 24px !important; } .icon-preview { display: flex; align-items: center; justify-content: center; height: 48px; background-color: transparent; border-radius: 8px; } .preview-placeholder { color: #999; font-size: 14px; } .color-grid { display: grid; grid-template-columns: repeat(8, 1fr); gap: 16px; padding: 4px; } .icon-grid { display: grid; grid-template-columns: repeat(8, 1fr); grid-template-rows: repeat(2, 1fr); gap: 16px; padding: 4px; } .option-button { width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; border-radius: 8px; cursor: pointer; transition: all 0.3s; border: 2px solid transparent; } .option-button:hover { transform: scale(1.05); } .option-selected { border: 2px solid #52c41a !important; box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important; } :deep(.n-form-item) { margin-bottom: 24px !important; } :deep(.n-form-item:last-child) { margin-bottom: 0 !important; } </style><script setup> import { ref, watch, nextTick } from 'vue' import { NModal, NSpace, NInput, NButton, NTable, NEmpty, NSpin, NCheckbox, NRadio } from 'naive-ui' import { doGet } from '@/utils/requests' import messages from '@/utils/messages' const props = defineProps({ show: { type: Boolean, default: false }, title: { type: String, default: '选择成员' }, description: { type: String, default: '' }, label: { type: String, default: '请选择成员' }, multiple: { type: Boolean, default: false }, maxSelected: { type: Number, default: Infinity } }) const emit = defineEmits(['update:show', 'confirm']) // 状态变量 const keyword = ref('') const loading = ref(false) const contacts = ref([]) const selectedContacts = ref([]) // 搜索防抖 let searchTimer = null const handleSearch = (value) => { if (searchTimer) { clearTimeout(searchTimer) } searchTimer = setTimeout(() => { searchContacts(value) }, 300) } // 搜索联系人 const searchContacts = async (keyword) => { if (!keyword) { contacts.value = [] return } loading.value = true try { const response = await doGet(`/auth-center/system/user/list?keywords=${keyword}`) contacts.value = response.data } catch (error) { messages.error('搜索成员失败') } finally { loading.value = false } } // 处理选择 const handleSelect = (contact) => { if (props.multiple) { const index = selectedContacts.value.findIndex(item => item.id === contact.id) if (index > -1) { selectedContacts.value.splice(index, 1) } else { if (selectedContacts.value.length >= props.maxSelected) { messages.warning(`最多只能选择${props.maxSelected}个成员`) return } selectedContacts.value.push({ id: contact.id, nickname: contact.nickname }) } } else { selectedContacts.value = [{ id: contact.id, nickname: contact.nickname }] } } // 检查是否已选中 const isSelected = (contact) => { return selectedContacts.value.some(item => item.id === contact.id) } // 确认选择 const handleConfirm = () => { if (selectedContacts.value.length === 0) { messages.warning('请至少选择一个成员') return } emit('confirm', selectedContacts.value) handleClose() } // 关闭弹窗 const handleClose = () => { handleUpdateShow(false) // 重置状态 keyword.value = '' contacts.value = [] selectedContacts.value = [] } // 添加处理显示状态更新的函数 const handleUpdateShow = (value) => { emit('update:show', value) } // 监听show变化 watch(() => props.show, (newVal) => { if (newVal) { // 弹窗显示时，等待DOM更新后聚焦输入框 nextTick(() => { const input = document.querySelector('.contact-search-input input') input?.focus() }) } else { handleClose() } }) // 修改表格列定义 const columns = [ { title: '姓名', key: 'nickname', align: 'center' }, { title: '邮箱', key: 'username', align: 'center' }, { title: '部门', key: 'deptName', align: 'center' } ] </script> <template> <n-modal :show="show" :title="title" preset="dialog" :mask-closable="false" style="width: 600px" positive-text="确认" negative-text="取消" @positive-click="handleConfirm" @negative-click="handleClose" @update:show="handleUpdateShow" > <n-space vertical :size="16"> <!-- 说明文字 --> <p v-if="description" class="description">{{ description }}</p> <!-- 搜索框 --> <n-input v-model:value="keyword" placeholder="请输入关键词搜索" @input="handleSearch" clearable class="contact-search-input" /> <!-- 成员列表 --> <div class="contact-list"> <n-spin :show="loading"> <div class="list-wrapper"> <div v-if="contacts.length > 0" class="list-content"> <n-table :single-line="false"> <thead> <tr> <th v-for="col in columns" :key="col.key" :style="{ textAlign: col.align }"> {{ col.title }} </th> </tr> </thead> <tbody> <tr v-for="contact in contacts" :key="contact.id" :class="{ 'selected-row': isSelected(contact) }" style="cursor: pointer" @click="handleSelect(contact)" > <td v-for="col in columns" :key="col.key" :style="{ textAlign: col.align }"> {{ contact[col.key] }} </td> </tr> </tbody> </n-table> </div> <div v-else class="empty-content"> <n-empty description="暂无搜索结果" /> </div> </div> </n-spin> </div> <!-- 已选成员区域 - 始终显示 --> <div class="selected-contacts"> <div class="selected-title">已选择 {{ selectedContacts.length }} 人</div> <div class="selected-tags" :class="{ empty: selectedContacts.length === 0 }"> <template v-if="selectedContacts.length > 0"> <n-space> <n-tag v-for="contact in selectedContacts" :key="contact.id" closable type="primary" @close="handleSelect(contact)" > {{ contact.nickname }} </n-tag> </n-space> </template> <span v-else class="no-selected">未选择任何成员</span> </div> </div> </n-space> </n-modal> </template> <style scoped> .description { color: #666; margin: 0; font-size: 14px; } .contact-list { border: 1px solid #eee; border-radius: 4px; } .list-wrapper { height: 240px; overflow-y: auto; position: relative; } .list-content { min-height: 100%; } .empty-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100%; text-align: center; } .selected-contacts { padding-top: 8px; border-top: 1px solid #eee; min-height: 80px; } .selected-title { font-size: 14px; color: #666; margin-bottom: 8px; } .selected-tags { min-height: 32px; padding: 4px 0; } .selected-tags.empty { display: flex; align-items: center; } .no-selected { color: #999; font-size: 14px; } .selected-row { background-color: var(--primary-color, #18a058) !important; color: #fff; } .selected-row:hover { background-color: var(--primary-color-hover, #36ad6a) !important; } :deep(.n-table .n-data-table-td) { padding: 8px; } :deep(.n-table .n-data-table-th) { padding: 8px; background-color: #f5f5f5; height: 40px; } tr { transition: all 0.3s; height: 40px; } tr:hover { background-color: var(--primary-color-hover, #36ad6a); color: #fff; } /* 修改标签样式 */ .n-tag { cursor: pointer; } .n-tag:hover { opacity: 0.9; } </style><script setup> import { ref, watch, onMounted, computed } from 'vue' import { NModal, NForm, NFormItem, NInput, NSpin, NButton, NSpace, NIcon } from 'naive-ui' import { WarningOutline } from '@vicons/ionicons5' import messages from '@/utils/messages' import { doPost, doGet } from '@/utils/requests' const props = defineProps({ show: { type: Boolean, default: false }, title: { type: String, default: '安全验证' }, action: { type: String, required: true } }) const emit = defineEmits(['update:show', 'verify']) const formRef = ref(null) const loading = ref(false) const sendingCode = ref(false) const countdown = ref(0) const codeInputs = ref(Array(6).fill('')) const inputRefs = ref([]) // 添加计算属性检查输入是否完整 const isCodeComplete = computed(() => { return codeInputs.value.every(code => code !== '') }) // 处理验证码输入 const handleCodeInput = (index, event) => { const value = event.target.value // 只取最后一个字符 const char = value.slice(-1) codeInputs.value[index] = char if (char && index < 5) { // 自动聚焦下一个输入框 inputRefs.value[index + 1]?.focus() } } // 处理键盘事件 const handleKeydown = (index, event) => { if (event.key === 'Backspace' && !codeInputs.value[index] && index > 0) { // 当前框为空且按下删除键时，焦点移到上一个框 codeInputs.value[index - 1] = '' inputRefs.value[index - 1]?.focus() } } // 处理粘贴 const handlePaste = (event) => { event.preventDefault() const pastedText = event.clipboardData.getData('text') const chars = [...pastedText.trim()].slice(0, 6) // 清空所有输入框 codeInputs.value = Array(6).fill('') // 依次填充字符 chars.forEach((char, index) => { codeInputs.value[index] = char }) // 聚焦到下一个空输入框或最后一个输入框 const nextEmptyIndex = codeInputs.value.findIndex(v => !v) const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex inputRefs.value[focusIndex]?.focus() } // 发送验证码 const handleSendCode = async () => { if (sendingCode.value || countdown.value > 0) return sendingCode.value = true try { await doPost('/auth-center/system/validation', { operation: props.action }) messages.success('验证码已发送至您的邮箱') countdown.value = 60 startCountdown() } catch (error) { messages.error('验证码发送失败，请重试') } finally { sendingCode.value = false } } // 倒计时 const startCountdown = () => { const timer = setInterval(() => { if (countdown.value > 0) { countdown.value-- } else { clearInterval(timer) } }, 1000) } // 处理验证提交 const handleVerify = async () => { if (loading.value) return const verifyCode = codeInputs.value.join('') if (verifyCode.length !== 6) { messages.error('请输入完整的验证码') return } loading.value = true try { // 开发阶段直接模拟验证通过 emit('verify', { verifyCode, action: props.action }) // 清空输入 codeInputs.value = Array(6).fill('') emit('update:show', false) } catch (error) { messages.error('验证失败，请重试') // 清空输入 codeInputs.value = Array(6).fill('') inputRefs.value[0]?.focus() } finally { loading.value = false } } // 处理取消 const handleCancel = () => { codeInputs.value = Array(6).fill('') emit('update:show', false) } // 监听显示状态变化 watch(() => props.show, (newVal) => { if (newVal) { // 显示时自动发送验证码并聚焦第一个输入框 handleSendCode() setTimeout(() => { inputRefs.value[0]?.focus() }, 100) } else { codeInputs.value = Array(6).fill('') countdown.value = 0 } }) // 处理模态框显示状态 const handleUpdateShow = (value) => { emit('update:show', value) } </script> <template> <n-modal :show="show" preset="dialog" title="安全验证" positive-text="确认" negative-text="取消" :mask-closable="false" :closable="false" :keyboard="false" @update:show="handleUpdateShow" @positive-click="handleVerify" @negative-click="handleCancel" :positive-button-props="{ disabled: loading || !isCodeComplete, loading: loading }" > <n-spin :show="loading"> <div class="verify-content"> <div class="verify-notice"> <p class="notice-title"> <n-icon size="18" color="#ff4d4f" style="margin-right: 8px"> <warning-outline /> </n-icon> 您正在进行敏感操作：{{ props.action }} </p> <p class="notice-desc">请输入邮箱验证码进行验证</p> </div> <div class="verify-code-input"> <n-space :size="12" justify="center"> <input v-for="(code, index) in codeInputs" :key="index" :ref="el => inputRefs[index] = el" v-model="codeInputs[index]" type="text" maxlength="1" class="code-input" @input="e => handleCodeInput(index, e)" @keydown="e => handleKeydown(index, e)" @paste="handlePaste" /> </n-space> </div> <div class="resend-button"> <n-button :disabled="sendingCode || countdown > 0" :loading="sendingCode" @click="handleSendCode" text type="primary" > {{ countdown > 0 ? `${countdown}秒后可重新发送` : '重新发送验证码' }} </n-button> </div> </div> </n-spin> </n-modal> </template> <style scoped> .verify-content { padding: 8px 0; } .verify-notice { margin-bottom: 32px; text-align: center; } .notice-title { font-size: 14px; color: #262626; margin: 0 0 8px 0; display: flex; align-items: center; } .notice-desc { font-size: 14px; color: #8c8c8c; margin: 0; } .verify-code-input { margin: 24px 0; } .code-input { width: 44px; height: 44px; border: 1px solid #d9d9d9; border-radius: 8px; font-size: 20px; text-align: center; color: #262626; background: #fff; transition: all 0.3s; outline: none; caret-color: #1890ff; } .code-input:focus { border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); } .resend-button { text-align: center; margin-top: 16px; height: 32px; display: flex; align-items: center; justify-content: center; } /* 调整按钮内部的loading图标对齐 */ :deep(.n-button) { display: inline-flex; align-items: center; justify-content: center; min-height: 32px; } </style><template> <div class="page-container"> <n-card> <div class="header"> <h2>API 访问控制</h2> <p class="description">管理应用可以访问的 API 接口及其权限</p> </div> <div class="content-layout"> <!-- 左侧能力列表 --> <div class="ability-list"> <template v-if="abilityList.length > 0"> <div v-for="ability in abilityList" :key="ability.id" class="ability-item" :class="{ 'ability-item--active': selectedAbilityId === ability.id }" @click="handleAbilitySelect(ability.id)" > <span class="ability-name">{{ ability.abilityName }}</span> </div> </template> <n-empty v-else description="暂无可用能力" /> </div> <!-- 右侧 API 列表 --> <div class="api-list"> <n-data-table :columns="columns" :data="aclList" :loading="loading" /> </div> </div> </n-card> </div> </template> <script setup> import { ref, onMounted, inject, h } from 'vue' import { useDialog } from 'naive-ui' import { doGet, doPost, doDelete } from '@/utils/requests' import messages from '@/utils/messages' import { NEmpty, NIcon } from 'naive-ui' import { OpenOutline } from '@vicons/ionicons5' const currentAppId = inject('currentAppId') const dialog = useDialog() // 能力列表相关 const abilityList = ref([]) const selectedAbilityId = ref(null) // 数据相关 const loading = ref(false) const aclList = ref([]) // 加载应用已获取的能力列表 const loadAbilityList = async () => { try { const res = await doGet(`/app-center/app/acl/${currentAppId.value}/abilities`) if (res.code === 0) { abilityList.value = res.data // 默认选中第一个能力 if (abilityList.value.length > 0) { selectedAbilityId.value = abilityList.value[0].id loadACLList() } } } catch (error) { messages.error('获取能力列表失败') } } // 处理能力选择 const handleAbilitySelect = (abilityId) => { selectedAbilityId.value = abilityId loadACLList() } // 加载 ACL 列表 const loadACLList = async () => { if (!selectedAbilityId.value) return loading.value = true try { const res = await doGet( `/app-center/app/acl/${currentAppId.value}/ability/${selectedAbilityId.value}/apis` ) if (res.code === 0) { aclList.value = res.data } } finally { loading.value = false } } const pendingApiId = ref(null) const columns = [ { title: '权限信息', key: 'api_info', render(row) { return h('div', { class: 'api-info' }, [ h('div', { class: 'api-name', title: row.api_name }, row.api_name), h('div', { class: 'api-code', title: row.api_code }, row.api_code) ]) } }, { title: '权限说明', key: 'api_desc', ellipsis: { tooltip: true }, render(row) { if (!row.doc_url) { return row.api_desc || '-' } return h('div', { class: 'desc-wrapper' }, [ h( 'a', { class: 'desc-link', onClick: (e) => { e.preventDefault() window.open(row.doc_url, '_blank') } }, [ h('span', { class: 'api-desc' }, row.api_desc || '-'), h(NIcon, { size: 18, class: 'help-icon' }, { default: () => h(OpenOutline) }) ] ) ]) } }, { title: '权限状态', key: 'usable', render(row) { return h('div', { class: 'status-wrapper' }, [ h('span', { class: ['status-dot', row.usable === 1 ? 'status-dot--active' : 'status-dot--inactive'] }), h('span', { class: 'status-text' }, row.usable === 1 ? '已开通' : '未开通') ]) } }, { title: '操作', key: 'actions', render(row) { return h( 'div', { class: ['action-text', row.usable === 1 ? 'action-text--cancel' : 'action-text--enable'], onClick: () => handleStatusChange(row) }, row.usable === 1 ? '取消' : '开通' ) } } ] const handleStatusChange = async (row) => { if (row.usable === 1) { // 取消授权时显示确认对话框 dialog.warning({ title: '确认取消授权', content: '取消授权后，应用将无法继续调用该接口，是否确认？', positiveText: '确认取消', negativeText: '保持授权', onPositiveClick: async () => { try { const res = await doDelete(`/app-center/app/acl/${currentAppId.value}/api/${row.id}`) if (res.code === 0) { messages.success('取消授权成功') await loadACLList() } } catch (error) { messages.error('取消授权失败') } } }) } else { // 开通授权时显示确认对话框 dialog.info({ title: '确认开通授权', content: '开通授权后，应用将可以调用该接口，是否确认？', positiveText: '确认开通', negativeText: '取消', onPositiveClick: async () => { try { const res = await doPost(`/app-center/app/acl/${currentAppId.value}/api/${row.id}`) if (res.code === 0) { messages.success('开通授权成功') await loadACLList() } } catch (error) { messages.error('开通授权失败') } } }) } } onMounted(() => { loadAbilityList() }) </script> <style scoped> .page-container { padding: 24px; background-color: #f5f7fa; min-height: 100vh; } .header { margin-bottom: 24px; } .header h2 { margin: 0; font-size: 20px; font-weight: 500; color: #333; } .description { margin: 8px 0 0; color: #666; font-size: 14px; } .content-layout { display: flex; gap: 24px; margin-top: 24px; } .ability-list { width: 240px; background: #fff; border-radius: 8px; padding: 16px; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06); } .ability-item { height: 44px; padding: 0 16px; margin: 4px 0; border-radius: 6px; display: flex; align-items: center; cursor: pointer; transition: all 0.3s; position: relative; } .ability-item:hover { background-color: #f3f3f3; } .ability-item--active { background-color: #e8f7f2; color: #18a058; font-weight: 500; } .ability-item--active::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background-color: #18a058; border-top-left-radius: 6px; border-bottom-left-radius: 6px; } .ability-name { font-size: 14px; flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; } /* 空状态样式 */ .ability-list :deep(.n-empty) { padding: 24px 0; } .api-list { flex: 1; background: #fff; border-radius: 8px; padding: 16px; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06); overflow-x: auto; } /* 确保表格内容不换行 */ :deep(.n-data-table .n-data-table-table) { width: auto; min-width: 100%; white-space: nowrap; } .api-info { display: flex; flex-direction: column; gap: 4px; width: 400px; } .api-name { font-size: 14px; font-weight: 500; color: #333; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; } .api-code { font-size: 12px; color: #999; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; } .doc-link { color: #2080f0; text-decoration: none; } .doc-link:hover { text-decoration: underline; } /* 修改状态样式 */ :deep(.status-wrapper) { display: inline-flex; align-items: center; gap: 6px; } :deep(.status-dot) { display: inline-block; width: 6px; height: 6px; border-radius: 50%; } :deep(.status-dot--active) { background-color: #18a058; } :deep(.status-dot--inactive) { background-color: #bbb; } :deep(.status-text) { font-size: 13px; color: #666; } /* 修改操作按钮式 */ :deep(.action-text) { font-size: 13px; cursor: pointer; transition: opacity 0.2s; } :deep(.action-text--cancel) { color: #bbb; /* 改为灰色，与未开通状态的圆点颜色一致 */ } :deep(.action-text--enable) { color: #18a058; /* 保持绿色，与已开通状态的圆点颜色一致 */ } :deep(.action-text:hover) { opacity: 0.8; } .desc-wrapper { display: flex; align-items: center; } :deep(.desc-link) { display: inline-flex; align-items: center; gap: 4px; color: #333; text-decoration: none; border-bottom: 1px dashed #18a058; padding-bottom: 1px; cursor: pointer; max-width: fit-content; } :deep(.api-desc) { font-size: 13px; } :deep(.help-icon) { color: #18a058; margin-left: 4px; } :deep(.desc-link:hover) { opacity: 0.8; } </style><script setup> import { ref, onMounted, inject } from 'vue' import { NCard, NSpace, NInput, NButton, NIcon, NPopconfirm, useMessage, NForm, NFormItem, NDataTable, NSwitch } from 'naive-ui' import { HelpCircleOutline, AddOutline, EllipseOutline, ArrowForwardOutline, TrashOutline } from '@vicons/ionicons5' import { useRoute } from 'vue-router' import { doGet,doPost,doDelete,doPut } from '@/utils/requests' import messages from '@/utils/messages' const currentAppId = inject('currentAppId') const route = useRoute() const message = useMessage() const appId = route.params.id // IP白名单列表数据 const ipList = ref([]) const newIp = ref('') const loading = ref(false) // 表格列定义 const columns = [ { title: 'IP地址', key: 'ip' }, { title: '操作', key: 'actions', render: (row) => { return h( NPopconfirm, { onPositiveClick: () => removeIp(row.ip) }, { trigger: () => h( NButton, { quaternary: true, circle: true, size: 'small', type: 'error' }, { icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) } ), default: () => '确认删除此IP地址吗？' } ) } } ] // 添加状态枚举 const AWL_STATUS = { ENABLE: 'ENABLE', DISABLED: 'DISABLED' } // 修改IP白名单数据结构 const awlData = ref({ id: null, appId: appId, ipWhiteList: '', awlStatus: AWL_STATUS.DISABLED }) // 添加IP格式验证函数 const isValidIP = (ip) => { const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/ if (!ipRegex.test(ip)) return false const parts = ip.split('.') return parts.every(part => { const num = parseInt(part, 10) return num >= 0 && num <= 255 }) } // 修改加载IP白名单列表方法 const loadIpList = async () => { try { loading.value = true const response = await doGet(`/app-center/app/awl/detail/${appId}`) if (response.data) { // 确保所有必要的字段都存在 awlData.value = { id: response.data.id || null, appId: response.data.appId || appId, ipWhiteList: response.data.ipWhiteList || '', awlStatus: response.data.awlStatus || AWL_STATUS.DISABLED } // 处理 IP 列表 ipList.value = awlData.value.ipWhiteList ? awlData.value.ipWhiteList.split(',') .filter(ip => ip.trim()) .map(ip => ({ ip: ip.trim(), createTime: '-' })) : [] } } catch (error) { console.error('Error loading IP list:', error) messages.error('加载IP白名单失败') // 确保发生错误时有默认值 awlData.value = { id: null, appId: appId, ipWhiteList: '', awlStatus: AWL_STATUS.DISABLED } ipList.value = [] } finally { loading.value = false } } // 修改添加IP地址方法 const addIp = async () => { if (!newIp.value) { messages.error('请输入IP地址') return } try { // 分割输入的IP地址(支持逗号、分号、空格分隔) const ips = newIp.value.split(/[,;\s]+/).filter(ip => ip.trim()) // 验证所有IP格式 const invalidIps = ips.filter(ip => !isValidIP(ip.trim())) if (invalidIps.length > 0) { messages.error(`以下IP地址格式无效: ${invalidIps.join(', ')}`) return } // 检查重复的IP const existingIps = ipList.value.map(item => item.ip) const duplicateIps = ips.filter(ip => existingIps.includes(ip.trim())) const newUniqueIps = ips.filter(ip => !existingIps.includes(ip.trim())) // 如果全部都是重复的IP，提示用户 if (newUniqueIps.length === 0) { messages.warning('输入的IP地址已全部存在于白名单中') newIp.value = '' // 清空输入框 return } // 如果有部分重复的IP，提示用户 if (duplicateIps.length > 0) { messages.info(`以下IP地址已存在，将被自动过滤: ${duplicateIps.join(', ')}`) } // 合并不重复的IP const allIps = [...existingIps, ...newUniqueIps] await doPut('/app-center/app/awl', { id: awlData.value.id, ipWhiteList: allIps.join(',') }) messages.success('添加成功') newIp.value = '' await loadIpList() } catch (error) { messages.error('添加失败') } } // 修改删除IP地址方法 const removeIp = async (ip) => { try { // 从现有IP列表中移除指定IP const newIpList = ipList.value .filter(item => item.ip !== ip) .map(item => item.ip) await doPut('/app-center/app/awl', { id: awlData.value.id, ipWhiteList: newIpList.join(',') }) messages.success('删除成功') await loadIpList() } catch (error) { messages.error('删除失败') } } // 修改toggleAwlStatus方法 const toggleAwlStatus = async (value) => { try { loading.value = true const newStatus = value ? AWL_STATUS.ENABLE : AWL_STATUS.DISABLED // 获取当前的IP列表 const currentIpList = ipList.value.map(item => item.ip).join(',') // 如果是第一次开启，需要创建记录 if (!awlData.value.id) { const response = await doPost('/app-center/app/awl', { appId: appId, awlStatus: newStatus, ipWhiteList: currentIpList // 传递当前IP列表 }) if (response.data) { awlData.value = response.data } } else { // 更新现有记录 await doPut('/app-center/app/awl', { id: awlData.value.id, awlStatus: newStatus, ipWhiteList: currentIpList // 传递当前IP列表 }) awlData.value.awlStatus = newStatus awlData.value.ipWhiteList = currentIpList } messages.success(value ? '已开启IP白名单' : '已关闭IP白名单') await loadIpList() // 重新加载数据以确保状态同步 } catch (error) { console.error('Toggle AWL status error:', error) messages.error('切换状态失败：' + (error.message || '未知错误')) // 如果失败，回滚UI状态 awlData.value.awlStatus = value ? AWL_STATUS.DISABLED : AWL_STATUS.ENABLE } finally { loading.value = false } } onMounted(() => { console.log('Component mounted, calling loadIpList') loadIpList() }) </script> <template> <div class="awl-config"> <n-card class="help-card"> <n-space vertical :size="16"> <div class="help-header"> <n-icon size="24" color="#1890ff"> <help-circle-outline /> </n-icon> <span class="help-title">IP白名单说明</span> </div> <div class="help-content"> <div class="help-item"> <div class="help-item-title"> <n-icon color="#52c41a" class="bullet-icon"> <ellipse-outline /> </n-icon> 什么是IP白名单？ </div> <p class="help-item-desc"> IP白名单用于限制可以访问您应用API的IP地址，只有在白名单中的IP地址才能调用API。 </p> </div> <div class="help-item"> <div class="help-item-title"> <n-icon color="#52c41a" class="bullet-icon"> <ellipse-outline /> </n-icon> 如何使用？ </div> <p class="help-item-desc"> 1. 添需要允许访问的IP地址到白名单中；<br> 2. 如果白名单为空，则表示不限制IP访问；<br> 3. 建议添加固定的服务器IP地址，以提高安全性。 </p> </div> </div> <n-space justify="end"> <n-button text type="primary" @click="window.open('文档URL', '_blank')"> 查看详细文档 <template #icon> <n-icon><arrow-forward-outline /></n-icon> </template> </n-button> </n-space> </n-space> </n-card> <n-card title="IP白名单管理" class="config-card"> <template #header-extra> <n-space align="center"> <span class="status-text"> {{ awlData.awlStatus === AWL_STATUS.ENABLE ? '已开启' : '已关闭' }} </span> <n-switch :value="awlData.awlStatus === AWL_STATUS.ENABLE" @update:value="toggleAwlStatus" :disabled="loading" /> </n-space> </template> <n-space vertical> <n-space align="center"> <n-input v-model:value="newIp" placeholder="请输入您的服务器出口IP地址，按回车键新增" style="min-width: 500px" size="large" :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE" @keyup.enter="addIp" /> <n-button type="primary" @click="addIp" size="large" :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE" > <template #icon> <n-icon><add-outline /></n-icon> </template> 添加 </n-button> </n-space> <n-space wrap> <n-tag v-for="item in ipList" :key="item.ip" closable :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE" @close="removeIp(item.ip)" > {{ item.ip }} </n-tag> </n-space> </n-space> </n-card> </div> </template> <style scoped> .awl-config { padding: 24px; } .config-card { margin-bottom: 24px; min-width: 800px; } .help-card { margin-bottom: 24px; background-color: #fafafa; } .help-header { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; } .help-title { font-size: 16px; font-weight: 500; color: #262626; } .help-content { display: flex; flex-direction: column; gap: 16px; } .help-item { display: flex; flex-direction: column; gap: 4px; } .help-item-title { display: flex; align-items: center; gap: 8px; font-size: 14px; font-weight: 500; color: #262626; } .bullet-icon { font-size: 8px; } .help-item-desc { margin: 0; padding-left: 20px; font-size: 14px; color: #595959; line-height: 1.6; } .status-text { font-size: 14px; color: #8c8c8c; } /* 更新n-tag样式 */ .n-tag { font-size: 18px; /* 增大字体 */ padding: 10px 20px; /* 增大内边距 */ margin: 6px; /* 增加间距 */ display: flex; align-items: center; } .n-tag .n-tag__close { margin-left: 12px; /* 增加文本和关闭按之间的间距 */ cursor: pointer; /* 添加鼠标指针样式 */ } </style><template> <div class="page-container"> <!-- 如果显示试用页面则隐藏能力列表 --> <div v-if="!showTrial"> <n-card> <!-- 原有的能力列表内容 --> <div class="header"> <h2>应用能力</h2> </div> <div class="ability-list"> <div v-for="item in abilityList" :key="item.id" class="ability-item"> <img :src="item.abilityCover" :alt="item.abilityName"> <h3>{{ item.abilityName }}</h3> <p>{{ item.abilityDesc }}</p> <div class="api-count">为您提供了 {{ item.apiCount }} 个API</div> <div class="button-group"> <n-button type="info" @click="handleTryAbility(item.trialPage, item)" class="action-button" :disabled="!item.trialPage" > {{ item.trialPage ? '立即体验' : '敬请期待' }} </n-button> <n-button :type="item.exists ? 'error' : 'success'" @click="handleAbilityClick(item.id, item.exists)" class="action-button" > {{ item.exists ? '删除能力' : '添加能力' }} </n-button> </div> </div> </div> </n-card> </div> <!-- 添加试用页面区域 --> <div v-else class="trial-container"> <component :is="currentTrialComponent" v-if="currentTrialComponent" :ability_added="Boolean(currentAbilityStatus)" :ability_id="currentAbilityId" :task-type="ability_type" @close="closeTrial" /> </div> <!-- 安全验证弹窗 --> <security-verify-modal v-model:show="showSecurityVerify" :action="currentAction" @verify="handleSecurityVerify" /> </div> </template> <script setup> import { ref, onMounted, inject } from 'vue' import { useDialog } from 'naive-ui' import { doGet, doPost, doPut } from '@/utils/requests' import SecurityVerifyModal from '@/components/system/SecurityVerifyModal.vue' import messages from '@/utils/messages' import PerceptionTrial from './PerceptionTrial.vue' import { markRaw } from 'vue' const currentAppId = inject('currentAppId') const dialog = useDialog() const abilityList = ref([]) // 安全验证相关 const showSecurityVerify = ref(false) const currentAction = ref('') const pendingAbilityId = ref(null) const pendingExists = ref(false) // 新增状态 const showTrial = ref(false) const currentTrialComponent = ref(null) // 添加当前能力状态 const currentAbilityStatus = ref(false) const currentAbilityId = ref(null) // 添加 ability_type ref const ability_type = ref('detection') // 默认为检测任务 const getAbilityList = async () => { const res = await doGet('/app-center/app/ability/list?appId=' + currentAppId.value) if (res.code === 0) { abilityList.value = res.data } } const handleAbilityClick = (abilityId, exists) => { if (!exists) { // 添加能力直接处理 handleAbility(abilityId, exists) } else { // 删除能力需要确认 dialog.warning({ title: '确认删除', content: '确定要删除该能力吗？删除后将无法使用能力提供的API。', positiveText: '确定', negativeText: '取消', onPositiveClick: () => { pendingAbilityId.value = abilityId pendingExists.value = exists currentAction.value = '删除能力' showSecurityVerify.value = true } }) } } const handleSecurityVerify = async () => { // 验证通过后执行删除操作 await handleAbility(pendingAbilityId.value, pendingExists.value) // 重置状态 pendingAbilityId.value = null pendingExists.value = false showSecurityVerify.value = false } const handleAbility = async (abilityId, exists) => { if (exists) { // 删除能力 const res = await doPut('/app-center/app/acl', { appId: currentAppId.value, abilityId }) if (res.code === 0) { messages.success('删除成功') if (abilityId === currentAbilityId.value) { currentAbilityStatus.value = false } await getAbilityList() } } else { // 添加能力 const res = await doPost('/app-center/app/acl', { appId: currentAppId.value, abilityId }) if (res.code === 0) { messages.success('添加成功') if (abilityId === currentAbilityId.value) { currentAbilityStatus.value = true } await getAbilityList() } } } const handleTryAbility = (trialPage, item) => { if (trialPage) { currentTrialComponent.value = markRaw(PerceptionTrial) currentAbilityStatus.value = Boolean(item.exists) currentAbilityId.value = item.id showTrial.value = true } } // 添加关闭试用页面的方法 const closeTrial = () => { showTrial.value = false currentTrialComponent.value = null } // 修改打开试用的方法，接收能力类型 const openTrial = (id, type) => { ability_id.value = id ability_type.value = type // 设置能力类型 showTrial.value = true } onMounted(() => { getAbilityList() }) </script> <style scoped> .page-container { padding: 24px; background-color: #f5f7fa; min-height: 100vh; } .header { margin-bottom: 24px; } .header h2 { margin: 0; font-size: 20px; font-weight: 500; color: #333; } .ability-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 24px; } .ability-item { background: #fff; border-radius: 8px; padding: 16px; position: relative; min-height: 380px; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06); transition: all 0.3s ease; padding-bottom: 70px; } .ability-item:hover { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); transform: translateY(-2px); } .ability-item img { width: 100%; height: 200px; object-fit: cover; border-radius: 6px; } .ability-item h3 { margin: 16px 0 8px; font-size: 16px; color: #333; } .ability-item p { color: #666; font-size: 14px; line-height: 1.5; } .api-count { font-size: 12px; color: #666; margin: 12px 0; display: flex; align-items: center; } .button-group { position: absolute; bottom: 16px; left: 16px; right: 16px; display: flex; justify-content: space-between; align-items: center; } .action-button { min-width: 90px; /* 设置最小宽度确保按钮大小一致 */ height: 34px; /* 统一按钮高度 */ padding: 0 16px; /* 统一内边距 */ font-size: 14px; /* 统一字体大小 */ } .trial-container { background: #fff; border-radius: 8px; padding: 20px; } .trial-header { margin-bottom: 20px; padding-bottom: 16px; border-bottom: 1px solid #eee; } </style><script setup> import { ref, onMounted, computed, h } from 'vue' import { NCard, NSpace, NInput, NButton, NIcon, NTag, NPopconfirm, NModal, NForm, NFormItem, useMessage, NTooltip, NAutoComplete, NAvatar } from 'naive-ui' import { CopyOutline, EyeOutline, EyeOffOutline, ReloadOutline, CreateOutline, WarningOutline, SwapHorizontalOutline, TrashOutline, AccessibilitySharp, LogoChrome, ServerOutline, CloudDoneOutline, AnalyticsOutline, BuildOutline, BusinessOutline, CartOutline, DocumentTextOutline, FileTrayFullOutline, GridOutline, LogoAmplify, LogoApple, LogoDesignernews, LogoEdge, LogoElectron, HelpCircleOutline, PowerOutline } from '@vicons/ionicons5' import { applicationApi } from '@/api/applications' import { useRoute, useRouter } from 'vue-router' import AppForm from '@/components/app/AppForm.vue' import messages from '@/utils/messages' import SecurityVerifyModal from '@/components/system/SecurityVerifyModal.vue' import { doPut, doGet } from '@/utils/requests' import ContactSelectModal from '@/components/contacts/ContactSelectModal.vue' import { inject } from 'vue' const route = useRoute() const router = useRouter() const message = useMessage() const appId = route.params.id const showSecret = ref(false) const showEditModal = ref(false) // 编辑表单 const formRef = ref(null) const formModel = ref({ iconName: '', iconColor: '', appName: '', docUrl: '' }) // 复制文本 const copyText = async (text, type) => { try { await navigator.clipboard.writeText(text) messages.success(`${type}已复制到剪贴板`) } catch (err) { messages.error('复制失败') } } // 添加状态变量 const showSecurityVerify = ref(false) const currentAction = ref('') // 在script setup中添加新的状态变量 const showContactSelect = ref(false) // 添加防抖函数 const debounce = (fn, delay) => { let timer = null return (...args) => { if (timer) clearTimeout(timer) timer = setTimeout(() => { fn.apply(this, args) }, delay) } } // 修改用户搜索函数 const doUserSearch = async (query) => { if (!query) { userSearchResults.value = [] return } searchingUsers.value = true try { const response = await doGet(`/auth-center/system/user/list?keyword=${query}`) userSearchResults.value = response.data.map(user => ({ label: user.nickname, value: user.id, nickname: user.nickname })) } catch (error) { messages.error('搜索用户失败') userSearchResults.value = [] } finally { searchingUsers.value = false } } // 创建防抖版本的搜索函数 const handleUserSearch = debounce(doUserSearch, 100) // 修改转移所有权函数 const handleTransferOwnership = async () => { if (!transferTo.value) { messages.error('请选择转移对象') return } try { await doPut(`/app-center/app/info/${appId}`, { ownerId: transferTo.value }) messages.success('应用转移成功') showTransferModal.value = false // 转移成功后跳转到应用中心 router.push('/app/center') } catch (error) { messages.error('应用转移失败') } } // 修改安全验证处理函数 const handleSecurityVerify = async ({ verifyCode, action }) => { try { switch (action) { case '重置密钥': await handleResetSecret() break case '转移应用所有权': showContactSelect.value = true // 显示成员选择弹窗 break case '删除应用': await handleDeleteApp() break } showSecurityVerify.value = false } catch (error) { console.error('操作失败:', error) messages.error('操作失败') } } // 重置密钥 const handleResetSecret = async () => { try { await doPut(`/app-center/app/info/${appId}`, { appSecret: 'reset' }) messages.success('密钥重置成功') // 触发更新事件，让父组件重新加载数据 emit('update') } catch (error) { console.error('重置密钥失败:', error) messages.error('密钥重置失败') } } // 删除应用 const handleDeleteApp = async () => { try { await doPut(`/app-center/app/info/${appId}`, { appState: 'DROPPED' }) messages.success('应用删除成功') // 修改删除后的跳转路径为根路径 router.push('/') } catch (error) { console.error('删除应用失败:', error) messages.error('删除应用失败') } } // 添加图标映射函数 const getIconComponent = (iconName) => { const iconMap = { 'AccessibilitySharp': AccessibilitySharp, 'LogoChrome': LogoChrome, 'ServerOutline': ServerOutline, 'CloudDoneOutline': CloudDoneOutline, 'AnalyticsOutline': AnalyticsOutline, 'BuildOutline': BuildOutline, 'BusinessOutline': BusinessOutline, 'CartOutline': CartOutline, 'DocumentTextOutline': DocumentTextOutline, 'FileTrayFullOutline': FileTrayFullOutline, 'GridOutline': GridOutline, 'LogoAmplify': LogoAmplify, 'LogoApple': LogoApple, 'LogoDesignernews': LogoDesignernews, 'LogoEdge': LogoEdge, 'LogoElectron': LogoElectron } return iconMap[iconName] || LogoElectron } // 处理编辑提交 const handleEditSubmit = async (formData) => { try { console.log('提交编辑表单:', formData) await applicationApi.updateApplication(appId, { appName: formData.name, appDesc: formData.description, iconName: formData.iconName, iconColor: formData.iconColor, docUrl: formData.docUrl }) showEditModal.value = false messages.success('应用信息更新成功') // 触发更新事件，让父组件重新加载数据 emit('update') } catch (error) { console.error('更新应用失败:', error) messages.error('应用信息更新失败') } } // 添加一个新的方法来处理确认按钮点击 const handlePositiveClick = async () => { console.log('点击确认按钮') if (formRef.value) { await formRef.value.handleSubmit() } } // 初始化编辑表单数据 const getInitialFormData = () => ({ name: props.appInfo?.appName || '', description: props.appInfo?.description || props.appInfo?.appDesc || '', iconName: props.appInfo?.iconName || '', iconColor: props.appInfo?.iconColor || '', docUrl: props.appInfo?.docUrl || '' }) // 处理危险操作的点击 const handleDangerousAction = (action) => { currentAction.value = action showSecurityVerify.value = true } // 添加选项渲染函数 const renderUserOption = (option, selected) => { return h('div', { class: 'user-option' }, [ h(NAvatar, { round: true, size: 'small', src: `https://api.dicebear.com/7.x/pixel-art/svg?seed=${option.nickname}` }), h('span', { class: 'user-nickname' }, option.nickname) ]) } // 修改成员选择处理函数 const handleContactSelect = async (selected) => { try { await doPut(`/app-center/app/info/${appId}`, { ownerId: selected[0].id, ownerName: selected[0].nickname }) messages.success('应用转移成功') router.push('/') } catch (error) { console.error('转移应用失败:', error) messages.error('应用转移失败') } } // 修改 props 定义，允许 null 值 const props = defineProps({ appInfo: { type: Object, required: false, // 改为 false default: () => ({}) // 提供默认值 } }) // 添加一个计算属性来判断是否在加载中 const isLoading = computed(() => !props.appInfo) // 添加 emit 定义 const emit = defineEmits(['update']) // 修改启用/停用控制函数 const handleToggleAppState = async () => { try { const newState = props.appInfo?.appState === 'DISABLED' ? 'ENABLE' : 'DISABLED' const response = await doPut(`/app-center/app/info/${appId}`, { appState: newState }) // 根据操作类型显示不同的成功消息 const actionText = newState === 'ENABLE' ? '启用' : '停用' messages.success(`应用${actionText}成功`) // 刷新当前页面 window.location.reload() } catch (error) { console.error('切换应用状态失败:', error) messages.error('操作失败') } } </script> <template> <!-- 添加加载状态判断 --> <div v-if="isLoading" class="loading-state"> <n-spin size="medium" /> <span>加载中...</span> </div> <div v-else class="app-base-info"> <!-- 应用凭证区域 --> <n-card title="应用凭证" class="info-card"> <n-grid :cols="2" :x-gap="24"> <n-grid-item> <div class="credential-item"> <span class="label"> App ID <n-tooltip trigger="hover" placement="right"> <template #trigger> <n-icon class="help-icon"> <help-circle-outline /> </n-icon> </template> 应用的唯一标识 </n-tooltip> </span> <div class="value-container"> <span class="value">{{ props.appInfo?.appId }}</span> <n-button quaternary circle size="small" @click="copyText(props.appInfo?.appId, 'AppID')"> <template #icon> <n-icon><copy-outline /></n-icon> </template> </n-button> </div> </div> </n-grid-item> <n-grid-item> <div class="credential-item"> <span class="label"> App Secret <n-tooltip trigger="hover" placement="right"> <template #trigger> <n-icon class="help-icon"> <help-circle-outline /> </n-icon> </template> 应用密钥，用于获取app_access_token </n-tooltip> </span> <div class="value-container"> <span class="value"> {{ showSecret ? props.appInfo?.appSecret : '********************************' }} </span> <n-space :size="8"> <n-button quaternary circle size="small" @click="showSecret = !showSecret"> <template #icon> <n-icon> <component :is="showSecret ? EyeOffOutline : EyeOutline" /> </n-icon> </template> </n-button> <n-button quaternary circle size="small" @click="copyText(props.appInfo?.appSecret, 'Appsecret')"> <template #icon> <n-icon><copy-outline /></n-icon> </template> </n-button> <n-popconfirm @positive-click="() => handleDangerousAction('重置密钥')" positive-text="确定" negative-text="取消"> <template #trigger> <n-button quaternary circle size="small"> <template #icon> <n-icon><reload-outline /></n-icon> </template> </n-button> </template> 确定要重置应用密钥吗？重置后需要更新您的应用配置。 </n-popconfirm> </n-space> </div> </div> </n-grid-item> </n-grid> </n-card> <!-- 综合信息区域 --> <n-card title="基础信息" class="info-card"> <template #header-extra> <n-button quaternary circle @click="showEditModal = true"> <template #icon> <n-icon><create-outline /></n-icon> </template> </n-button> </template> <n-space vertical :size="16"> <div class="info-item"> <span class="label">应用图标</span> <div class="value-container"> <n-icon :component="getIconComponent(props.appInfo?.iconName)" :color="props.appInfo?.iconColor" size="48" /> </div> </div> <div class="info-item"> <span class="label">应用名称</span> <span class="value">{{ props.appInfo?.appName }}</span> </div> <div class="info-item"> <span class="label">文档地址</span> <a v-if="props.appInfo?.docUrl" :href="props.appInfo.docUrl" target="_blank" class="doc-link">{{ props.appInfo.docUrl }}</a> <span v-else class="empty-value">暂无</span> </div> </n-space> </n-card> <!-- 危险操作区域 --> <n-card title="危险操作" class="info-card danger-zone"> <template #header-extra> <n-icon color="#ff4d4f"><warning-outline /></n-icon> </template> <n-space vertical :size="16"> <!-- 添加启用/停用控制 --> <div class="danger-item"> <div class="danger-info"> <div class="danger-header"> <n-icon :color="props.appInfo?.appState === 'DISABLED' ? '#52c41a' : '#ff4d4f'"> <power-outline /> </n-icon> <span class="danger-title">{{ props.appInfo?.appState === 'DISABLED' ? '启用应用' : '停用应用' }}</span> </div> <span class="danger-desc">{{ props.appInfo?.appState === 'DISABLED' ? '重新启用此应用' : '暂时停用此应用的所有功能' }}</span> </div> <n-popconfirm @positive-click="handleToggleAppState" positive-text="确定" negative-text="取消" > <template #trigger> <n-button secondary :type="props.appInfo?.appState === 'DISABLED' ? 'success' : 'warning'"> {{ props.appInfo?.appState === 'DISABLED' ? '启用' : '停用' }} </n-button> </template> {{ props.appInfo?.appState === 'DISABLED' ? '确定要启用此应用吗？' : '确定要停用此应用吗？停用后应用将立即禁止访问。' }} </n-popconfirm> </div> <div class="danger-item"> <div class="danger-info"> <div class="danger-header"> <n-icon color="#faad14"><swap-horizontal-outline /></n-icon> <span class="danger-title">转移应用所有权</span> </div> <span class="danger-desc">将应用转移给其他用户</span> </div> <n-popconfirm @positive-click="() => handleDangerousAction('转移应用所有权')" positive-text="确定" negative-text="取消"> <template #trigger> <n-button secondary type="warning"> 转移所有权 </n-button> </template> 确定要转移应用所有权吗？转移后您将失去对应用的所有权。 </n-popconfirm> </div> <div class="danger-item"> <div class="danger-info"> <div class="danger-header"> <n-icon color="#ff4d4f"><trash-outline /></n-icon> <span class="danger-title">删除应用</span> </div> <span class="danger-desc">删除后无法恢复，请谨慎操作</span> </div> <n-popconfirm @positive-click="() => handleDangerousAction('删除应用')" positive-text="确定" negative-text="取消"> <template #trigger> <n-button secondary type="error"> 删除应用 </n-button> </template> 确定要删除应用吗？此操作不可逆。 </n-popconfirm> </div> </n-space> </n-card> </div> <!-- 修改编辑弹窗 --> <n-modal v-model:show="showEditModal" preset="dialog" title="编辑应用信息" positive-text="确定" negative-text="取消" style="width: 680px" @positive-click="handlePositiveClick" @negative-click="() => showEditModal = false"> <app-form ref="formRef" :initial-data="getInitialFormData()" @submit="handleEditSubmit" @cancel="() => showEditModal = false" /> </n-modal> <!-- 添加安全验证弹窗 --> <security-verify-modal v-model:show="showSecurityVerify" :action="currentAction" @verify="handleSecurityVerify" /> <!-- 添加成员选择弹窗 --> <contact-select-modal v-model:show="showContactSelect" title="转移应用给其他成员" description="转移后，你将无法查看和管理此应用" label="转移对象" :multiple="false" @confirm="handleContactSelect" /> </template> <style scoped> .app-base-info { padding: 24px; } .info-card { margin-bottom: 24px; } .info-card:last-child { margin-bottom: 0; } .credential-item, .info-item { display: flex; flex-direction: column; gap: 8px; } .label { font-size: 14px; color: #8c8c8c; } .value-container { display: flex; align-items: center; gap: 8px; min-width: 0; height: 48px; } .value { font-size: 14px; color: #262626; font-family: monospace; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; min-width: 0; } .empty-value { color: #bfbfbf; font-size: 14px; } .doc-link { color: #1890ff; text-decoration: none; } .doc-link:hover { text-decoration: underline; } .danger-zone { border: 1px solid #ff4d4f15; } .danger-item { display: flex; justify-content: space-between; align-items: flex-start; padding: 16px 0; border-bottom: 1px solid #f0f0f0; } .danger-item:last-child { border-bottom: none; padding-bottom: 0; } .danger-header { display: flex; align-items: center; gap: 8px; } .danger-info { display: flex; flex-direction: column; gap: 4px; } .danger-title { font-size: 14px; font-weight: 500; color: #262626; } .danger-desc { font-size: 12px; color: #8c8c8c; margin-left: 32px; /* 与图标对齐 */ } .help-icon { font-size: 14px; margin-left: 4px; cursor: help; vertical-align: -0.125em; /* 微调图标的垂直对齐 */ } /* 添加转移对话框相关样式 */ .transfer-content { padding: 16px 0; } .transfer-warning { font-size: 14px; color: #ff4d4f; margin: 0 0 24px 0; } .transfer-input { display: flex; align-items: center; gap: 8px; } .transfer-input .label { font-size: 14px; color: #262626; white-space: nowrap; } /* 调整自动完成组件的宽度 */ .transfer-input :deep(.n-auto-complete) { flex: 1; } /* 添加用户选项样式 */ .user-option { display: flex; align-items: center; gap: 8px; padding: 4px 0; } .user-nickname { font-size: 14px; color: #262626; } /* 调整下拉选项的样式 */ :deep(.n-base-selection) { width: 100%; } :deep(.n-base-selection-input) { width: 100%; } /* 添加加载状态的样式 */ .loading-state { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 200px; gap: 16px; color: #8c8c8c; } </style><script setup> import { ref, onMounted } from 'vue' import { NCard, NSpace, NInput, NButton, NIcon, NPopconfirm, useMessage, NForm, NFormItem, NSwitch, NTooltip } from 'naive-ui' import { SaveOutline, RefreshOutline, HelpCircleOutline, LinkOutline, EllipseOutline, ArrowForwardOutline, CreateOutline, EyeOutline, EyeOffOutline, CopyOutline } from '@vicons/ionicons5' import { useRoute } from 'vue-router' import { applicationApi } from '@/api/applications' import messages from '@/utils/messages' const route = useRoute() const message = useMessage() const appId = route.params.id // 回调配置数据 const callbackConfig = ref({ callbackUrl: '', callbackEncryptKey: '' }) // 加载回调配置 const loadCallbackConfig = async () => { try { const response = await applicationApi.getAppCallbackConfig(appId) console.log('API Response:', response) const { callbackUrl, aesKey } = response console.log('Parsed data:', { callbackUrl, aesKey }) callbackConfig.value = { callbackUrl: callbackUrl || '', callbackEncryptKey: aesKey || '', callbackId: response.id } tempValues.value = { url: callbackUrl || '', encryptKey: aesKey || '' } console.log('Updated config:', callbackConfig.value) } catch (error) { console.error('Load callback config error:', error) messages.error('加载回调配置失败') } } // 保存回调配置 const saveCallbackConfig = async () => { try { const updateData = { callbackUrl: callbackConfig.value.callbackUrl, aesKey: callbackConfig.value.callbackEncryptKey } console.log('Save data:', updateData) await applicationApi.updateApplication(appId, updateData) messages.success('保存成功') } catch (error) { console.error('Save config error:', error) messages.error('保存失败') } } // 添加生成随机字符串的函数 const generateRandomKey = () => { const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789' let result = '' const length = 32 for (let i = 0; i < length; i++) { result += chars.charAt(Math.floor(Math.random() * chars.length)) } return result } // 修改重置回调密钥函数 const resetCallbackSecret = async () => { const newKey = generateRandomKey() await applicationApi.updateAppCallbackConfig({ id: callbackConfig.value.callbackId, aesKey: newKey }) callbackConfig.value.callbackEncryptKey = newKey messages.success('重置成功') } // 测试回调连接 const testCallback = async () => { try { await applicationApi.testCallback(appId) messages.success('连接测试成功') } catch (error) { messages.error('连接测试失败') } } // 添加编辑状态控制 const editState = ref({ url: false }) // 添加临时编辑值 const tempValues = ref({ url: '' }) // 开始编辑 const startEdit = (field) => { if (field === 'url') { tempValues.value.url = callbackConfig.value.callbackUrl editState.value.url = true } } // 取消编辑 const cancelEdit = (field) => { if (field === 'url') { editState.value.url = false } } // 保存单个字段 const saveField = async (field) => { if (field !== 'url') return // URL 非空验证 if (!tempValues.value.url?.trim()) { messages.error('回调地址不能为空') return } // 先验证回调地址 const validateResponse = await applicationApi.validateCallback({ aesKey: callbackConfig.value.callbackEncryptKey, callbackUrl: tempValues.value.url }) // 检查验证结果 if (validateResponse.code !== 0) { messages.error(validateResponse.message || '回调地址验证失败') return } // 验证通过后保存，只传入 URL 相关参数 await applicationApi.updateAppCallbackConfig({ id: callbackConfig.value.callbackId, callbackUrl: tempValues.value.url }) // 更新本地数据 callbackConfig.value.callbackUrl = tempValues.value.url editState.value.url = false messages.success('保存成功') } // 添加显示密钥状态控制 const showSecret = ref(false) // 添加复制功能 const copySecret = () => { if (callbackConfig.value.callbackEncryptKey) { navigator.clipboard.writeText(callbackConfig.value.callbackEncryptKey) messages.success('复制成功') } } onMounted(loadCallbackConfig) </script> <template> <div class="callback-config"> <n-card class="help-card"> <n-space vertical :size="16"> <div class="help-header"> <n-icon size="24" color="#1890ff"> <help-circle-outline /> </n-icon> <span class="help-title">回调配置说明</span> </div> <div class="help-content"> <div class="help-item"> <div class="help-item-title"> <n-icon color="#52c41a" class="bullet-icon"> <ellipse-outline /> </n-icon> 什么是回调？ </div> <p class="help-item-desc"> 当您通过开放API下发的任务执行完毕后，我们会向您配置的回调地址发送任务执行结果。 </p> </div> <div class="help-item"> <div class="help-item-title"> <n-icon color="#52c41a" class="bullet-icon"> <ellipse-outline /> </n-icon> 如何接收回调？ </div> <p class="help-item-desc"> 1. 配置一个可以接收POST请求的URL地址（建议使用https协议，不支持IP:PORT方式）；<br> 2. 使用下面配置的加密密钥来解密回调数据； <br> 3. 当您准备妥当时，将回调地址更新在下面，点击保存；<br> 4. 点击保存时，会向您的回调地址发送一个测试请求，请确保按文档内容进行响应，否则可能导致回调失败。 </p> </div> <div class="help-item"> <div class="help-item-title"> <n-icon color="#52c41a" class="bullet-icon"> <ellipse-outline /> </n-icon> 安全建议 </div> <p class="help-item-desc"> 请妥善保管加密的密钥，建议定期更换。 </p> </div> </div> <n-space justify="end"> <n-button text type="primary" @click="window.open('文档URL', '_blank')"> 查看详细文档 <template #icon> <n-icon><arrow-forward-outline /></n-icon> </template> </n-button> </n-space> </n-space> </n-card> <n-card title="回调配置" class="config-card"> <n-form label-placement="left" label-width="120"> <n-form-item label="回调地址"> <div class="input-with-test"> <div class="input-with-edit"> <n-input v-if="editState.url" v-model:value="tempValues.url" placeholder="请输入回调地址" clearable class="callback-url-input" /> <n-input v-else :value="callbackConfig.callbackUrl" readonly placeholder="请输入回调地址" class="callback-url-input" /> <div class="edit-actions" v-if="editState.url"> <n-button text style="color: #18a058" @click="saveField('url')"> 保存 </n-button> <n-button text style="color: #d03050" @click="cancelEdit('url')"> 取消 </n-button> </div> <n-button v-else quaternary circle size="small" @click="startEdit('url')"> <template #icon> <n-icon><create-outline /></n-icon> </template> </n-button> </div> </div> </n-form-item> <n-form-item label="加密密钥"> <div class="input-group"> <span class="secret-text"> {{ showSecret ? callbackConfig.callbackEncryptKey : '********************************' }} </span> <n-button quaternary circle size="small" @click="showSecret = !showSecret"> <template #icon> <n-icon> <component :is="showSecret ? EyeOffOutline : EyeOutline" /> </n-icon> </template> </n-button> <n-button quaternary circle size="small" @click="copySecret"> <template #icon> <n-icon><copy-outline /></n-icon> </template> </n-button> <n-popconfirm @positive-click="resetCallbackSecret" positive-text="确定" negative-text="取消"> <template #trigger> <n-button quaternary circle size="small"> <template #icon> <n-icon><refresh-outline /></n-icon> </template> </n-button> </template> 确定要重置加密密钥吗？重置后需要更新您的回调服务配置。 </n-popconfirm> </div> </n-form-item> </n-form> </n-card> </div> </template> <style scoped> .callback-config { padding: 24px; } .config-card { margin-bottom: 24px; min-width: 800px; } .input-with-test { display: flex; gap: 12px; width: 60%; flex-wrap: wrap; } .input-with-test :deep(.n-input) { flex: 1; } .input-group { display: flex; align-items: center; gap: 8px; } .input-group :deep(.n-input) { flex: 1; min-width: 300px; } .help-icon { font-size: 16px; color: #8c8c8c; cursor: help; } :deep(.n-form-item .n-form-item-label) { font-weight: 500; } .help-card { margin-bottom: 24px; background-color: #fafafa; } .help-header { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; } .help-title { font-size: 16px; font-weight: 500; color: #262626; } .help-content { display: flex; flex-direction: column; gap: 16px; } .help-item { display: flex; flex-direction: column; gap: 4px; } .help-item-title { display: flex; align-items: center; gap: 8px; font-size: 14px; font-weight: 500; color: #262626; } .bullet-icon { font-size: 8px; } .help-item-desc { margin: 0; padding-left: 20px; font-size: 14px; color: #595959; line-height: 1.6; } .callback-url-input { min-width: 200px; } .input-with-edit { display: flex; align-items: center; gap: 8px; flex: 1; min-width: 200px; } .edit-actions { display: flex; gap: 4px; } .secret-text { font-family: monospace; font-size: 14px; color: #262626; flex: 1; min-width: 300px; padding: 4px 0; } /* 调整只读输入框的样式 */ :deep(.n-input--disabled) { background-color: transparent; } :deep(.n-input--disabled .n-input__border), :deep(.n-input--disabled .n-input__state-border) { border: none; } :deep(.n-input-wrapper) { background-color: transparent; } </style><script setup> import { ref, onMounted } from 'vue' import { NGrid, NGridItem, NButton, NModal, NIcon } from 'naive-ui' import { AddCircleOutline } from '@vicons/ionicons5' import { applicationApi } from '@/api/applications' import AppCard from '@/components/app/AppCard.vue' import AppForm from '@/components/app/AppForm.vue' import { useRouter } from 'vue-router' import messages from '@/utils/messages' const router = useRouter() const applications = ref([]) const showCreateModal = ref(false) const loading = ref(false) const formRef = ref(null) // 加载应用列表 const loadApplications = async () => { loading.value = true try { applications.value = await applicationApi.getApplications() } catch (error) { console.error('Failed to load applications:', error) } finally { loading.value = false } } // 创建应用 const handleCreateSubmit = async (formData) => { loading.value = true try { await applicationApi.createApplication(formData) messages.success('应用创建成功') showCreateModal.value = false // 重新加载应用列表 await loadApplications() } catch (error) { console.error('Failed to create application:', error) messages.error('应用创建失败') } finally { loading.value = false } } // 点击应用卡片 const handleAppClick = (app) => { console.log('Navigate to app:', app.name) // TODO: 实现应用导航逻辑 router.push(`/app/${app.id}`) } // 创建应用按钮点击 const handleCreateApp = () => { showCreateModal.value = true } // 添加一个新的方法来处理确认按钮点击 const handlePositiveClick = async () => { console.log('点击确认按钮') if (formRef.value) { await formRef.value.handleSubmit() } } onMounted(loadApplications) </script> <template> <div class="apps-container"> <div class="section-header"> <h2 class="section-title">我的应用</h2> <n-button type="primary" @click="handleCreateApp"> <template #icon> <n-icon> <AddCircleOutline /> </n-icon> </template> 创建应用 </n-button> </div> <!-- 添加空状态显示 --> <div v-if="applications.length === 0" class="empty-state"> <p class="empty-text">您还没有任何应用</p> <n-button type="primary" @click="handleCreateApp"> <template #icon> <n-icon> <AddCircleOutline /> </n-icon> </template> 立即创建 </n-button> </div> <!-- 有应用时显示网格 --> <n-grid v-else :cols="2" :x-gap="16" :y-gap="16"> <n-grid-item v-for="app in applications" :key="app.id"> <app-card :app="app" @click="handleAppClick" /> </n-grid-item> </n-grid> </div> <!-- 创建应用弹窗 --> <n-modal v-model:show="showCreateModal" preset="dialog" title="创建应用" positive-text="创建" negative-text="取消" style="width: 680px" @positive-click="handlePositiveClick" @negative-click="() => { showCreateModal = false }" :footer-style="{ backgroundColor: 'transparent' }" :positive-button-props="{ disabled: loading, loading: loading, }" :negative-button-props="{ disabled: loading }" > <app-form ref="formRef" @submit="handleCreateSubmit" @cancel="() => { showCreateModal = false }" /> </n-modal> </template> <style scoped> .apps-container { width: 100%; height: 100%; margin: 0 auto; padding: 20px; background-color: #ffffff; border-radius: 8px; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); box-sizing: border-box; display: flex; flex-direction: column; flex: 1; } /* 小屏幕设备 (平板和小屏笔记本) */ @media screen and (min-width: 768px) { .apps-container { width: 90%; } } /* 中等屏幕设备 (大屏笔记本) */ @media screen and (min-width: 1024px) { .apps-container { width: 85%; max-width: 1200px; } } /* 大屏幕设备 (台式机和大显示器) */ @media screen and (min-width: 1440px) { .apps-container { width: 75%; max-width: 1400px; } } /* 超大屏幕设备 */ @media screen and (min-width: 1920px) { .apps-container { width: 65%; max-width: 1600px; } } .section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; height: 32px; } .section-title { margin: 0; font-size: 20px; font-weight: 500; color: #333; line-height: 32px; display: flex; align-items: center; } /* 添加空状态样式 */ .empty-state { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 48px 0; background-color: #fafafa; border-radius: 8px; margin-top: 16px; } .empty-text { font-size: 14px; color: #8c8c8c; margin-bottom: 16px; } </style><script setup> import { ref, onMounted, computed, markRaw, provide } from 'vue' import { useRoute, useRouter } from 'vue-router' import { NLayout, NLayoutSider, NMenu, NIcon, NSpace, NTag, NButton, NBreadcrumb, NBreadcrumbItem } from 'naive-ui' import { HomeOutline, KeyOutline, PeopleOutline, ExtensionPuzzleOutline, ShieldOutline, NotificationsOutline, LockClosedOutline, RocketOutline, DocumentTextOutline, AccessibilitySharp, LogoChrome, ServerOutline, CloudDoneOutline, AnalyticsOutline, BuildOutline, BusinessOutline, CartOutline, FileTrayFullOutline, GridOutline, LogoAmplify, LogoApple, LogoDesignernews, LogoEdge, LogoElectron } from '@vicons/ionicons5' import { h } from 'vue' import { applicationApi } from '@/api/applications' import AppBaseInfo from './AppBaseInfo.vue' // 导入基础信息组件 import AppMember from './AppMember.vue' // 添加这行 import AppAbility from './AppAbility.vue' import AppACL from './AppACL.vue' import AppVersion from './AppVersion.vue' import AppCallbackConfig from './AppCallbackConfig.vue' import AppAwl from './AppAWL.vue' const route = useRoute() const router = useRouter() const appId = route.params.id const activeKey = ref('basic-info') const appInfo = ref(null) // 添加缓存ID的ref const currentAppId = ref('') // 添加默认展开的菜单键值数组 const defaultExpandedKeys = ['basic', 'capabilities', 'development', 'versions', 'monitoring'] // 菜单配置 const menuOptions = [ { label: '基础信息', key: 'basic', children: [ { label: '凭证与基础信息', key: 'basic-info', icon: () => h(NIcon, null, { default: () => h(KeyOutline) }) }, { label: '成员管理', key: 'members', icon: () => h(NIcon, null, { default: () => h(PeopleOutline) }) } ] }, { label: '应用能力', key: 'capabilities', children: [ { label: '应用能力管理', key: 'capability-manage', icon: () => h(NIcon, null, { default: () => h(ExtensionPuzzleOutline) }) } ] }, { label: '开发配置', key: 'development', children: [ { label: '权限管理', key: 'permissions', icon: () => h(NIcon, null, { default: () => h(ShieldOutline) }) }, { label: '回调配置', key: 'events', icon: () => h(NIcon, null, { default: () => h(NotificationsOutline) }) }, { label: '安全设置', key: 'security', icon: () => h(NIcon, null, { default: () => h(LockClosedOutline) }) } ] }, { label: '应用发布', key: 'versions', children: [ { label: '版本管理', key: 'publish', icon: () => h(NIcon, null, { default: () => h(RocketOutline) }) } ] }, { label: '运营监控', key: 'monitoring', children: [ { label: '日志检索', key: 'logs', icon: () => h(NIcon, null, { default: () => h(DocumentTextOutline) }) } ] } ] // 返回应用中心 const handleBack = () => { router.push('/') } // 添加面包屑点击处理 const handleBreadcrumbClick = (path) => { if (path === 'app-center') { router.push('/') } } // 处理菜单选择 const handleMenuSelect = (key) => { activeKey.value = key // 如果是基础信息页面，更新路由但保持在当前页面 if (key === 'basic-info') { router.push(`/app/${appId}`) } // TODO: 其他菜单项的路由处理 } // 修改加载应用详情函数 const loadAppDetail = async () => { try { const response = await applicationApi.getApplicationDetail(appId) appInfo.value = response currentAppId.value = response.id console.log('Current App ID set to:', currentAppId.value) } catch (error) { console.error('Failed to load app detail:', error) } } onMounted(async () => { await loadAppDetail() // 先加载应用详情 // 确认currentAppId已经设置 if (!currentAppId.value) { console.error('Failed to set currentAppId') return } // 根据当前路由设置初始选中的菜单项 const path = route.path if (path === `/app/${appId}`) { activeKey.value = 'basic-info' } }) // 获取图标组件 const getIconComponent = (iconName) => { // 这里可以复用 AppForm 中的图标映射逻辑 const iconMap = { 'LogoElectron': LogoElectron, // ... 其他图标映射 } return iconMap[iconName] || LogoElectron } // 菜单项对应的组件映射 const menuComponents = { 'basic-info': markRaw(AppBaseInfo), 'members': markRaw(AppMember), 'capability-manage': markRaw(AppAbility), 'permissions': markRaw(AppACL), 'events': markRaw(AppCallbackConfig), 'security': markRaw(AppAwl), 'publish': markRaw(AppVersion), 'logs': null } // 当前显示的组件 const currentComponent = computed(() => menuComponents[activeKey.value]) // 将currentAppId通过provide提供给子组件 provide('currentAppId', currentAppId) </script> <template> <n-layout has-sider> <!-- 顶部信息栏 --> <div class="app-header"> <n-space align="center" :size="16"> <n-button quaternary circle class="home-button" @click="handleBack"> <n-icon size="24"> <home-outline /> </n-icon> </n-button> <n-breadcrumb> <n-breadcrumb-item @click="handleBreadcrumbClick('app-center')" class="clickable-breadcrumb"> 应用中心 </n-breadcrumb-item> <n-breadcrumb-item> <n-space align="center" :size="8"> <n-icon v-if="appInfo" :component="getIconComponent(appInfo.iconName)" :color="appInfo?.iconColor" /> <span>{{ appInfo?.appName || '加载中...' }}</span> </n-space> </n-breadcrumb-item> </n-breadcrumb> </n-space> <n-space align="center"> <n-tag :type="appInfo?.appState === 'DISABLED' ? 'error' : 'success'"> {{ appInfo?.appState === 'DISABLED' ? '停止运行' : '正常运行' }} </n-tag> <!-- 信息提示区域 --> </n-space> </div> <!-- 左侧菜单 --> <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240" :native-scrollbar="false" class="app-sider"> <n-menu :value="activeKey" :options="menuOptions" :collapsed-width="64" :collapsed-icon-size="22" :default-expanded-keys="defaultExpandedKeys" @update:value="handleMenuSelect" /> </n-layout-sider> <!-- 右侧内容区 --> <n-layout class="app-content"> <!-- 根据activeKey显示对应的组件 --> <component v-if="currentComponent" :is="currentComponent" :key="activeKey" :app-info="appInfo" @update="loadAppDetail" /> <div v-else class="empty-content"> 该功能正在开发中... </div> </n-layout> </n-layout> </template> <style scoped> .app-header { height: 64px; padding: 0 24px; background-color: #fff; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; position: fixed; top: 0; left: 0; right: 0; z-index: 100; } .app-sider { position: fixed; top: 64px; left: 0; bottom: 0; background-color: #fff; } .app-content { margin-left: 240px; margin-top: 64px; padding: 24px; background-color: #f5f5f5; min-height: calc(100vh - 64px); } .home-button { width: 40px !important; height: 40px !important; display: flex; align-items: center; justify-content: center; } .clickable-breadcrumb { cursor: pointer; color: #1890ff; } .clickable-breadcrumb:hover { color: #40a9ff; } /* 添加图标样式 */ .app-icon { display: flex; align-items: center; gap: 8px; } .empty-content { display: flex; justify-content: center; align-items: center; height: 200px; color: #999; font-size: 14px; } </style><script setup> import { ref, onMounted, h, inject } from 'vue' import { NCard, NDataTable, NSpace, NButton, NIcon, NTag, useDialog } from 'naive-ui' import { PersonAddOutline } from '@vicons/ionicons5' import { useRoute } from 'vue-router' import { doGet, doPut, doPost, doDelete } from '@/utils/requests' import messages from '@/utils/messages' import ContactSelectModal from '@/components/contacts/ContactSelectModal.vue' import { applicationApi } from '@/api/applications' const route = useRoute() const appId = route.params.id const loading = ref(false) const members = ref([]) const showContactSelect = ref(false) const currentAction = ref('') // 注入currentAppId const currentAppId = inject('currentAppId') // 添加批量操作相关的状态 const checkedRowKeys = ref([]) // 定义表格列 const columns = [ { type: 'selection', disabled: (row) => row.roleId === 'OWNER', // 所有者不能被选择 align: 'center' }, { title: '姓名', key: 'memberName', ellipsis: true, align: 'center' }, { title: '角色', key: 'roleName', align: 'center', render(row) { return h(NTag, getRoleTagProps(row.roleName), { default: () => row.roleName }) } }, { title: '加入时间', key: 'createTime', align: 'center' }, { title: '操作', key: 'actions', align: 'center', render(row) { return h( NSpace, { align: 'center', justify: 'center' }, { default: () => [ row.roleId === 'OWNER' ? h( NButton, { size: 'small', quaternary: true, type: 'warning', onClick: () => handleTransferOwner(row) }, { default: () => '转移' } ) : h( NButton, { size: 'small', quaternary: true, type: 'error', onClick: () => handleRemoveMember(row) }, { default: () => '移除' } ) ] } ) } } ] // 修改角色列的渲染函数 const getRoleTagProps = (roleName) => { const baseProps = { size: 'small', round: true, bordered: false } // 根据不同角色设置不同的样式 switch (roleName) { case '所有者': return { ...baseProps, type: 'warning', color: { color: '#faad14', textColor: '#fff', borderColor: '#faad14' } } case '管理': return { ...baseProps, type: 'error', color: { color: '#ff4d4f', textColor: '#fff', borderColor: '#ff4d4f' } } case '开发': return { ...baseProps, type: 'info', color: { color: '#2080f0', textColor: '#fff', borderColor: '#2080f0' } } case '测试': return { ...baseProps, type: 'success', color: { color: '#18a058', textColor: '#fff', borderColor: '#18a058' } } default: return baseProps } } // 加载成员列表 const loadMembers = async () => { loading.value = true try { const response = await doGet(`/app-center/app/member/list?appId=${appId}`) members.value = response.data } catch (error) { messages.error('加载成员列表失败') } finally { loading.value = false } } // 添加成员按钮点击 const handleAddMember = () => { currentAction.value = 'add' showContactSelect.value = true } // 修改移除成员函数 const handleRemoveMember = (member) => { if (member.roleId === 'OWNER') return // 显示确认对话框 window.$dialog.warning({ title: '移除成员', content: `确定要移除成员 ${member.memberName} 吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await doDelete(`/app-center/app/member?ids=${member.id}`) messages.success('移除成员成功') await loadMembers() // 清空选择 checkedRowKeys.value = [] } catch (error) { console.error('Failed to remove member:', error) messages.error('移除成员失败') } } }) } // 修改批量移除函数 const handleBatchRemove = () => { if (checkedRowKeys.value.length === 0) { messages.warning('请选择要移除的成员') return } // 显示确认对话框 window.$dialog.warning({ title: '批量移除成员', content: `确定要移除选中的 ${checkedRowKeys.value.length} 名成员吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await doDelete(`/app-center/app/member?ids=${checkedRowKeys.value.join(',')}`) messages.success('批量移除成员成功') await loadMembers() // 清空选择 checkedRowKeys.value = [] } catch (error) { console.error('Failed to batch remove members:', error) messages.error('批量移除成员失败') } } }) } // 添加转移所有者处理函数 const handleTransferOwner = (member) => { currentAction.value = 'transfer' showContactSelect.value = true } // 修改添加成员选择处理函数 const handleContactSelect = async (selected) => { if (currentAction.value === 'add') { try { console.log('Current App ID:', currentAppId.value) if (!currentAppId.value) { messages.error('应用ID未找到') return } const promises = selected.map(user => { const params = { memberId: user.id, memberName: user.nickname, appId: currentAppId.value, roleId: 'DEVELOPER', roleName: '开发' } console.log('Adding member with params:', params) return doPost('/app-center/app/member', params) }) await Promise.all(promises) messages.success('添加成员成功') await loadMembers() } catch (error) { console.error('Failed to add members:', error) messages.error('添加成员失败') } } else if (currentAction.value === 'transfer') { try { await applicationApi.updateApplication(currentAppId.value, { ownerId: selected[0].id, ownerName: selected[0].nickname }) messages.success('转移所有者成功') await loadMembers() } catch (error) { messages.error('转移所有者失败') } } } // 在setup中初始化dialog const dialog = useDialog() window.$dialog = dialog onMounted(async () => { if (!currentAppId.value) { console.warn('currentAppId is not set') } await loadMembers() }) </script> <template> <div class="app-member"> <n-card title="成员管理"> <template #header-extra> <n-space> <n-button v-if="checkedRowKeys.length > 0" type="error" @click="handleBatchRemove" > 批量移除 </n-button> <n-button type="primary" @click="handleAddMember"> <template #icon> <n-icon> <person-add-outline /> </n-icon> </template> 添加成员 </n-button> </n-space> </template> <n-data-table :columns="columns" :data="members" :loading="loading" :pagination="{ pageSize: 10 }" :bordered="false" @update:checked-row-keys="checkedRowKeys = $event" :checked-row-keys="checkedRowKeys" :row-key="(row) => row.id" /> </n-card> <!-- 添加成员选择弹窗 --> <contact-select-modal v-model:show="showContactSelect" :title="currentAction === 'add' ? '添加成员' : '转移所有者'" :description="currentAction === 'add' ? '最多可选择10名成员' : '请选择要转移给的成员'" :label="currentAction === 'add' ? '成员' : '转移对象'" :multiple="currentAction === 'add'" :max-selected="currentAction === 'add' ? 10 : 1" @confirm="handleContactSelect" /> </div> </template> <style scoped> .app-member { padding: 24px; } </style><template> <div class="page-container"> <n-card> <div class="header"> <div class="header-left"> <h2>版本管理</h2> <p class="description">管理应用的版本信息</p> </div> <div class="header-right"> <n-button type="primary" @click="handleCreateVersion"> 创建版本 </n-button> </div> </div> <n-data-table :columns="columns" :data="versionList" :loading="loading" :pagination="pagination" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" /> </n-card> <!-- 创建版本弹窗 --> <n-modal v-model:show="showCreateModal" preset="dialog" title="创建版本" positive-text="确认" negative-text="取消" @positive-click="handleConfirmCreate" @negative-click="handleCancelCreate" > <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="80" require-mark-placement="right-hanging" > <n-form-item label="版本号" path="version"> <n-input v-model:value="formModel.version" placeholder="请输入版本号，如：1.0.0" /> </n-form-item> <n-form-item label="版本说明" path="description"> <n-input v-model:value="formModel.description" type="textarea" placeholder="请输入版本说明" :autosize="{ minRows: 3, maxRows: 5 }" /> </n-form-item> </n-form> </n-modal> </div> </template> <script setup> import { ref, onMounted, inject, h } from 'vue' import { NTag } from 'naive-ui' import { doGet, doPost } from '@/utils/requests' import messages from '@/utils/messages' const currentAppId = inject('currentAppId') // 列表相关 const loading = ref(false) const versionList = ref([]) const pagination = ref({ page: 1, pageSize: 10, itemCount: 0, showSizePicker: true, pageSizes: [10, 20, 30, 40] }) // 表格列定义 const columns = [ { title: '版本号', key: 'versionCode', width: 120 }, { title: '版本说明', key: 'versionDesc', ellipsis: { tooltip: true } }, { title: '版本类型', key: 'versionType', width: 100, render(row) { return h( NTag, { type: row.versionType === 'DEVELOP' ? 'info' : 'success', size: 'small' }, { default: () => row.versionType === 'DEVELOP' ? '开发版' : '正式版' } ) } }, { title: '提交状态', key: 'submitState', width: 100, render(row) { const stateMap = { 'DONE': { type: 'success', text: '已完成' }, 'PENDING': { type: 'warning', text: '待审核' }, 'REJECTED': { type: 'error', text: '已驳回' } } const state = stateMap[row.submitState] || { type: 'default', text: row.submitState } return h( NTag, { type: state.type, size: 'small' }, { default: () => state.text } ) } }, { title: '提交人', key: 'submitUserName', width: 120 }, { title: '创建时间', key: 'createTime', width: 180 } ] // 加载版本列表 const loadVersionList = async () => { loading.value = true try { const res = await doGet( `/app-center/app/version/page/${currentAppId.value}`, { page: pagination.value.page, size: pagination.value.pageSize } ) if (res.code === 0) { versionList.value = res.data.records pagination.value.itemCount = res.data.totalRow pagination.value.page = res.data.pageNumber pagination.value.pageSize = res.data.pageSize } } catch (error) { messages.error('加载版本列表失败') } finally { loading.value = false } } // 处理分页变化 const handlePageChange = (page) => { pagination.value.page = page loadVersionList() } const handlePageSizeChange = (pageSize) => { pagination.value.pageSize = pageSize pagination.value.page = 1 loadVersionList() } // 创建版本相关 const showCreateModal = ref(false) const formRef = ref(null) const formModel = ref({ version: '', description: '' }) const rules = { version: { required: true, message: '请输入版本号', trigger: 'blur' }, description: { required: true, message: '请输入版本说明', trigger: 'blur' } } const handleCreateVersion = () => { showCreateModal.value = true formModel.value = { version: '', description: '' } } const handleConfirmCreate = async (e) => { e.preventDefault() try { await formRef.value?.validate() const res = await doPost(`/app-center/app/${currentAppId.value}/version`, formModel.value) if (res.code === 0) { messages.success('创建成功') showCreateModal.value = false loadVersionList() } } catch (error) { // 表单验证失败或请求失败 } } const handleCancelCreate = () => { showCreateModal.value = false } onMounted(() => { loadVersionList() }) </script> <style scoped> .page-container { padding: 24px; background-color: #f5f7fa; min-height: 100vh; } .header { margin-bottom: 24px; display: flex; justify-content: space-between; align-items: flex-start; } .header h2 { margin: 0; font-size: 20px; font-weight: 500; color: #333; } .description { margin: 8px 0 0; color: #666; font-size: 14px; } </style><template> <div class="perception-trial"> <n-card> <!-- 添加顶部操作栏 --> <div class="trial-header"> <div class="left-actions"> <n-button quaternary circle @click="$emit('close')"> <template #icon> <n-icon> <ArrowLeftOutline /> </n-icon> </template> </n-button> <span class="page-title">千诀·感知模型全家桶</span> </div> <div class="right-actions"> <n-button type="success" v-if="!is_ability_added" @click="handleAddAbility"> 添加能力 </n-button> <n-button type="error" v-else @click="handleRemoveAbility"> 删除能力 </n-button> </div> </div> <!-- 原有的输入区域 --> <div class="input-area"> <!-- 左侧图片上传 --> <div class="upload-section"> <!-- 添加 tab 切换 --> <n-tabs v-model:value="active_tab" type="segment" size="large"> <n-tab-pane name="2d" tab="2D"> <div class="upload-box"> <n-upload accept="image/*" :show-file-list="false" @change="handleImageUpload" @drop.prevent="handleDrop" :before-upload="beforeUpload"> <div class="upload-trigger"> <n-icon size="48" class="upload-icon"> <image-outline /> </n-icon> <p class="upload-text">点击上传RGB图片或拖拽到此处</p> <div class="image-preview" v-if="image_url"> <img :src="image_url" alt="预览图" /> <div class="image-actions"> <n-button quaternary circle class="remove-image" @click.stop="removeImage"> <template #icon> <n-icon> <TrashOutline /> </n-icon> </template> </n-button> <n-button quaternary circle class="zoom-image" @click.stop="showLargeImage(image_url)"> <template #icon> <n-icon> <SearchOutline /> </n-icon> </template> </n-button> </div> </div> </div> </n-upload> </div> </n-tab-pane> <n-tab-pane name="3d" tab="3D"> <div class="upload-box-container upload-box-container-3d"> <div class="upload-box"> <n-upload accept="image/*" :show-file-list="false" @change="handleRGBImageUpload" @drop.prevent="handleRGBDrop" :before-upload="beforeUpload"> <div class="upload-trigger"> <n-icon size="48" class="upload-icon"> <image-outline /> </n-icon> <p class="upload-text">点击上传RGB图片或拖拽到此处</p> <div class="image-preview" v-if="rgb_image_url"> <img :src="rgb_image_url" alt="RGB预览图" /> <div class="image-actions"> <n-button quaternary circle class="remove-image" @click.stop="removeRGBImage"> <template #icon> <n-icon> <TrashOutline /> </n-icon> </template> </n-button> <n-button quaternary circle class="zoom-image" @click.stop="showLargeImage(rgb_image_url)"> <template #icon> <n-icon> <SearchOutline /> </n-icon> </template> </n-button> </div> </div> </div> </n-upload> </div> <div class="upload-box"> <n-upload accept="image/*" :show-file-list="false" @change="handleRGBDImageUpload" @drop.prevent="handleRGBDDrop" :before-upload="beforeUpload"> <div class="upload-trigger"> <n-icon size="48" class="upload-icon"> <image-outline /> </n-icon> <p class="upload-text">点击上传深度图片或拖拽到此处</p> <div class="image-preview" v-if="rgbd_image_url"> <img :src="rgbd_image_url" alt="RGBD预览图" /> <div class="image-actions"> <n-button quaternary circle class="remove-image" @click.stop="removeRGBDImage"> <template #icon> <n-icon> <TrashOutline /> </n-icon> </template> </n-button> <n-button quaternary circle class="zoom-image" @click.stop="showLargeImage(rgbd_image_url)"> <template #icon> <n-icon> <SearchOutline /> </n-icon> </template> </n-button> </div> </div> </div> </n-upload> </div> </div> </n-tab-pane> </n-tabs> </div> <!-- 中间功能选择和文本输入区 --> <div class="input-section"> <!-- 功能选择 --> <n-select v-model:value="selected_function" :options="function_options" placeholder="请选择功能" class="function-select" /> <!-- 单行文本 --> <n-input v-model:value="object_names" type="text" placeholder="（必填）请输入要识别的物品名称，多个物品请用逗号分隔，例如：苹果,香蕉,橘子" class="single-line-input" /> <!-- 修改多行文本输入框 --> <n-input v-model:value="questions" type="textarea" :placeholder="multi_line_placeholder" :rows="7" class="multi-line-input" :disabled="shouldDisableQuestions" /> </div> <!-- 右侧发送按钮和任务信息 --> <div class="action-section"> <n-button type="primary" :disabled="!can_submit || submitting" @click="handleSubmit" class="send-button"> <template #icon> <n-icon> <PaperPlaneOutline /> </n-icon> </template> {{ submitting ? '处理中' : '发送' }} </n-button> <!-- 将任务信息移到这里 --> <div class="task-info" v-if="taskInfo"> <div class="task-info-item"> <span class="label">任务ID</span> <span class="value"> {{ taskInfo.taskId }} <n-button text size="tiny" @click="() => copyToClipboard(taskInfo.taskId)"> <template #icon> <n-icon> <CopyOutline /> </n-icon> </template> </n-button> </span> </div> <div class="task-info-item"> <span class="label">请求ID</span> <span class="value"> {{ taskInfo.traceId }} <n-button text size="tiny" @click="() => copyToClipboard(taskInfo.traceId)"> <template #icon> <n-icon> <CopyOutline /> </n-icon> </template> </n-button> </span> </div> <div class="task-info-item"> <span class="label">任务状态</span> <n-tag :type="getStatusType">{{ taskInfo.taskStatus }}</n-tag> </div> <!-- 新增操作区域 --> <div class="task-info-item" v-if="showActionButton"> <span class="label">可执行操作</span> <div class="action-buttons"> <n-button v-if="taskInfo.taskStatus === 'RUNNING' || taskInfo.taskStatus === 'PROCESSING'" type="warning" size="small" @click="handleCancelTask" class="cancel-button" > 取消任务 </n-button> <n-button v-if="taskInfo.taskStatus === 'TIMEOUT'" type="primary" size="small" @click="retryPolling" :loading="actionLoading" > 重试任务 </n-button> </div> </div> </div> </div> </div> <!-- 修改结果区域的显示部分 --> <div class="result-container"> <n-collapse-transition :show="loading"> <div class="result-area loading"> <n-spin size="medium"> <template #description>拼命处理中...</template> </n-spin> </div> </n-collapse-transition> <n-collapse-transition :show="!!taskResult"> <div class="result-area" v-if="taskResult"> <n-divider>执行结果</n-divider> <div class="result-summary"> {{ getTaskResultTitle }} </div> <!-- 修改角度图片显示的条件判断 --> <div v-if="taskResult.angleImage && taskResult.angleImage !== ''" class="additional-image-container"> <div class="image-title">角度预测结果</div> <div class="image-wrapper"> <img :src="taskResult.angleImage" alt="角度预测图" /> </div> </div> <!-- 修改抓取点图片显示的条件判断 --> <div v-if="taskResult.graspImage && taskResult.graspImage !== ''" class="additional-image-container"> <div class="image-title">抓取点预测结果</div> <div class="image-wrapper"> <img :src="taskResult.graspImage" alt="抓取点预测图" /> </div> </div> <ResultCards :result="taskResult" :images-list="getImagesList" @mounted="() => console.log('TaskResult:', taskResult)" @retry="retryPolling" /> </div> </n-collapse-transition> </div> </n-card> <n-modal v-model:show="showModal" preset="card" style="width: 80%; max-width: 1200px;"> <div class="large-image-container"> <img :src="currentLargeImage" alt="大图预览" /> </div> </n-modal> </div> </template> <script setup> import { ref, computed, watch, onMounted, h, onUnmounted } from 'vue' import { ArrowBack as ArrowLeftOutline, ImageOutline, TrashOutline, PaperPlaneOutline, CopyOutline, SearchOutline } from '@vicons/ionicons5' import { inject } from 'vue' import { doPost, doPut, doGet } from '@/utils/requests' import messages from '@/utils/messages' import { getUploadParams } from '@/api/base' import { NSpin, NDivider, NCollapseTransition, NCard, NTag, NModal } from 'naive-ui' import TypewriterText from '@/components/TypewriterText.vue' import ResultCards from '@/components/ResultCards.vue' // 注入必要的数据 const currentAppId = inject('currentAppId') // props 定义要放在最前面 const props = defineProps({ ability_added: { type: Boolean, required: true }, ability_id: { type: [String, Number], required: true }, taskType: { type: String, required: true } }) // 基础状态 const is_ability_added = ref(props.ability_added) const image_url = ref('') const selected_function = ref(null) const object_names = ref('') const questions = ref('') const function_options = ref([]) const current_request = ref({ method: '', path: '' }) // 图片相关状态 const active_tab = ref('2d') const rgb_image_url = ref('') const rgbd_image_url = ref('') // 任务相关状态 const loading = ref(false) const taskResult = ref(null) const polling = ref(null) // 添加提交状态 const submitting = ref(false) // 在 script setup 中添加新的响应状态变量 const taskInfo = ref(null) const pollingCount = ref(0) const MAX_POLLING_COUNT = 30 // 添加操作按钮加载状态 const actionLoading = ref(false) // 添加是否显示操作按钮的计算属性 const showActionButton = computed(() => { return (taskInfo.value?.taskStatus === 'RUNNING' || taskInfo.value?.taskStatus === 'PROCESSING' || taskInfo.value?.taskStatus === 'TIMEOUT') }) // 监听 props 变化 watch(() => props.ability_added, (newValue) => { is_ability_added.value = newValue }) // 发射关闭事件，替代原来的closeTrial方法 defineEmits(['close']) // 修改 API 配置映射 const API_CONFIG = { // 全功能 'app:perception:full': { title: '识别出', imageField: 'croppedImagesListBbox', questions: false // 允许输入问题 }, // 检测 'app:perception:check': { title: '检测到', imageField: 'croppedImagesListBbox', questions: false }, // 分割 'app:perception:split': { title: '分割出', imageField: 'croppedImagesListSegment', questions: false }, // 属性描述 'app:perception:props-describe': { title: '识别出', imageField: 'croppedImagesListBbox', questions: false // 允许输入问题 }, // 角度预测 'app:perception:angle-prediction': { title: '识别出角度', imageField: 'croppedImagesListAngle', questions: false }, // 抓取点预测 'app:perception:grab-point-prediction': { title: '识别出抓取点', imageField: 'croppedImagesListGrasp', questions: false }, // 关键点预测 'app:perception:key-point-prediction': { title: '识别出关键点', imageField: 'croppedImagesListPoint', questions: false } } // 修改获取任务结果标题的计算属性 const getTaskResultTitle = computed(() => { if (!taskResult.value?.labels?.length) return '' // 从当前选择的功能中获取 apiCode const selectedOption = function_options.value.find(opt => opt.value === selected_function.value) const apiCode = selectedOption?.apiCode // 获取对应的配置 const config = API_CONFIG[apiCode] || { title: '识别出' } // 对标签进行分组计数 const labelCounts = taskResult.value.labels.reduce((acc, label) => { acc[label] = (acc[label] || 0) + 1 return acc }, {}) // 将分组结果转换为描述文本 const labelDescription = Object.entries(labelCounts) .map(([name, count]) => `${count}个${name}`) .join('，') return `为您${config.title} ${labelDescription}` }) // 修改加载功能列表的方法，确保保存 apiCode const loadFunctionOptions = async () => { const res = await doGet('/app-center/app/ability/api/list', { ability_id: props.ability_id }) if (res.code === 0) { function_options.value = res.data.map(item => ({ label: item.apiName, value: item.apiName, requestMethod: item.requestMethod, requestPath: item.requestPath, apiCode: item.apiCode // 保存 apiCode })) // 如果有选项，默认选中第一项 if (function_options.value.length > 0) { selected_function.value = function_options.value[0].value } } } // 在组件挂载时加载功能列表 onMounted(() => { loadFunctionOptions() }) // 监听功能选择变化 watch(selected_function, (newValue) => { if (newValue) { // 找到选中的功能选项 const selectedOption = function_options.value.find(opt => opt.value === newValue) if (selectedOption) { // 更请求信息 current_request.value = { method: selectedOption.requestMethod, path: selectedOption.requestPath } // 如果是检测任务，清空问题 if (selectedOption.value === '提交检测任务') { questions.value = '' } } } else { // 清空请求信息 current_request.value = { method: '', path: '' } } }) // 修改是否可以提交的计算属性 const can_submit = computed(() => { // 检查基本条件：必须有功选择和物品名称 const has_basic_requirements = selected_function.value && object_names.value if (!has_basic_requirements) return false // 根模式检查 if (active_tab.value === '2d') { // 2D模式：必须有普通图片 return Boolean(image_url.value) } else { // 3D模式：必须同时有RGB和RGBD图片 return Boolean(rgb_image_url.value && rgbd_image_url.value) } }) // 添加文件大小限制常量（10MB） const MAX_FILE_SIZE = 10 * 1024 * 1024 // 修改文件处理方法 const handleFileUpload = (file, type) => { if (!file || !(file instanceof File)) return // 检查文件大小 if (file.size > MAX_FILE_SIZE) { messages.error('图片大小不能超过10MB') return } const reader = new FileReader() reader.onload = (e) => { switch (type) { case 'image': image_url.value = e.target.result break case 'rgb': rgb_image_url.value = e.target.result break case 'rgbd': rgbd_image_url.value = e.target.result break } } reader.readAsDataURL(file) } // 改上传组件的 before-upload 属性 const beforeUpload = (file) => { if (file.size > MAX_FILE_SIZE) { messages.error('图片大小不能超过10MB') return false } return true } // 修改拖拽处理方法 const handleDrop = (e) => { const file = e.file?.file if (file) handleFileUpload(file, 'image') } const handleRGBDrop = (e) => { const file = e.file?.file if (file) handleFileUpload(file, 'rgb') } const handleRGBDDrop = (e) => { const file = e.file?.file if (file) handleFileUpload(file, 'rgbd') } // 删除图片方法 const removeImage = () => { image_url.value = '' } const removeRGBImage = () => { rgb_image_url.value = '' } const removeRGBDImage = () => { rgbd_image_url.value = '' } // 处理图片上传 const uploadImage = async (base64Data) => { if (!base64Data) return null try { // 获取上传参数 const uploadParams = await getUploadParams() if (uploadParams.code !== 0) { console.error('获取上传参数失败:', uploadParams) throw new Error('获取上传参数失败') } // 将base64转换为文件 const binaryData = atob(base64Data.split(',')[1]) const array = new Uint8Array(binaryData.length) for (let i = 0; i < binaryData.length; i++) { array[i] = binaryData.charCodeAt(i) } const blob = new Blob([array], { type: 'image/png' }) const file = new File([blob], 'image.png', { type: 'image/png' }) // 构建FormData const formData = new FormData() formData.append('token', uploadParams.data.token) formData.append('key', uploadParams.data.key) formData.append('file', file) console.log('上传参数:', { url: uploadParams.data.host, token: uploadParams.data.token, key: uploadParams.data.key, }) // 发送上传请求 const response = await fetch(uploadParams.data.host, { method: 'POST', body: formData }) console.log('上传响应状态:', response.status) // 如果响应不是204尝试读取响应内容 if (response.status !== 204) { const responseText = await response.text() console.error('上传失败响应内容:', responseText) throw new Error(`上传失败: ${response.status} ${responseText}`) } // 修改这里：返回完整的URL return uploadParams.data.key } catch (error) { console.error('上传程出错:', error) throw error } } // 修改提交处理方法 const handleSubmit = async () => { if (!selected_function.value || !current_request.value.path) return // 检查必填项 if (!object_names.value) { messages.error('请输入要识别的物品名称') return } if (active_tab.value === '3d' && !rgbd_image_url.value) { messages.error('3D模式下需要同时上传RGB图片和深度图片') return } try { submitting.value = true // 开始提交，禁用按钮 loading.value = true taskResult.value = null // 初始化任务信息，立即显示处理中状态 taskInfo.value = { taskId: '等待响应...', taskStatus: '正在提交...', traceId: '等响应...' } // 构建基础请求数据 const request_data = { object_names: object_names.value, image_type: active_tab.value.toUpperCase() } if (questions.value) { request_data.questions = questions.value } // 根据当前模式上传图片 if (active_tab.value === '2d') { if (image_url.value) { const imageUrl = await uploadImage(image_url.value) request_data.imageUrl = imageUrl // 直接使用完整的URL } else { messages.error('请上传图片') return } } else { if (rgb_image_url.value && rgbd_image_url.value) { const [imageUrl, depthUrl] = await Promise.all([ uploadImage(rgb_image_url.value), uploadImage(rgbd_image_url.value) ]) request_data.imageUrl = imageUrl // 直接使用完整的URL request_data.depthUrl = depthUrl // 直接使用完整的URL } else { messages.error('请同时上传RGB图片和深度图') return } } console.log('发送给后端的数据:', request_data) // 添加日志 // 发送请求并处理响应 const api_path = `/app-center${current_request.value.path}` const res = await doPost(api_path, request_data) if (res.code === 0) { if (res.data.taskStatus === 'SUBMIT_FAILED') { // 如果提交就失败了，直接显示提交失败状态 taskInfo.value = { taskId: res.data.taskId, taskStatus: '提交失败', traceId: res.headers?.['x-trace-id'] || '未获取' } loading.value = false submitting.value = false } else if (res.data.taskId) { // 正常提交成功，开始轮询 taskInfo.value = { taskId: res.data.taskId, taskStatus: '处理中', traceId: res.headers?.['x-trace-id'] || '未获取' } startPolling(res.data.taskId) } } else { messages.error('提交失败') loading.value = false submitting.value = false taskInfo.value = null } } catch (error) { loading.value = false submitting.value = false taskInfo.value = null } } // 修改取消任务的方法 const handleCancelTask = async () => { if (!taskInfo.value?.taskId) return try { actionLoading.value = true // 直接更新任务状态为 CANCELED await doPut('/app-center/app/task/record', { taskId: taskInfo.value.taskId, taskStatus: 'CANCELED' }) messages.success('任务已取消') // 终止轮询 clearInterval(polling.value) // 重置状态 loading.value = false submitting.value = false if (taskInfo.value) { taskInfo.value.taskStatus = 'CANCELED' } // 重置表单 resetForm() } catch (error) { messages.error('取消任务失败') } finally { actionLoading.value = false } } // 添加多行文本占位符 const multi_line_placeholder = `（可用时必填）可在此针对每个物体询问其状态，例如： 苹果: 是什么形状的？ 香蕉: 是什么颜色的？ ` // 添加图片上传处理方法 const handleImageUpload = ({ file }) => { handleFileUpload(file.file, 'image') } const handleRGBImageUpload = ({ file }) => { handleFileUpload(file.file, 'rgb') } const handleRGBDImageUpload = ({ file }) => { handleFileUpload(file.file, 'rgbd') } // 修改 shouldDisableQuestions 计算属 const shouldDisableQuestions = computed(() => { const selectedOption = function_options.value.find(opt => opt.value === selected_function.value) const apiCode = selectedOption?.apiCode const config = API_CONFIG[apiCode] // 如果没有配置或 questions 不为 true，则禁用输入框 return !config?.questions }) // 修改获取图片列表的计算属性 const getImagesList = computed(() => { if (!taskResult.value) return [] // 从当前选择的功能中获取 apiCode const selectedOption = function_options.value.find(opt => opt.value === selected_function.value) const apiCode = selectedOption?.apiCode // 获取对应的配置 const config = API_CONFIG[apiCode] if (!config) return [] // 返回对应字段的图片列表 return taskResult.value[config.imageField] || [] }) // 添加复制方法 const copyToClipboard = async (text) => { try { await navigator.clipboard.writeText(text) messages.success('复制成功') } catch (err) { messages.error('复制失败') } } // 同时修改状态样式计算属性，添加失败状态 const getStatusType = computed(() => { if (!taskInfo.value) return 'default' switch (taskInfo.value.taskStatus) { case 'DONE': return 'success' case 'TIMEOUT': return '超时,请重试' case '失败': case '提交失败': // 添加提交失败状态 return 'error' case '处理中': return 'info' default: return 'default' } }) // 添加大图预览相关的响应式变量 const showModal = ref(false) const currentLargeImage = ref('') // 添加显示大图的方法 const showLargeImage = (imageUrl) => { currentLargeImage.value = imageUrl showModal.value = true } // 添加能力方法 const handleAddAbility = async () => { try { const res = await doPost('/app-center/app/acl', { appId: currentAppId.value, abilityId: props.ability_id }) if (res.code === 0) { messages.success('添加成功') is_ability_added.value = true } else { messages.error(res.msg || '添加失败') } } catch (error) { messages.error('添加失败') } } // 删除能力方法 const handleRemoveAbility = async () => { try { const res = await doPut('/app-center/app/acl', { appId: currentAppId.value, abilityId: props.ability_id }) if (res.code === 0) { messages.success('删除成功') is_ability_added.value = false } else { messages.error(res.msg || '删除失败') } } catch (error) { messages.error('删除失败') } } // 添加轮询相关函数 const startPolling = async (taskId) => { // 清除可能存在的之前的轮询 if (polling.value) { clearInterval(polling.value) } // 重置轮询计数 pollingCount.value = 0 // 创建新的轮询 polling.value = setInterval(async () => { try { pollingCount.value++ // 检查是否超过最大轮询次数 if (pollingCount.value > MAX_POLLING_COUNT) { clearInterval(polling.value) taskInfo.value.taskStatus = 'TIMEOUT' loading.value = false submitting.value = false return } // 获取任务状态 const res = await doGet('/open-apis/app/perception/result?task_id=' + taskId) if (res.code === 0) { // 更新任务状态 taskInfo.value = { ...taskInfo.value, taskStatus: res.data.taskStatus } // 根据任务状态处理 switch (res.data.taskStatus) { case 'DONE': // 任务完成 clearInterval(polling.value) taskResult.value = res.data.taskResult loading.value = false submitting.value = false break case 'FAILED': // 任务失败 clearInterval(polling.value) messages.error('任务执行失败') loading.value = false submitting.value = false break case 'CANCELED': // 任务被取消 clearInterval(polling.value) loading.value = false submitting.value = false break } } else { // 请求出错 clearInterval(polling.value) messages.error('获取任务状态失败') loading.value = false submitting.value = false } } catch (error) { console.error('轮询出错:', error) clearInterval(polling.value) loading.value = false submitting.value = false } }, 2000) // 每2秒轮询一次 } // 添加重试轮询的方法 const retryPolling = async () => { if (!taskInfo.value?.taskId) return try { actionLoading.value = true // 重置任务状态 taskInfo.value.taskStatus = '处理中' loading.value = true // 重新开始轮询 startPolling(taskInfo.value.taskId) } catch (error) { messages.error('重试失败') loading.value = false } finally { actionLoading.value = false } } // 在组件卸载时清除轮询 onUnmounted(() => { if (polling.value) { clearInterval(polling.value) } }) </script> <style scoped> .perception-trial { width: 100%; } .trial-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; padding-bottom: 16px; border-bottom: 1px solid #eee; } .left-actions { display: flex; align-items: center; gap: 12px; } .page-title { font-size: 16px; font-weight: 500; color: #333; } .right-actions { display: flex; align-items: center; } .input-area { display: flex; gap: 20px; height: 320px; width: 100%; margin-left: 0; } .upload-section { flex: 3; min-width: 300px; height: 100%; display: flex; flex-direction: column; } :deep(.n-tabs) { height: 100%; display: flex; flex-direction: column; } :deep(.n-tabs-content) { flex: 1; height: 100%; } :deep(.n-tab-pane) { height: 100%; padding: 0; } .upload-box-container { display: flex; flex-direction: column; gap: 16px; height: 100%; } .upload-box { height: 100%; flex: 1; border: 2px dashed #e5e7eb; border-radius: 8px; padding: 16px; text-align: center; cursor: pointer; transition: all 0.3s; display: flex; flex-direction: column; box-sizing: border-box; align-items: center; justify-content: center; position: relative; } .upload-box:hover { border-color: #18a058; } .upload-trigger { flex: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; position: relative; width: 100%; height: 100%; pointer-events: none; } .upload-icon { color: #909399; margin-bottom: 8px; pointer-events: none; } .upload-text { color: #909399; font-size: 14px; margin: 0; pointer-events: none; } .image-preview { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: #f8f9fa; border-radius: 4px; overflow: hidden; pointer-events: none; } .image-preview img { width: 100%; height: 100%; object-fit: contain; pointer-events: none; } .image-actions { position: absolute; top: 8px; right: 8px; z-index: 1; display: flex; gap: 8px; pointer-events: auto; } .remove-image { width: 24px; height: 24px; padding: 0; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(4px); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); } .remove-image:hover { background-color: rgba(255, 255, 255, 1); box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); } .remove-image .n-icon { font-size: 16px; color: #d03050; } .zoom-image { width: 24px; height: 24px; padding: 0; background-color: rgba(255, 255, 255, 0.9); backdrop-filter: blur(4px); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); } .zoom-image:hover { background-color: rgba(255, 255, 255, 1); box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); } .zoom-image .n-icon { font-size: 16px; color: #2080f0; } .input-section { flex: 4; min-width: 400px; height: 100%; display: flex; flex-direction: column; gap: 16px; } .function-select { width: 100%; } .single-line-input, .multi-line-input { width: 100%; } .action-section { flex: 3; min-width: 300px; max-width: none; height: 100%; display: flex; flex-direction: column; gap: 16px; box-sizing: border-box; } .send-button { width: 100%; height: 40px; box-sizing: border-box; } .send-button .n-icon { font-size: 16px; } /* 优化一下按钮样式 */ .n-button { display: flex; align-items: center; justify-content: center; } .n-button.n-button--quaternary { width: 32px; height: 32px; padding: 0; } /* 图标样式 */ .n-icon { font-size: 18px; } /* Tab 样式优化 */ :deep(.n-tabs-nav) { margin-bottom: 8px; flex-shrink: 0; } :deep(.n-tab-pane) { padding: 0; } /* 简化功能选项样式 */ .function-option { padding: 8px 0; font-size: 14px; } /* 移除不需要的样式 */ .function-detail, .method-tag, .path-text { display: none; } /* 修改下拉框样式 */ .function-select { width: 100%; } /* 设置下拉框文字居中 */ :deep(.n-base-selection-label) { justify-content: center; text-align: center; } :deep(.n-base-selection-placeholder) { justify-content: center; text-align: center; } :deep(.n-base-selection-input) { text-align: center; } /* 设置下拉选项文字居中 */ :deep(.n-select-option-body) { justify-content: center !important; } :deep(.n-select-option__content) { text-align: center; width: 100%; } /* 设置选项激活和hover状态下的样式也保持居中 */ :deep(.n-select-option.n-select-option--selected) .n-select-option__content { text-align: center; width: 100%; } :deep(.n-select-option:hover) .n-select-option__content { text-align: center; width: 100%; } /* 确保下拉框和单行输入框高度一致 */ .function-select, .single-line-input { height: 34px; } /* 多行文本框独立布局 */ .multi-line-input { flex: 1; margin-top: 16px; } .result-container { margin-top: 20px; } .result-area { width: 100%; } .result-area.loading { display: flex; justify-content: center; padding: 40px 0; } .result-code { background: #f5f7fa; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: monospace; font-size: 14px; line-height: 1.5; margin: 0; } .result-summary { text-align: center; font-size: 16px; color: #18a058; margin-bottom: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px; font-weight: 500; } /* 添加禁用状态的样式 */ .multi-line-input:disabled { background-color: #f5f7fa; cursor: not-allowed; } .task-info { width: 100%; padding: 20px; background: #f9f9f9; border-radius: 6px; border: 1px solid #eee; font-size: 16px; box-sizing: border-box; flex: 1; display: flex; flex-direction: column; justify-content: flex-start; } .task-info-item { display: flex; flex-direction: row; align-items: center; justify-content: space-between; margin-bottom: 20px; width: 100%; } .task-info-item .label { min-width: 84px; color: #666; flex-shrink: 0; font-size: 16px; font-weight: 500; } .task-info-item .value { flex: 1; min-width: 0; display: flex; align-items: center; justify-content: flex-end; gap: 8px; font-size: 16px; } .task-info-item .value span { text-align: right; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; } /* 在1920px以下时改变布局和字号 */ @media screen and (max-width: 1920px) { .task-info { padding: 16px; font-size: 14px; } .task-info-item { flex-direction: column; align-items: flex-start; gap: 4px; margin-bottom: 12px; } .task-info-item .label { width: 100%; margin-bottom: 2px; font-size: 14px; } .task-info-item .value { width: 100%; justify-content: flex-start; font-size: 14px; } .task-info-item .value span { text-align: left; } /* 调整复制按钮大小 */ .task-info-item .n-button { padding: 2px; height: 20px; width: 20px; min-width: 20px; margin-left: 4px; } .task-info-item .n-button .n-icon { font-size: 14px; } /* 调整标签样式 */ :deep(.n-tag) { font-size: 14px; padding: 4px 12px; } } .task-info-item:last-child { margin-bottom: 0; } .task-info-item .label { color: #666; flex-shrink: 0; font-size: 16px; font-weight: 500; } .task-info-item .value { flex: 1; min-width: 0; display: flex; align-items: center; gap: 8px; font-size: 16px; } .task-info-item .value span { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; } /* 调整复制按钮样式 */ .task-info-item .n-button { padding: 4px; height: 24px; width: 24px; min-width: 24px; flex-shrink: 0; margin-left: 8px; } .task-info-item .n-button .n-icon { font-size: 16px; } /* 调整标签样式 */ :deep(.n-tag) { font-size: 16px; padding: 6px 16px; border-radius: 4px; } /* 修改上传触发区域的样式 */ :deep(.n-upload-trigger) { display: flex; align-items: center; justify-content: center; height: 100%; width: 100%; } :deep(.n-upload) { height: 100%; width: 100%; } :deep(.n-upload-trigger-area) { height: 100%; width: 100%; } .upload-box { height: 100%; flex: 1; border: 2px dashed #e5e7eb; border-radius: 8px; padding: 16px; text-align: center; cursor: pointer; transition: all 0.3s; display: flex; flex-direction: column; box-sizing: border-box; align-items: center; justify-content: center; position: relative; } .upload-trigger { flex: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; position: relative; width: 100%; height: 100%; pointer-events: none; } .upload-icon, .upload-text { pointer-events: none; } /* 添加3D模式特定的样式 */ .upload-box-container-3d { flex-direction: column; gap: 8px; height: 100%; overflow: hidden; } .upload-box-container-3d .upload-box { flex: 1; min-height: 0; height: calc(50% - 4px); padding: 8px; } /* 调整上传框内部元素的样式 */ .upload-box-container-3d .upload-icon { font-size: 24px; margin-bottom: 4px; } .upload-box-container-3d .upload-text { font-size: 12px; margin: 0; line-height: 1.2; } /* 调整预览图片容器在3D模式下的样式 */ .upload-box-container-3d .image-preview { padding: 4px; } .additional-image-container { margin: 20px 0; background: #fff; border-radius: 8px; padding: 16px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); } .image-title { font-size: 16px; font-weight: 500; color: #333; margin-bottom: 12px; text-align: center; } .image-wrapper { width: 100%; display: flex; justify-content: center; align-items: center; overflow: hidden; border-radius: 4px; } .image-wrapper img { max-width: 100%; height: auto; object-fit: contain; } /* 添加响应式样式 */ @media screen and (max-width: 1920px) { .image-title { font-size: 14px; margin-bottom: 8px; } .additional-image-container { margin: 16px 0; padding: 12px; } } /* 添加操作按钮样式 */ .action-buttons { display: flex; gap: 8px; justify-content: flex-end; flex: 1; } /* 修改按钮样式 */ .action-buttons .n-button { min-width: 80px; padding: 0 16px; height: 28px; font-size: 14px; } /* 调整小屏幕下的样式 */ @media screen and (max-width: 1920px) { .task-info-item { flex-direction: column; align-items: flex-start; gap: 4px; margin-bottom: 12px; } .task-info-item .label { width: 100%; margin-bottom: 2px; font-size: 14px; } .action-buttons { width: 100%; justify-content: flex-start; } .action-buttons .n-button { min-width: 72px; font-size: 12px; padding: 0 12px; height: 24px; } } /* 添加取消按钮特定样式 */ .cancel-button { cursor: pointer; } .cancel-button:disabled { cursor: not-allowed; opacity: 0.7; } /* 移除取消按钮的hover效果 */ .cancel-button:hover { background-color: var(--n-color) !important; border-color: var(--n-border-color) !important; } /* 调整小屏幕下的样式 */ @media screen and (max-width: 1920px) { /* ... 其他媒体查询样式保持不变 ... */ .cancel-button { min-width: 72px; font-size: 12px; padding: 0 12px; height: 24px; } } /* 添加大图预览容器样式 */ .large-image-container { width: 100%; height: 80vh; display: flex; justify-content: center; align-items: center; background: #000; border-radius: 8px; overflow: hidden; } .large-image-container img { max-width: 100%; max-height: 100%; object-fit: contain; } /* 调整弹窗样式 */ :deep(.n-modal) { background: rgba(0, 0, 0, 0.85); } :deep(.n-card) { background: transparent; border: none; padding: 0; } :deep(.n-card__content) { padding: 0; } </style><script setup> import { NLayout, NLayoutSider, NLayoutContent, NTree, NDataTable, NSpace, NInput, NButton, NIcon, NModal, NForm, NFormItem, NSelect, NTag, NDialog, useDialog, NDropdown } from 'naive-ui' import { SearchOutline, PersonAddOutline, AddCircleOutline, StopCircleOutline, LinkOutline, EllipsisHorizontalOutline, CreateOutline, SwapHorizontalOutline, KeyOutline, TrashOutline, EllipsisVerticalOutline } from '@vicons/ionicons5' import { ref, computed, onMounted, h } from 'vue' import { contactsApi } from '@/api/contacts' import messages from '@/utils/messages' import { doDelete, doPut, doPost } from '@/utils/requests' // 组织机构树数据 const orgTreeData = ref([]) // 选中的组织节点 const selectedOrgKey = ref(null) // 添加选中行的状态 const checkedRowKeys = ref([]) // 表格列定义 const columns = [ { type: 'selection', fixed: 'left', align: 'center', width: 50 }, { title: '姓名', key: 'name', align: 'left', ellipsis: true, minWidth: 100 }, { title: '部门', key: 'department', align: 'left', ellipsis: true, minWidth: 150 }, { title: '手机', key: 'workMobile', align: 'left', ellipsis: true, minWidth: 120 }, { title: '邮箱', key: 'email', align: 'left', ellipsis: true, minWidth: 180 }, { title: '状态', key: 'status', align: 'center', width: 100, render(row) { return h( NTag, { size: 'small', type: row.status === 'active' ? 'success' : 'error', round: true, style: { minWidth: '60px', justifyContent: 'center' } }, { default: () => row.status === 'active' ? '正常' : '停用' } ) } }, { title: '操作', key: 'actions', width: 120, fixed: 'right', align: 'center', render(row) { return h('div', { class: 'table-actions' }, [ h( NButton, { size: 'small', quaternary: true, circle: true, onClick: (e) => { e.stopPropagation() messages.info('编辑功能开发中...') } }, { icon: () => h(NIcon, { component: CreateOutline }) } ), h( NDropdown, { trigger: 'click', options: actionOptions(row), onSelect: (key) => handleActionSelect(key, row) }, { default: () => h( NButton, { size: 'small', quaternary: true, circle: true, onClick: (e) => e.stopPropagation() }, { icon: () => h(NIcon, { component: EllipsisHorizontalOutline }) } ) } ) ]) } } ] // 表格数据和分页状态 const tableData = ref([]) const pagination = ref({ page: 1, pageSize: 20, itemCount: 0, pageSizes: [20, 50], showSizePicker: true, prefix({ itemCount }) { return `共 ${itemCount} 条` } }) // 搜索关键词 const searchKeyword = ref('') // 新增组织机构相关 const showOrgModal = ref(false) const orgFormRef = ref(null) const orgLoading = ref(false) const orgFormModel = ref({ name: '', parentKey: null, type: 'department' // 'company' | 'department' | 'group' }) const orgFormRules = { name: { required: true, message: '请输入组织名称', trigger: 'blur' }, type: { required: true, message: '请选择组织类型', trigger: 'change' } } const orgTypeOptions = [ { label: '公司', value: 'company' }, { label: '部门', value: 'department' }, { label: '小组', value: 'group' } ] // 获取所有可选的父级组织 const parentOrgOptions = computed(() => { const options = [] const traverse = (nodes, prefix = '') => { nodes.forEach(node => { options.push({ label: prefix + node.label, value: node.key }) if (node.children) { traverse(node.children, prefix + node.label + ' / ') } }) } traverse(orgTreeData.value) return options }) // 添加树节点加载状态 const treeLoading = ref(false) // 处理组织树选择 const handleOrgSelect = async (keys) => { if (keys.length > 0) { selectedOrgKey.value = keys[0] // 重置分页到第一页 pagination.value.page = 1 // 设置加载状态 treeLoading.value = true try { // 加载选中节点的成员 await loadMembers(keys[0]) } finally { // 无论成功失败都关闭加载状态 treeLoading.value = false } } } // 处理分页变化 const handlePageChange = async (page) => { await loadMembers(selectedOrgKey.value, page) } // 添加页大小变化处函数 const handlePageSizeChange = async (pageSize) => { pagination.value.pageSize = pageSize pagination.value.page = 1 await loadMembers(selectedOrgKey.value, 1) } // 处理搜索 const handleSearch = async () => { if (selectedOrgKey.value) { pagination.value.page = 1 await loadMembers(selectedOrgKey.value) } } // 处理添加成员 const handleAddMember = () => { // 设置默认部门ID为当前选中的组织 memberFormModel.value.departmentId = selectedOrgKey.value showMemberModal.value = true } // 处理新增组织 const handleAddOrg = () => { showOrgModal.value = true // 如果有选中的节点，默认设置为父级 if (selectedOrgKey.value) { orgFormModel.value.parentKey = selectedOrgKey.value } } // 处理提交新增组织 const handleOrgSubmit = () => { orgFormRef.value?.validate(async (errors) => { if (errors) return orgLoading.value = true try { await contactsApi.createOrganization({ name: orgFormModel.value.name, parentId: orgFormModel.value.parentKey || 0, type: orgFormModel.value.type }) messages.success('创建组织成功') showOrgModal.value = false // 重新加载组织树 await loadOrgTree() // 重置表单 orgFormModel.value = { name: '', parentKey: null, type: 'department' } } catch (error) { console.error('创建组织失败:', error) } finally { orgLoading.value = false } }) } // 计算所有节点的 key，用于默认展开 const defaultExpandedKeys = computed(() => { const keys = [] const getKeys = (nodes) => { nodes.forEach(node => { keys.push(node.key) if (node.children) { getKeys(node.children) } }) } getKeys(orgTreeData.value) return keys }) // 加载组机构树数据 const loadOrgTree = async () => { try { const { data } = await contactsApi.getOrganizations() // 将扁平数据转换为树形结构 const treeData = buildTree(data) orgTreeData.value = treeData // 默认选中第一个根节点 if (treeData && treeData.length > 0) { selectedOrgKey.value = treeData[0].key // 加载根节点的成员 await loadMembers(treeData[0].key) } } catch (error) { console.error('Failed to load organizations:', error) } } // 修改构建树数据的函数 const buildTree = (flatData) => { const treeData = [] const map = {} // 首先创建所有节点的映射 flatData.forEach(item => { map[item.id] = { key: item.id.toString(), label: item.name, children: [], // 添加 suffix 函数来渲染操作按钮 suffix: () => selectedOrgKey.value === item.id.toString() ? h( NDropdown, { trigger: 'hover', options: [ { label: '重命名', key: 'rename', icon: () => h(NIcon, { component: CreateOutline }) }, { label: '删除', key: 'delete', icon: () => h(NIcon, { component: TrashOutline }) } ], onSelect: (key) => handleOrgAction(key, map[item.id]) }, { default: () => h( NButton, { size: 'small', quaternary: true, circle: true, class: 'org-tree-action' }, { icon: () => h(NIcon, { component: EllipsisVerticalOutline }) } ) } ) : null, // 保存原始数据，以备后用 raw: item } }) // 构建树形结构 flatData.forEach(item => { const node = map[item.id] if (item.parentId === 0) { // 根点 treeData.push(node) } else { // 子节点 const parent = map[item.parentId] if (parent) { parent.children.push(node) } } }) // 移除空的 children 数组 const removeEmptyChildren = (nodes) => { nodes.forEach(node => { if (node.children.length === 0) { delete node.children } else { removeEmptyChildren(node.children) } }) } removeEmptyChildren(treeData) return treeData } // 修改成员加载函数 const loadMembers = async (orgKey, page = 1) => { orgLoading.value = true try { const { data } = await contactsApi.getMembers( orgKey, searchKeyword.value, page, pagination.value.pageSize ) // 将后端数据映射到表格所需的格式 tableData.value = data.records.map(item => ({ id: item.id, name: item.nickname, department: item.deptName, employeeId: item.username, workMobile: item.workMobile, email: item.username, status: item.disabled ? 'disabled' : 'active', // position 字段暂时为空 position: '' })) // 更新分页信息 pagination.value.itemCount = data.totalRow pagination.value.page = data.pageNumber pagination.value.pageSize = data.pageSize } catch (error) { console.error('Failed to load members:', error) } finally { orgLoading.value = false } } // 处理选中行变化 const handleCheckedRowKeysChange = (keys) => { checkedRowKeys.value = keys console.log('选中的行:', keys) } // 在组件挂载时加载数据 onMounted(async () => { await loadOrgTree() }) // 批量禁用处理函数 const handleBatchDisable = async () => { const selectedIds = checkedRowKeys.value if (!selectedIds.length) return dialog.warning({ title: '确认禁用', content: `确定要禁用选中的 ${selectedIds.length} 名成员吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await contactsApi.batchUpdateMemberStatus(selectedIds, 'disabled') messages.success('批量禁用成功') // 重新加载数据 await loadMembers(selectedOrgKey.value, pagination.value.page) // 清空选择 checkedRowKeys.value = [] } catch (error) { messages.error('批量禁用失败') } } }) } // 修改邀请成员处理函数 const handleInvite = async () => { try { const { data } = await doPost('/auth-center/system/department/invitation') // 复制到剪贴板 navigator.clipboard.writeText(data.txt) .then(() => { messages.success('邀请链接已复制到剪贴板') }) .catch(() => { messages.error('复制失败，请手动复制') }) } catch (err) { messages.error('获取邀请链接失败') } } // 注入对话框服务 const dialog = useDialog() // 添加操作菜单选项 const actionOptions = (row) => [ { label: row.status === 'active' ? '禁用' : '启用', key: row.status === 'active' ? 'disable' : 'enable', props: { type: row.status === 'active' ? 'error' : 'success' } }, { label: '变更部门', key: 'change-department' }, { label: '重置密码', key: 'reset-password' } ] // 处理操作菜单点击 const handleActionSelect = (key, row) => { switch (key) { case 'disable': case 'enable': handleStatusChange(row) break case 'change-department': handleChangeDepartment(row) break case 'reset-password': handleResetPassword(row) break } } // 处理状态变更 const handleStatusChange = (row) => { const newStatus = row.status === 'active' ? 'disabled' : 'active' dialog.warning({ title: `确认${newStatus === 'active' ? '启用' : '禁用'}`, content: `确定要${newStatus === 'active' ? '启用' : '禁用'}该成员吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await contactsApi.batchUpdateMemberStatus([row.id], newStatus) messages.success(`${newStatus === 'active' ? '启用' : '禁用'}成功`) await loadMembers(selectedOrgKey.value, pagination.value.page) } catch (error) { messages.error(`${newStatus === 'active' ? '启用' : '禁用'}失败`) } } }) } // 处理部门变更 const handleChangeDepartment = (row) => { messages.info('功能开发中...') } // 处理重置密码 const handleResetPassword = (row) => { messages.info('功能开发中...') } // 添加新成员相关状态 const showMemberModal = ref(false) const memberFormRef = ref(null) const memberLoading = ref(false) const memberFormModel = ref({ nickname: '', workMobile: '', username: '', departmentId: null }) const memberFormRules = { nickname: { required: true, message: '请输入姓名', trigger: 'blur' }, workMobile: { required: true, message: '请输入手机号', trigger: 'blur', pattern: /^1[3-9]\d{9}$/, validator(rule, value) { if (!value) return true if (!/^1[3-9]\d{9}$/.test(value)) { return new Error('请输入正确的手机号') } return true } }, username: { required: true, message: '请输入邮箱', trigger: 'blur', validator(rule, value) { if (!value) return true if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) { return new Error('请输入正确的邮箱地址') } return true } } } // 处理提交新增成员 const handleMemberSubmit = () => { memberFormRef.value?.validate(async (errors) => { if (errors) return memberLoading.value = true try { await contactsApi.createMember(memberFormModel.value) messages.success('添加成员成功') showMemberModal.value = false // 重新加载成员列表 await loadMembers(selectedOrgKey.value, pagination.value.page) // 重置表单 memberFormModel.value = { nickname: '', workMobile: '', username: '', departmentId: null } } catch (error) { console.error('添加成员失败:', error) } finally { memberLoading.value = false } }) } // 添加操作处理函数 const handleOrgAction = (key, node) => { switch (key) { case 'rename': handleRenameOrg(node) break case 'delete': handleDeleteOrg(node) break } } // 修改重命名处理函数 const handleRenameOrg = (node) => { const inputValue = ref(node.label) dialog.warning({ title: '重命名组织', content: () => h(NInput, { value: inputValue.value, maxlength: 20, onInput: (value) => { // 过滤特殊字符 inputValue.value = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') }, placeholder: '请输入组织名称（限20字以内，不支持特殊字符）' }), positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { const newName = inputValue.value?.trim() if (!newName) { messages.error('组织名称不能为空') return false } if (newName === node.label) return await doPut('/auth-center/system/department', { id: node.key, name: newName }) messages.success('重命名成功') await loadOrgTree() } }) } // 修改处理删除的函数 const handleDeleteOrg = (node) => { dialog.warning({ title: '警告', content: '确定要删除该节点吗？删除后无法恢复。', positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { await doDelete(`/auth-center/system/department?ids=${node.key}`) messages.success('删除成功') await loadOrgTree() } }) } </script> <template> <n-layout has-sider :x-gap="16"> <!-- 左侧组织机构树 --> <n-layout-sider bordered :width="240" :native-scrollbar="false" class="sider" > <div class="org-tree-container"> <div class="tree-header"> <span>组织机构</span> <n-button type="primary" ghost size="small" @click="handleAddOrg" > <template #icon> <n-icon> <AddCircleOutline /> </n-icon> </template> 新增组织 </n-button> </div> <n-tree block-line :data="orgTreeData" :selected-keys="selectedOrgKey ? [selectedOrgKey] : []" @update:selected-keys="handleOrgSelect" class="org-tree" :default-expanded-keys="defaultExpandedKeys" :loading="treeLoading" :loading-props="{ size: 'small', stroke: '#18a058' }" /> </div> </n-layout-sider> <!-- 右侧成员列表 --> <n-layout-content class="content"> <div class="member-list-container"> <!-- 工具栏 --> <n-space align="center" justify="space-between" class="toolbar"> <n-input v-model:value="searchKeyword" placeholder="搜索成员" @keyup.enter="handleSearch" > <template #prefix> <n-icon :component="SearchOutline" /> </template> </n-input> <n-space> <n-button type="warning" :disabled="!checkedRowKeys.length" @click="handleBatchDisable" > <template #icon> <n-icon> <StopCircleOutline /> </n-icon> </template> 批量禁用 </n-button> <n-button type="primary" @click="handleAddMember"> <template #icon> <n-icon :component="PersonAddOutline" /> </template> 添加成员 </n-button> <n-button type="info" @click="handleInvite"> <template #icon> <n-icon> <LinkOutline /> </n-icon> </template> 邀请成员 </n-button> </n-space> </n-space> <!-- 成员表格 --> <n-data-table :columns="columns" :data="tableData" :loading="orgLoading" :pagination="pagination" :bordered="false" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" @update:checked-row-keys="handleCheckedRowKeysChange" :checked-row-keys="checkedRowKeys" class="member-table" :row-key="row => row.id" :scroll-x="1200" /> </div> </n-layout-content> <!-- 新增组织弹窗 --> <n-modal v-model:show="showOrgModal" preset="dialog" title="新增组织" positive-text="确定" negative-text="取消" :loading="orgLoading" @positive-click="handleOrgSubmit" > <n-form ref="orgFormRef" :model="orgFormModel" :rules="orgFormRules" label-placement="left" label-width="80" require-mark-placement="right-hanging" > <n-form-item label="名称" path="name"> <n-input v-model:value="orgFormModel.name" placeholder="请输入组织名称" /> </n-form-item> <n-form-item label="类型" path="type"> <n-select v-model:value="orgFormModel.type" :options="orgTypeOptions" placeholder="请选择组织类型" /> </n-form-item> <n-form-item label="上级组织" path="parentKey"> <n-select v-model:value="orgFormModel.parentKey" :options="parentOrgOptions" placeholder="请选择上级组织" clearable /> </n-form-item> </n-form> </n-modal> <!-- 新增成员弹窗 --> <n-modal v-model:show="showMemberModal" preset="dialog" title="添加成员" positive-text="确定" negative-text="取消" :loading="memberLoading" @positive-click="handleMemberSubmit" > <n-form ref="memberFormRef" :model="memberFormModel" :rules="memberFormRules" label-placement="left" label-width="80" require-mark-placement="right-hanging" > <n-form-item label="姓名" path="nickname"> <n-input v-model:value="memberFormModel.nickname" placeholder="请输入姓名" /> </n-form-item> <n-form-item label="手机号" path="workMobile"> <n-input v-model:value="memberFormModel.workMobile" placeholder="请输入手机号" /> </n-form-item> <n-form-item label="邮箱" path="username"> <n-input v-model:value="memberFormModel.username" placeholder="请输入邮箱" /> </n-form-item> </n-form> </n-modal> </n-layout> </template> <style scoped> .sider { background: #fff; height: 100%; display: flex; flex-direction: column; } .org-tree-container { height: 100%; padding: 16px; display: flex; flex-direction: column; box-sizing: border-box; } .tree-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; padding: 0 8px; flex-shrink: 0; } .tree-header span { font-size: 16px; font-weight: 500; color: #333; } .org-tree { flex: 1; overflow-y: auto; padding-right: 8px; margin-bottom: 0; } .content { height: 100%; background: #f5f5f5; display: flex; flex-direction: column; box-sizing: border-box; border-radius: 0 8px 8px 0; } .member-list-container { background: #fff; border-radius: 8px; padding: 16px; height: 100%; display: flex; flex-direction: column; box-sizing: border-box; } .toolbar { margin-bottom: 16px; flex-shrink: 0; } .member-table { flex: 1; overflow: hidden; display: flex; flex-direction: column; } :deep(.n-data-table-wrapper) { flex: 1; overflow: hidden; } :deep(.n-data-table) { height: 100%; } /* 自定义滚动条样式 */ .org-tree::-webkit-scrollbar { width: 6px; } .org-tree::-webkit-scrollbar-thumb { background-color: #d9d9d9; border-radius: 3px; } .org-tree::-webkit-scrollbar-track { background-color: transparent; } /* 确保内容区域不会被按钮遮挡 */ :deep(.n-layout-sider-scroll-container) { height: 100%; } /* 确保侧边栏容器高度正确 */ .sider { height: 100%; display: flex; flex-direction: column; } /* 修改主布局样式 */ .n-layout { height: 100%; } :deep(.n-layout-sider-border) { border-radius: 8px 0 0 8px; } /* 添加树节点加载状态的样式 */ :deep(.n-tree-node-content--selected) { background-color: rgba(24, 160, 88, 0.1) !important; } :deep(.n-tree-node-content--selected:hover) { background-color: rgba(24, 160, 88, 0.15) !important; } :deep(.n-tree-node-loading) { color: #18a058; } /* 调整表格样式 */ .member-table { flex: 1; overflow: hidden; display: flex; flex-direction: column; } :deep(.n-data-table-wrapper) { flex: 1; overflow: hidden; } :deep(.n-data-table) { height: 100%; } /* 优化行的样式 */ :deep(.n-data-table-tr) { height: 48px; } :deep(.n-data-table-td) { padding: 8px 12px !important; } /* 优化选中行的样式 */ :deep(.n-data-table-tr--checked) { background-color: rgba(24, 160, 88, 0.1) !important; } :deep(.n-data-table-tr--checked:hover) { background-color: rgba(24, 160, 88, 0.15) !important; } /* 添加批量操作按钮的过渡效果 */ .toolbar { position: relative; } .n-button { transition: all 0.3s; } /* 添加按钮间距样式 */ .n-space .n-button { margin-left: 12px; } /* 添加表格操作列样式 */ :deep(.table-actions) { display: flex; justify-content: center; gap: 4px; } :deep(.table-actions .n-button) { width: 28px; height: 28px; padding: 0; } :deep(.table-actions .n-button:hover) { background-color: rgba(0, 0, 0, 0.06); } /* 修改树节点样式 */ :deep(.n-tree-node-content) { display: flex; align-items: center; } .org-tree-action { margin-left: 8px; /* 添加左边距 */ } /* 确保下拉菜单在最上层 */ :deep(.n-dropdown-menu) { z-index: 1000; } </style><template> <div class="invite-container"> <n-card class="invite-card"> <template #header> <div class="card-header"> <n-icon size="48" color="#2080f0"> <TeamOutlined /> </n-icon> <h2>加入团队</h2> </div> </template> <div v-if="loading" class="loading-wrapper"> <n-spin size="large" /> <p>正在获取邀请信息...</p> </div> <div v-else-if="error" class="error-wrapper"> <n-result status="error" title="获取邀请信息失败" :description="error"> <template #footer> <n-button @click="fetchInviteInfo">重试</n-button> </template> </n-result> </div> <div v-else class="invite-content"> <div class="invite-info"> <p class="welcome-text"> 欢迎加入<span class="highlight">{{ inviteInfo.name }}</span> </p> </div> <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="80" class="form-wrapper" > <n-form-item label="称呼" path="nickname"> <n-input v-model:value="formModel.nickname" placeholder="请输入您的称呼（10字以内）" maxlength="10" @input="value => formModel.nickname = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '')" /> </n-form-item> <n-form-item label="邮箱" path="email"> <n-input v-model:value="formModel.email" placeholder="请输入邮箱（将作为登录用户名）" /> </n-form-item> <n-form-item label="手机号" path="mobile"> <n-input v-model:value="formModel.mobile" placeholder="请输入手机号" /> </n-form-item> <n-form-item label="设置密码" path="password"> <n-input v-model:value="formModel.password" type="password" placeholder="请输入密码" @keydown.enter.prevent /> </n-form-item> <n-form-item label="确认密码" path="confirmPassword"> <n-input v-model:value="formModel.confirmPassword" type="password" placeholder="请再次输入密码" @keydown.enter="handleJoin" /> </n-form-item> </n-form> <div class="actions"> <n-button type="primary" size="large" :loading="joining" :disabled="joining" block @click="handleJoin" > {{ joining ? '正在加入...' : '加入团队' }} </n-button> </div> </div> </n-card> </div> </template> <script setup> import { ref, onMounted } from 'vue' import { useRoute, useRouter } from 'vue-router' import { NCard, NButton, NSpin, NResult, NForm, NFormItem, NInput, NIcon } from 'naive-ui' import { TeamOutlined } from '@vicons/antd' import messages from '@/utils/messages' import { doGet, doPost } from '@/utils/requests' const route = useRoute() const router = useRouter() const formRef = ref(null) const loading = ref(true) const error = ref('') const joining = ref(false) const inviteInfo = ref({}) const formModel = ref({ nickname: '', email: '', mobile: '', password: '', confirmPassword: '' }) const rules = { nickname: [ { required: true, message: '请输入您的称呼' }, { max: 10, message: '称呼不能超过10个字符' }, { validator: (rule, value) => { if (!value) return true if (/[^\u4e00-\u9fa5a-zA-Z0-9\s]/.test(value)) { return new Error('称呼不能包含特殊字符') } return true } } ], email: [ { required: true, message: '请输入邮箱' }, { pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的邮箱地址' } ], mobile: [ { required: true, message: '请输入手机号' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' } ], password: [ { required: true, message: '请输入密码' }, { min: 9, message: '密码长度不能小于9位' }, { validator: (rule, value) => { if (!value) return true const hasUpperCase = /[A-Z]/.test(value) const hasLowerCase = /[a-z]/.test(value) const hasNumber = /[0-9]/.test(value) const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value) const conditions = [ hasUpperCase, hasLowerCase, hasNumber, hasSpecial ].filter(Boolean).length if (conditions < 2) { return new Error('密码必须包含大写字母、小写字母、数字、特殊符号中的至少2种') } return true } } ], confirmPassword: [ { required: true, message: '请确认密码' }, { validator: (rule, value) => { return value === formModel.value.password || new Error('两次输入的密码不一致') } } ] } const fetchInviteInfo = async () => { const inviteCode = route.query.dept_invite_code if (!inviteCode) { error.value = '邀请链接无效，缺少邀请码' loading.value = false return } loading.value = true const { data } = await doGet(`/auth-center/system/department/invitation?invite_code=${inviteCode}`) inviteInfo.value = data loading.value = false } const handleJoin = () => { formRef.value?.validate(async (errors) => { if (errors) return joining.value = true try { await doPost('/auth-center/system/user/join', { username: formModel.value.email, passwd: formModel.value.password, workMobile: formModel.value.mobile, nickname: formModel.value.nickname, disabled: false, tenantId: inviteInfo.value.tenantId, departmentId: inviteInfo.value.id }) messages.success('成功加入团队，请使用邮箱和密码登录') router.push({ path: '/login', query: { email: formModel.value.email } }) } finally { joining.value = false } }) } onMounted(() => { fetchInviteInfo() }) </script> <style scoped> .invite-container { min-height: 100vh; display: flex; align-items: center; justify-content: center; background-color: #f5f7fa; padding: 20px; } .invite-card { width: 100%; max-width: 480px; } .card-header { display: flex; flex-direction: column; align-items: center; gap: 16px; } .card-header h2 { margin: 0; color: #2c3e50; } .loading-wrapper, .error-wrapper { display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 200px; } .invite-content { padding: 20px 0; } .welcome-text { line-height: 1.8; color: #333; margin: 0; padding: 0; text-align: center; font-size: 16px; } .highlight { color: #2080f0; font-weight: 500; margin: 0 4px; font-size: 18px; } .invite-info { margin-bottom: 32px; padding: 20px; background-color: #f9f9f9; border-radius: 8px; border-left: 4px solid #2080f0; } .form-wrapper { margin-bottom: 24px; } .actions { margin-top: 32px; } </style><template> <div class="invite-container"> <n-card class="invite-card"> <template #header> <div class="card-header"> <n-icon size="48" color="#2080f0"> <TeamOutlined /> </n-icon> <h2>团队邀请</h2> </div> </template> <div v-if="loading" class="loading-wrapper"> <n-spin size="large" /> <p>正在获取邀请信息...</p> </div> <div v-else-if="error" class="error-wrapper"> <n-result status="error" title="获取邀请信息失败" :description="error"> <template #footer> <n-button @click="fetchInviteInfo">重试</n-button> </template> </n-result> </div> <div v-else class="invite-content"> <div class="invite-info"> <p class="welcome-text"> 您好，<span class="highlight">{{ inviteInfo.nickname }}</span>， 请确认是否加入<span class="highlight">{{ inviteInfo.deptName }}</span>？ <br>确认加入请在下方设置您的登陆密码 </p> </div> <n-form ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="80" class="form-wrapper" > <div class="username-display"> <span class="label">您的登陆用户名为：</span> <span class="username">{{ inviteInfo.username }}</span> </div> <n-form-item label="设置密码" path="password"> <n-input v-model:value="formModel.password" type="password" placeholder="请输入密码" @keydown.enter.prevent /> </n-form-item> <n-form-item label="确认密码" path="confirmPassword"> <n-input v-model:value="formModel.confirmPassword" type="password" placeholder="请再次输入密码" @keydown.enter.prevent /> </n-form-item> <n-form-item path="agreement"> <n-checkbox v-model:checked="formModel.agreement"> 我已阅读并同意千诀科技开放平台的 <a href="https://home.qj-robots.com/policies.pdf" target="_blank" class="agreement-link">用户协议、隐私政策</a> </n-checkbox> </n-form-item> </n-form> <div class="actions"> <n-button type="primary" size="large" :loading="joining" :disabled="joining" block @click="handleJoin" > {{ joining ? '正在加入...' : '加入团队' }} </n-button> </div> </div> </n-card> </div> </template> <script setup> import { ref, onMounted } from 'vue' import { useRoute, useRouter } from 'vue-router' import { NCard, NButton, NSpin, NResult, NForm, NFormItem, NInput, NIcon, NCheckbox } from 'naive-ui' import { TeamOutlined } from '@vicons/antd' import messages from '@/utils/messages' import requests from '@/utils/requests' const route = useRoute() const router = useRouter() const formRef = ref(null) const loading = ref(true) const error = ref('') const joining = ref(false) const inviteInfo = ref({}) const formModel = ref({ password: '', confirmPassword: '', agreement: false }) const rules = { password: [ { required: true, message: '请输入密码' }, { min: 9, message: '密码长度不能小于9位' }, { validator: (rule, value) => { if (!value) return true // 检查密码复杂度 const hasUpperCase = /[A-Z]/.test(value) const hasLowerCase = /[a-z]/.test(value) const hasNumber = /[0-9]/.test(value) const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value) // 计算满足的条件数 const conditions = [ hasUpperCase, hasLowerCase, hasNumber, hasSpecial ].filter(Boolean).length if (conditions < 2) { return new Error('密码必须包含大写字母、小写字母、数字、特殊符号中的至少2种') } return true } } ], confirmPassword: [ { required: true, message: '请确认密码' }, { validator: (rule, value) => { return value === formModel.value.password || new Error('两次输入的密码不一致') } } ], agreement: [ { validator: (rule, value) => { if (!value) { return new Error('请阅读并同意用户协议和隐私政策') } return true }, trigger: ['change', 'submit'] } ] } const fetchInviteInfo = async () => { const inviteCode = route.query.invite_code if (!inviteCode) { error.value = '邀请链接无效，缺少邀请码' loading.value = false return } try { loading.value = true error.value = '' const { data } = await requests.get(`/auth-center/system/invitation`, { params: { invite_code: inviteCode } }) inviteInfo.value = data } catch (err) { console.error('获取邀请信息错误:', err) error.value = err.message || '网络错误，请稍后重试' } finally { loading.value = false } } const handleJoin = () => { formRef.value?.validate(async (errors) => { if (errors) return const inviteCode = route.query.invite_code if (!inviteCode) { messages.error('邀请码无效') return } try { joining.value = true await requests.put(`/auth-center/system/invitation/${inviteCode}`, { id: inviteInfo.value.id, passwd: formModel.value.password }) messages.success('成功加入团队，请使用设置的密码登录') // 跳转到登录页面并带上用户名 router.push({ path: '/login', query: { email: inviteInfo.value.username } }) } catch (err) { messages.error(err.message || '加入团队失败') } finally { joining.value = false } }) } onMounted(() => { fetchInviteInfo() }) </script> <style scoped> .invite-container { min-height: 100vh; display: flex; align-items: center; justify-content: center; background-color: #f5f7fa; padding: 20px; } .invite-card { width: 100%; max-width: 480px; } .card-header { display: flex; flex-direction: column; align-items: center; gap: 16px; } .card-header h2 { margin: 0; color: #2c3e50; } .loading-wrapper, .error-wrapper { display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 200px; } .invite-content { padding: 20px 0; } .welcome-text { line-height: 1.8; color: #333; margin: 0; padding: 0; } .highlight { color: #2080f0; font-weight: 500; margin: 0 4px; font-size: 16px; } .invite-info { margin-bottom: 32px; padding: 20px; background-color: #f9f9f9; border-radius: 8px; border-left: 4px solid #2080f0; } .info-item { margin: 8px 0; line-height: 1.5; } .info-item .label { color: #666; margin-right: 8px; } .info-item .value { color: #2c3e50; font-weight: 500; } .form-wrapper { margin-bottom: 24px; } .actions { margin-top: 32px; } .username-display { display: flex; align-items: center; margin-bottom: 24px; padding: 4px 0; } .username-display .label { color: #666; } .username-display .username { color: #2080f0; font-weight: 500; font-size: 16px; } .agreement-link { color: #2080f0; text-decoration: none; } .agreement-link:hover { text-decoration: underline; } </style><template> <div class="login-container"> <div class="login-box"> <div class="login-content"> <div class="login-form-section"> <div class="login-logo"> <img src="@/assets/images/qj-logo-Dgc-aGJV.png" alt="Logo" style="width: 450px; height: auto;"> </div> <div class="login-header"> </div> <div class="login-form"> <div class="input-item"> <div class="input-wrapper"> <n-input v-model:value="username" placeholder="请输入您的邮箱" autocomplete="off" :autofocus="true" :input-props="{ style: 'text-align: left' }" @blur="validateEmail" :status="emailError ? 'error' : undefined" > <template #prefix> <n-icon><person-outline /></n-icon> </template> </n-input> <span v-if="emailError" class="error-message">{{ emailError }}</span> </div> </div> <div class="input-item"> <div class="input-wrapper"> <n-input v-model:value="password" type="password" placeholder="请输入您的密码" show-password-on="click" autocomplete="new-password" @keyup.enter="handleLogin" :input-props="{ style: 'text-align: left' }"> <template #prefix> <n-icon><lock-closed-outline /></n-icon> </template> </n-input> </div> </div> <div class="login-actions"> <n-checkbox v-model:checked="rememberMe">记住我</n-checkbox> <n-button type="primary" class="login-button" @click="handleLogin">登录</n-button> </div> <!-- <div class="register-link"> <n-button text @click="handleRegister">还没有账号？立即创建</n-button> </div> --> <div class="register-link" style="color: #b0b0b0"> 登录视为您已阅读并同意千诀科技开放平台的<n-button text @click="showPolicies">用户协议、隐私政策</n-button> </div> </div> </div> <!-- <div class="wecom-login-section"> <n-spin size="large" :show="isLoading"> <div ref="wecomLoginContainer"></div> </n-spin> </div> --> </div> </div> <footer class="footer"> <div class="footer-content"> <p>© 2024 北京千诀科技有限公司 版权所有</p> <p>京ICP备2023038411号-1</p> <p class="footer-text"> <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoCAYAAACWwljjAAAFQklEQVRYw+3Wa1BU dRjH8SOpMeg4WhZGpDIxiaaTeUFgWrxE4AVRQJGlRRAVIV1JkbgMgQLi5AVBQSVLSp0xlEAUKBEEFZCrCstl l8UV2AV2YbmoGCrYv31+R95UL5pmmtamZ+bz6rz5nvOc/5zDcX9jGLs/iTxuyvIlWYkRFeTHA2HVRFtzfhth TG5KuH96/vUgNlC4mMgyw1NJit/aAXLKazYje9xtIMZ/OZz50gW+9hcNkvoLEemEPbnrSP47QYwxQ5Ifv54R qzcXwFFvSyjaOhfavN8F7Y5ZcC/HH9JOB4LNa9Zw5YA76OZV8vIGMdZtSp7cDrtOnOavYiQhTAiPwi1AMtIQaq yngsxpBtw2GAGDKfaQmpUAa6xc4Vfp4UtEdzAMycsT9JQ1Tyctl/2eEkuTlYysF/rCUNxMqDEzgTqzSXBnpgnI HCzgjvEEuD52DLBr3rA1MAaWmNtB582wdtIljZ9G9D+IPU6aTxIPBjHCcXvg3CEh9K2fDLWvjIH6D6fwTIyheu wEqLUyhzLOALq8pkN+bgRw3HY4FBsMzxojZxP9DequLjAlQwVrbpIjhyIY4UYGQ/buhdBqPxlk3Gion2IMDQIz 3kJe/ZS34I7uHkmD7VSQVgYDNyIAwsNCgfXGXoOBPjP9DKrOCAogA2etGTmTHAMcFwFZye7wS5QlVHGjoEw4A2 qPCUBZ6AzNcQ5Q/YYRdO+YB1U3dsDwypLio4FJ3ECryIzWz6Cm3NgTRHN8HiPF6eHAGSbAdh8feFZkB7krzaH E9h2o85sDsiAbkIsXQMN+e2CtGyF0kzdwXCgU5++D/ouLQFV4OEU/g2Q/iNuIPNaKkQflAWBqexxGjhLDVUcL 6IwSQN3SGVChe6FJg9dckCx6D1QBliDZLIAxo7eA8eyv4KE0BJqTrHkZvnL9DJKn+Twmt0NsGGHZy2Dn3kQY fsQ53Hh4/r4RNGz8AIpdzKEuaAF0RC2E57MmQgE3ATjuM/CPiANW7AqSfQJQ5vk362eQKmd3JrmXsoSRocpN IMnbB9zbceDIWUPmuHFQNMkISqa9DpUvNK6YDpW2s8DfwBK48WFQnhMCgzUBoLy0BrRVe5P0NWjPLdKUsJiR1 tR1wGp8IeZwMgx/SrgRvjxuAziNcwLvyathLOcJHLflhRDYGRYFrNET2rJ5yvPLoas0tOj/oL8UpC4JHyTSU+ 6MNCS4gvKoAB5WiKG+MAQSg0WwLXQ/ZJ3xhao0FxB5hYCbUwAEfhEF3Td8QP2dAOQnPwFlxgrolUVq9TPoaX+ ZB2nLc2Gk6awj1MU78HZZwJMid2Byb550JQwVO0NfxlJgdz14vWKeRAiK6DlQF28PLZdcoLNcBIO92bb6GTQ8Q /13RURT6tlH2gvXMlITLYD6uI+gp2ozdF0VQXumM6ivCqGvahM8kPiDItkeGo8tB025GFQ3xFrSr06zI3/4yde 7oN7m0sWk5eKWDqK5JWJQvAHac9ygq3Adr9gTNNc3QG85rzPfHe5/7wDtPwuhp/Zz6CjyhaZzwi6ivfetHdH/oP7 7+3PJQOsuRnqkQdCa4wWqyx6gyecpL64GTaEX7ycXUJz4GJp1B4O0X/Hg0Xp1tFV+8Ei1k6c5coHofxBrrzQinbKY o0SVJ+wn6iurGHlY5gY911aDJnMFaHXXiDp9GQyvtKfUA9QFTtBZ7gPdit0tpFd9OpwwFmlA9D/o9yNLDpxIKmI8P MnNSNtviCLVpYTITzrXEGWaq4qos0WgOPdpCenIF+eRrurjB4k0PXopYZG6gMg/D/gNBUxhAbSAmKMAAAAASUVORK5CYII=" alt="备案图标" class="footer-icon"> 京公网安备11010802044154号 </p> </div> </footer> </div> </template> <script setup> import { ref, onMounted, watch } from 'vue' import { NInput, NButton, NCheckbox, NIcon, NSpin } from 'naive-ui' import { PersonOutline, LockClosedOutline } from '@vicons/ionicons5' import { useRouter, useRoute } from 'vue-router' import { doPost } from '@/utils/requests' import CryptoJS from 'crypto-js' import message from '@/utils/messages' import { useMainStore } from '@/stores/mainStore' import * as wecom from '@wecom/jssdk' const router = useRouter() const route = useRoute() const mainStore = useMainStore() const username = ref('') const password = ref('') const rememberMe = ref(false) const emailError = ref('') const encryptPassword = (pwd) => { const salt = '***#17600620312#' const key = CryptoJS.enc.Utf8.parse(salt) const iv = CryptoJS.enc.Utf8.parse(salt.slice(0, 16)) // 使用salt的前16个字符作为IV const encrypted = CryptoJS.AES.encrypt(pwd, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }) return encrypted.toString() } const decryptPassword = (encryptedPwd) => { const salt = '***#17600620312#' const key = CryptoJS.enc.Utf8.parse(salt) const iv = CryptoJS.enc.Utf8.parse(salt.slice(0, 16)) const decrypted = CryptoJS.AES.decrypt(encryptedPwd, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }) return decrypted.toString(CryptoJS.enc.Utf8) } const validateEmail = () => { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/ if (!username.value) { emailError.value = '邮箱不能为空' } else if (!emailRegex.test(username.value)) { emailError.value = '请输入有效的邮箱地址' } else { emailError.value = '' } } const handleLogin = async () => { validateEmail() if (emailError.value) { return } try { let loginPassword = password.value if (rememberMe.value) { // 如果是"记住我"，我们存储加密后的密码 const encryptedPassword = encryptPassword(password.value) localStorage.setItem('rememberedUsername', username.value) localStorage.setItem('rememberedPassword', encryptedPassword) } else { localStorage.removeItem('rememberedUsername') localStorage.removeItem('rememberedPassword') } // 始终使用未加密的密码进行登录 const response = await doPost('/auth-center/system/user/login', { username: username.value, passwd: encryptPassword(password.value) }) // 检查响应是否成功 if (response.code === 0 && response.data) { // 存储 accessToken 到 localStorage localStorage.setItem('access_token', response.data.accessToken) // 使用 mainStore 设置用户信息 mainStore.setUser({ nickname: response.data.nickname, // 添加其他需要的用户信息 }) // 登录成功后跳到首页 router.push('/') } else { // 如果响应不符合预期，抛出错误 throw new Error(response.message || '登录失败') } } catch (error) { console.error('Login failed:', error) // message.error(error.message || '登录失败，请稍后重试') } } const isLoading = ref(true) const wecomLoginContainer = ref(null) const isDebugMode = ref(false) const checkDebugMode = () => { const debugParam = route.query.debug isDebugMode.value = debugParam === 'true' || debugParam === '1' if (isDebugMode.value) { document.body.classList.add('debug-mode') } else { document.body.classList.remove('debug-mode') } } // 修改 handleRegister 函数 const handleRegister = () => { router.push('/register') } const showPolicies = () => { window.open('https://home.qj-robots.com/policies.pdf', '_blank') } onMounted(() => { checkDebugMode() // 创建企业微信登录面板 // if (wecomLoginContainer.value) { // wecom.createWWLoginPanel({ // el: wecomLoginContainer.value, // params: { // login_type: 'CorpApp', // appid: 'wwb5a4214455e4bf68', // agentid: '1000002', // redirect_uri: 'https://jwd.vooice.tech/', // state: 'loginState', // redirect_type: 'callback', // }, // onCheckWeComLogin({ isWeComLogin }) { // console.log(isWeComLogin) // }, // onLoginSuccess({ code }) { // doPost(`/system/wecom/login?code=${code}`) // .then((response) => { // console.log('Login successful:', response.data.data.accessToken); // localStorage.setItem('access_token', response.data.data.accessToken) // window.location.href = '/' // }) // .catch(error => { // console.error('Login failed:', error); // }); // }, // onLoginFail(err) { // console.log(err) // }, // }) // 在创建完成后隐藏 loading // // } isLoading.value = false // 检查是否有保存的登录信息 const savedUsername = localStorage.getItem('rememberedUsername') const savedPassword = localStorage.getItem('rememberedPassword') if (savedUsername && savedPassword) { username.value = savedUsername // 解密保存的密码 password.value = decryptPassword(savedPassword) rememberMe.value = true } // 清除自动填充样式 setTimeout(() => { const inputs = document.querySelectorAll('input') inputs.forEach(input => { input.style.backgroundColor = 'transparent' input.style.boxShadow = 'none' }) }, 100) // 检查路由查询参数中是否有邮箱地址 const emailFromRegister = route.query.email if (emailFromRegister) { username.value = emailFromRegister } }) watch(() => route.query, () => { checkDebugMode() }, { deep: true }) </script> <style scoped> .login-container { display: flex; flex-direction: column; justify-content: space-between; align-items: center; min-height: 100vh; height: 100vh; background-color: #f0f4f9; overflow: hidden; /* 修改这里 */ box-sizing: border-box; /* 添加行 */ padding: 20px 0; /* 添加这行，给顶部和底部一些内边距 */ overflow-x: hidden; /* 添加这一行 */ } .login-box { display: flex; justify-content: center; align-items: center; background-color: #ffffff; border-radius: 8px; padding: 40px; width: 90%; max-width: 1200px; height: auto; max-height: calc(100vh - 120px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); margin: auto; overflow-y: auto; box-sizing: border-box; overflow-x: hidden; } .login-header { text-align: center; margin-bottom: 40px; } .login-header h2 { font-size: 28px; color: #333; } .login-form { display: flex; flex-direction: column; align-items: center; width: 100%; max-width: 458px; margin: 0 auto; } .input-item { width: 100%; max-width: 458px; margin-bottom: 20px; } .input-wrapper { position: relative; width: 100%; } .error-message { position: absolute; top: -12px; /* 调整这个值，使错误信息更靠上 */ right: 10px; color: #ff4d4f; font-size: 18px; background-color: white; padding: 0 6px; border-radius: 2px; z-index: 1; box-shadow: 0 0 0 1px white; font-weight: 500; line-height: 1; /* 添加这行以确保文本垂直居中 */ } .login-actions { width: 100%; max-width: 458px; display: flex; justify-content: space-between; align-items: center; margin-top: 20px; } .login-button { width: 140px; height: 50px; font-size: 18px; } .footer { width: 100%; padding: 10px 0; background-color: #f0f4f9; text-align: center; } .footer-content { font-size: 14px; color: #606266; } .footer-content p { margin: 5px 0; } /* 添加响应式设计 */ @media (max-height: 800px) { .login-box { padding: 20px; max-height: calc(100vh - 80px); /* 调整最大高度 */ } .login-header h2 { font-size: 24px; } .input-item { margin-bottom: 15px; } .login-button { height: 40px; font-size: 16px; } } /* 覆盖 NaiveUI 默认样式 */ :deep(input:-webkit-autofill), :deep(input:-webkit-autofill:hover), :deep(input:-webkit-autofill:focus), :deep(input:-webkit-autofill:active) { -webkit-box-shadow: 0 0 0 30px white inset !important; -webkit-text-fill-color: #333 !important; } :deep(.n-input) { max-width: 100%; background-color: transparent !important; } :deep(.n-input__input) { height: 56px; /* 稍微增加高度 */ font-size: 18px; /* 增加字体大小 */ background-color: transparent !important; } :deep(.n-input__input-el) { height: 56px; /* 确保输入元素高度与外层一致 */ line-height: 56px; /* 设置行高等于高度，使文本垂直居中 */ font-size: 18px; /* 确保字体大小一致 */ background-color: transparent !important; padding-top: 0; /* 移除顶部内边距 */ padding-bottom: 0; /* 移除底部内边距 */ } :deep(.n-input__prefix) { margin-right: 12px; /* 稍微增加图标和文本之间的距离 */ } :deep(.n-input__prefix-icon) { font-size: 20px; /* 增加图标大小 */ } /* 调整复选框和按钮样式以保持一致性 */ :deep(.n-checkbox) { font-size: 16px; } .login-button { width: 140px; height: 56px; /* 调整按钮高度与输入框一致 */ font-size: 18px; } /* 添加响应式设计 */ @media (max-height: 800px) { :deep(.n-input__input), :deep(.n-input__input-el) { height: 48px; /* 在较小屏幕上稍微减小高度 */ line-height: 48px; font-size: 16px; /* 在较小屏幕上稍微减小字体大小 */ } :deep(.n-input__prefix-icon) { font-size: 18px; /* 在较小屏幕上稍微减小图标大小 */ } .login-button { height: 48px; font-size: 16px; } } .login-content { display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; max-width: 100%; } .login-form-section { flex: 0 0 auto; max-width: 600px; display: flex; flex-direction: column; align-items: center; padding-right: 20px; } .login-logo, .login-header, .login-form { width: 100%; text-align: center; } .login-form { display: flex; flex-direction: column; align-items: center; } .input-item, .login-actions { width: 100%; } .wecom-login-section { flex: 0 0 40%; max-width: 100%; /* 添加这一行 */ display: flex; flex-direction: column; justify-content: center; align-items: center; padding-left: 10px; /* 添加左侧内边距 */ border-left: 1px solid #e0e0e0; height: 100%; } /* 修改 n-spin 相关样式 */ .wecom-login-section :deep(.n-spin-container) { width: 100%; height: 100%; margin: 0; /* 移除可能的外边距 */ padding: 0; /* 移除可能的内边距 */ } .wecom-login-section :deep(.n-spin-content) { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; margin: 0; /* 移除可能的外边距 */ padding: 0; /* 移除可能的内边距 */ } /* 确保 wecomLoginContainer 内的元素居中 */ .wecom-login-section #wecomLoginContainer { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; margin: 0; /* 移除可能的外边距 */ padding: 0; /* 移除可能的内边距 */ } /* 移除 wecom-login-section 内部元素的垂直间距 */ .wecom-login-section>* { margin: 0; /* 移除垂直间距 */ } @media (max-width: 1024px) { .login-content { flex-direction: column; align-items: center; height: auto; /* 在小屏幕上允许高度自适应 */ } .login-form-section, .wecom-login-section { flex: 0 0 100%; max-width: 100%; padding-right: 0; padding-left: 0; } .login-form-section { margin-bottom: 40px; border-bottom: 1px solid #e0e0e0; /* 在小屏幕上添加底部边框 */ padding-bottom: 40px; /* 添加底部内边距 */ } .wecom-login-section { border-left: none; padding-top: 40px; height: auto; } .login-box { padding: 20px; /* 减少内边距 */ } } /* 修改调试模式样式 */ :global(.debug-mode) div { position: relative !important; } :global(.debug-mode) div::after { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; border: 0.2px solid rgba(128, 128, 128, 0.5); pointer-events: none; z-index: 9999; } :global(.debug-mode) div::before { content: attr(class); position: absolute; top: 0; left: 0; background: rgba(255, 255, 255, 0.8); padding: 2px 4px; font-size: 10px; color: #666; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 100%; box-sizing: border-box; z-index: 10000; pointer-events: none; } /* 调整输入框样式 */ :deep(.n-input__placeholder) { text-align: left; padding-left: 40px; /* 整此值以匹配图标和文本之间的距离 */ } :deep(.n-input__input:not(:placeholder-shown)) { text-align: left; padding-left: 40px; /* 确保输入的文本与 placeholder 对齐 */ } :deep(.n-input__input:focus::placeholder) { color: transparent; } .footer-text { display: flex; align-items: center; justify-content: center; } .footer-icon { width: 1.2rem; height: 1.2rem; margin-right: 0.26rem; } .login-logo { width: 100%; text-align: center; margin-top: 40px; /* 增加上边距 */ margin-bottom: 20px; /* 可选：如果需要，也可以增加下边距 */ } .register-link { width: 100%; max-width: 458px; text-align: center; margin-top: 20px; } .register-link :deep(.n-button) { font-size: 14px; color: #1890ff; } .register-link :deep(.n-button:hover) { color: #40a9ff; } /* 响应式设计部分 */ @media (max-width: 1024px) { .register-link { margin-top: 15px; } } .error-message { color: #ff4d4f; font-size: 14px; margin-top: 5px; display: block; } :deep(.n-input.n-input--error) { border-color: #ff4d4f; } :deep(.n-input.n-input--error:hover), :deep(.n-input.n-input--error:focus) { border-color: #ff7875; box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2); } /* 添加这个新的样式来调整输入框在错误状态下的内边距 */ :deep(.n-input.n-input--error .n-input__input-el) { padding-right: 30px; /* 为错误图标留出空间 */ } /* 添加这个新的样式来调整错误图标的位置 */ :deep(.n-input.n-input--error .n-input__suffix) { right: 10px; /* 调整错误图标的位置 */ } /* 确保所有可能导致溢出的元素都不会超过其容器宽度 */ .login-logo img, .input-item, .login-actions, .register-link { max-width: 100%; } </style><template> <div class="menu-page"> <n-space class="toolbar"> <n-button type="primary" @click="refreshCache" round> <template #icon> <n-icon><refresh-outline /></n-icon> </template> 刷新数据 </n-button> <n-button type="success" @click="showAddRootMenu" round> <template #icon> <n-icon><add-outline /></n-icon> </template> 新增菜单 </n-button> <n-button type="error" @click="batchDelete" round> <template #icon> <n-icon><trash-outline /></n-icon> </template> 选中删除 </n-button> </n-space> <n-data-table ref="menuTable" :columns="columns" :data="menuList" :row-key="row => row.id" :children-key="'children'" :expanded-row-keys="expandedKeys" @update:expanded-row-keys="handleExpand" @update:checked-row-keys="handleCheck" > <template #empty> <span>暂无数据</span> </template> </n-data-table> <n-modal v-model:show="dialogVisible" :title="dialogTitle" preset="card" style="width: 30%"> <n-form :model="menuForm" label-placement="left" label-width="100px"> <n-form-item label="父菜单" v-if="menuForm.parentId"> <n-input v-model:value="parentMenuName" disabled /> </n-form-item> <n-form-item label="菜单名称"> <n-input v-model:value="menuForm.menuLabel" /> </n-form-item> <n-form-item label="节点路由"> <n-input v-model:value="menuForm.menuPath" /> </n-form-item> <n-form-item label="是否显示"> <n-switch v-model:value="menuForm.visible" /> </n-form-item> <n-form-item label="文件路径"> <n-input v-model:value="menuForm.viewPath" /> </n-form-item> <n-form-item label="节点图标"> <n-input v-model:value="menuForm.menuIcon" /> </n-form-item> <n-form-item label="序号"> <n-input-number v-model:value="menuForm.menuOrder" :min="1" /> </n-form-item> </n-form> <template #footer> <n-space justify="end"> <n-button @click="dialogVisible = false">取消</n-button> <n-button type="primary" @click="saveMenu">确定</n-button> </n-space> </template> </n-modal> </div> </template> <script setup> import { ref, onMounted, h, } from 'vue'; import { NButton, NSpace, NSwitch, NDataTable, useDialog, NIcon } from 'naive-ui'; import { RefreshOutline, AddOutline, TrashOutline, PencilOutline } from '@vicons/ionicons5'; import { menuApi } from '@/api/menus'; import message from '@/utils/messages'; const menuList = ref([]); const dialogVisible = ref(false); const dialogTitle = ref(''); const menuForm = ref({ id: null, menuLabel: '', menuPath: '', visible: true, viewPath: '', menuIcon: '', menuOrder: 1, parentId: null }); const selectedMenus = ref([]); const selectedParentMenu = ref(null); const parentMenuName = ref(''); const menuTable = ref(null); const currentRow = ref(null); const expandedKeys = ref([]); const dialog = useDialog(); function createColumns() { return [ { type: 'selection', width: 50, align: 'center' }, { title: '菜单名称', key: 'menuLabel', width: 180, align: 'left', render(row) { const indent = row.level * 20; return h('span', { style: `padding-left: ${indent}px;` }, row.menuLabel); } }, { title: '图标', key: 'menuIcon', width: 100, align: 'center', render(row) { return h('div', { innerHTML: row.menuIcon }); } }, { title: '节点路由', key: 'menuPath', width: 180, align: 'center' }, { title: '文件路径', key: 'viewPath', width: 180, align: 'center' }, { title: '序号', key: 'menuOrder', width: 80, align: 'center' }, { title: '是否显示', key: 'visible', width: 100, align: 'center', render(row) { return h(NSwitch, { value: row.visible, loading: updatingVisibility.value.has(row.id), onUpdateValue: (value) => toggleVisibility(row, value) }); } }, { title: '操作', key: 'actions', width: 120, // 减小列宽 align: 'center', render(row) { return h(NSpace, { justify: 'center', size: 'small' }, () => [ h(NIcon, { component: AddOutline, size: 20, // 稍微减小图标大小 color: '#18a058', style: { cursor: 'pointer' }, onClick: () => addSubMenu(row) }), h(NIcon, { component: PencilOutline, size: 20, // 稍微减小图标大小 color: '#2080f0', style: { cursor: 'pointer' }, onClick: () => editMenu(row) }), h(NIcon, { component: TrashOutline, size: 24, // 稍微减小图标大小 color: '#d03050', style: { cursor: 'pointer' }, onClick: () => deleteMenu(row) }) ]); } } ]; } const columns = createColumns(); const updatingVisibility = ref(new Set()); // 用于跟踪正在更新可见性的行 onMounted(async () => { await fetchMenus(); }); async function fetchMenus() { const response = await menuApi.getMenus(); menuList.value = buildMenuTree(response.data); } function buildMenuTree(menus) { const map = new Map(); const tree = []; // 首先创建所有节点的映射 menus.forEach(menu => { map.set(menu.id, { ...menu, children: [], level: 0 }); }); // 然后构建树形结构 menus.forEach(menu => { const node = map.get(menu.id); if (menu.parentId === 1) { tree.push(node); } else { const parent = map.get(menu.parentId); if (parent) { parent.children.push(node); node.level = parent.level + 1; // 设置层级 } } }); return tree; } function refreshCache() { // 实现刷新缓存的逻辑 message.success('缓存已刷新'); } function showAddRootMenu() { dialogTitle.value = '新增菜单'; menuForm.value = { id: null, menuLabel: '', menuPath: '', visible: true, viewPath: '', menuIcon: '', menuOrder: 1, parentId: 1 }; parentMenuName.value = '系统菜单'; dialogVisible.value = true; } function addSubMenu(row) { dialogTitle.value = '新增子菜单'; menuForm.value = { id: null, menuLabel: '', menuPath: '', visible: true, viewPath: '', menuIcon: '', menuOrder: 1, parentId: row.id }; parentMenuName.value = row.menuLabel; dialogVisible.value = true; } function editMenu(row) { dialogTitle.value = '编辑菜单'; menuForm.value = { ...row }; dialogVisible.value = true; } async function saveMenu() { if (menuForm.value.id) { await menuApi.updateMenu(menuForm.value); message.success('菜单更新成功'); } else { await menuApi.createMenu(menuForm.value); message.success('菜单创建成功'); } dialogVisible.value = false; await fetchMenus(); } async function batchDelete() { if (selectedMenus.value.length === 0) { message.warning('请先选择要删除的菜单'); return; } dialog.warning({ title: '警告', content: `确定要删除选中的 ${selectedMenus.value.length} 个菜单吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { const ids = selectedMenus.value.map(menu => menu.id); await deleteMenus(ids); } }); } async function deleteMenu(row) { dialog.warning({ title: '警告', content: '确定要删除该菜单？', positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { await deleteMenus([row.id]); } }); } async function deleteMenus(ids) { try { await menuApi.deleteMenus(ids); message.success('菜单删除成功'); await fetchMenus(); } catch (error) { message.error('删除菜单失败'); console.error('删除菜单时出错:', error); } } async function toggleVisibility(row, value) { updatingVisibility.value.add(row.id); // 开始更新，添加到集合中 try { await menuApi.updateMenu({ ...row, visible: value }); message.success('菜单可见性已更新'); await fetchMenus(); // 在成功更新后刷新列表 } catch (error) { message.error('更新菜单可见性失败'); console.error('更新菜单可见性时出错:', error); } finally { updatingVisibility.value.delete(row.id); // 更新完成，从集合中移除 } } function handleCheck(checkedKeys) { selectedMenus.value = checkedKeys; selectedParentMenu.value = checkedKeys.length === 1 ? checkedKeys[0] : null; } function findMenuById(menus, id) { for (const menu of menus) { if (menu.id === id) { return menu; } if (menu.subMenus && menu.subMenus.length > 0) { const found = findMenuById(menu.subMenus, id); if (found) { return found; } } } return null; } function handleExpand(keys) { expandedKeys.value = keys; } // 修改 handleRowClick 函数来展开/折叠子节点 function handleRowClick(row) { const index = expandedKeys.value.indexOf(row.id); if (index > -1) { // 如果已经展开，就折叠 expandedKeys.value.splice(index, 1); } else { // 如果未展开，就展开 expandedKeys.value.push(row.id); } } </script> <style scoped> .menu-page { padding: 20px; } .toolbar { margin-bottom: 20px; } :deep(.n-data-table) { margin-top: 20px; } :deep(.n-data-table-td) { text-align: center; } :deep(.n-button) { margin-right: 8px; } /* 新增样式，确保所有单元格内容居中 */ :deep(.n-data-table-td .n-data-table-td__content) { display: flex; justify-content: center; align-items: center; } /* 菜单名称列的特殊处理，保持左对齐但考虑缩进 */ :deep(.n-data-table-td .n-data-table-td__content span) { text-align: left; width: 100%; } /* 为菜单名称列添加新的样式 */ :deep(.n-data-table-td[data-col-key="menuLabel"] .n-data-table-td__content) { justify-content: flex-start; /* 左对齐内容 */ } /* 添加以下样式来增加图标之间的间距 */ :deep(.n-icon) { margin: 0 2px; } /* 为操作栏添加新的样式 */ :deep(.n-data-table-td[data-col-key="actions"] .n-space) { gap: 4px !important; } </style><template> <div class="not-found"> <h1>404</h1> <p>页面不存在</p> <n-button @click="$router.push('/')">返回首页</n-button> </div> </template> <style scoped> .not-found { display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; } </style><template> <div class="login-container"> <div class="login-box"> <div class="login-content"> <div class="login-form-section"> <div class="login-logo"> <img src="@/assets/images/qj-logo-Dgc-aGJV.png" alt="Logo" style="width: 450px; height: auto;"> </div> <div class="login-header"> </div> <div class="login-form"> <div v-if="!showVerificationInput && !showPasswordInput && !showOrganizationInput && !showSuccessView" class="input-item"> <div class="input-wrapper"> <n-input ref="emailInput" v-model:value="email" placeholder="请输入您的邮箱" autocomplete="off" :input-props="{ style: 'text-align: left; font-size: 18px;' }" @blur="validateEmail" @keyup.enter="handleEnter" :status="emailError ? 'error' : undefined" > <template #prefix> <n-icon><mail-outline /></n-icon> </template> </n-input> <span v-if="emailError" class="error-message">{{ emailError }}</span> </div> <div class="button-wrapper"> <n-button type="primary" class="send-code-button" @click="sendVerificationCode" :disabled="!isEmailValid || isSending"> {{ isSending ? `重新发送 (${countdown})` : '发送验证码' }} </n-button> </div> </div> <transition name="slide-fade"> <div v-if="showVerificationInput && !showPasswordInput && !showOrganizationInput && !showSuccessView" class="verification-step"> <p class="verification-prompt">请输入 {{ email }} 收到的验证码</p> <div class="verification-code-input"> <div class="code-input-wrapper"> <input v-for="(digit, index) in 6" :key="index" v-model="verificationCode[index]" type="text" maxlength="1" @input="onCodeInput(index)" @keydown="onKeyDown($event, index)" @paste.prevent="onPaste" ref="codeInputs"> </div> </div> <div class="resend-wrapper"> <n-button text @click="resendCode" :disabled="isSending"> {{ isSending ? `重新发送 (${countdown})` : '重新发送验证码' }} </n-button> </div> </div> </transition> <transition name="slide-fade"> <div v-if="showPasswordInput && !showOrganizationInput && !showSuccessView" class="password-step"> <div class="password-input-wrapper"> <n-input ref="passwordInput" v-model:value="password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码" @keyup.enter="confirmPassword" @input="filterPassword" > <template #suffix> <n-icon @click="togglePasswordVisibility" class="password-toggle"> <eye-outline v-if="showPassword" /> <eye-off-outline v-else /> </n-icon> </template> </n-input> </div> <div class="password-input-wrapper"> <n-input v-model:value="confirmPasswordValue" :type="showPassword ? 'text' : 'password'" placeholder="请再次输入密码" @keyup.enter="confirmPassword" @input="filterConfirmPassword" /> </div> <div class="button-wrapper password-button-wrapper"> <n-button type="primary" @click="confirmPassword" :disabled="!isPasswordValid"> 下一步 </n-button> </div> <p class="password-hint">密码至少9位，需包含大小写字母、数字、符号中的任意3种</p> </div> </transition> <transition name="slide-fade"> <div v-if="showOrganizationInput && !showSuccessView" class="organization-step"> <div class="organization-input-wrapper"> <n-input ref="organizationInput" v-model:value="organizationName" placeholder="请输入个人或组织名称" @blur="validateOrganizationName" maxlength="32" /> <span v-if="organizationNameError" class="error-message">{{ organizationNameError }}</span> </div> <div class="input-wrapper"> <n-input v-model:value="userName" placeholder="请输入您的称呼" @blur="validateUserName" maxlength="32" /> <span v-if="userNameError" class="error-message">{{ userNameError }}</span> </div> <div class="input-wrapper"> <n-input v-model:value="phoneNumber" placeholder="请输入您的手机号码" @blur="validatePhoneNumber" @input="validatePhoneNumber" @keyup.enter="handlePhoneNumberEnter" maxlength="11" /> <span v-if="phoneNumberError" class="error-message">{{ phoneNumberError }}</span> </div> <div class="button-wrapper organization-button-wrapper"> <n-button type="primary" @click="completeRegistration" :disabled="!isOrganizationInfoValid" > 完成注册 </n-button> </div> </div> </transition> <transition name="slide-fade"> <div v-if="showSuccessView" class="success-step"> <div class="success-icon"> <n-icon size="48" color="#52c41a"> <checkmark-circle-outline /> </n-icon> </div> <h2 class="success-title">注册成功</h2> <p class="success-description">您的账号已经创建完成</p> <div class="success-actions"> <n-button type="primary" size="large" @click="goToLogin"> 立即登录 </n-button> </div> </div> </transition> <div v-if="!showSuccessView" class="register-link"> <n-button text @click="goToLogin">已有账号？立即登录</n-button> </div> </div> </div> </div> </div> </div> </template> <script setup> import { ref, computed, nextTick, watch, onMounted } from 'vue' import { NInput, NButton, NIcon } from 'naive-ui' import { MailOutline, EyeOutline, EyeOffOutline, CheckmarkCircleOutline } from '@vicons/ionicons5' import { useRouter } from 'vue-router' import { doPost, doPut, doGet } from '@/utils/requests' import message from '@/utils/messages' const router = useRouter() // 将所有 ref 声明集中到一起 const email = ref('') const emailError = ref('') const isSending = ref(false) const countdown = ref(60) const showVerificationInput = ref(false) const verificationCode = ref(['', '', '', '', '', '']) const showPasswordInput = ref(false) const password = ref('') const confirmPasswordValue = ref('') const showPassword = ref(false) const showOrganizationInput = ref(false) const organizationName = ref('') const organizationNameError = ref('') const userName = ref('') const userNameError = ref('') const phoneNumber = ref('') const phoneNumberError = ref('') const showSuccessView = ref(false) const uid = ref(null) // 所有输入框的 ref const emailInput = ref(null) const passwordInput = ref(null) const organizationInput = ref(null) const codeInputs = ref([]) const isEmailValid = computed(() => { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/ return emailRegex.test(email.value) }) const validateEmail = () => { if (!email.value) { emailError.value = '邮箱不能为空' } else if (!isEmailValid.value) { emailError.value = '请输入有效的邮地址' } else { emailError.value = '' } } const resendCode = () => { if (!isSending.value) { sendVerificationCode() } } const sendVerificationCode = async () => { if (!isEmailValid.value) { return } isSending.value = true try { const encodedEmail = encodeURIComponent(email.value) const response = await doPost(`/auth-center/system/user/register/validation?email=${encodedEmail}`, {}) if (response.code === 0) { message.success('验证码已发送，请查收邮件') startCountdown() showVerificationInput.value = true nextTick(() => { codeInputs.value[0]?.focus() // 验证码输入框自动获取焦点 }) } else { throw new Error(response.message || '发送失败') } } catch (error) { console.error('发送验证码失败:', error) } finally { isSending.value = false } } const startCountdown = () => { const timer = setInterval(() => { countdown.value-- if (countdown.value <= 0) { clearInterval(timer) countdown.value = 60 isSending.value = false } }, 1000) } const handleEnter = () => { if (isEmailValid.value && !isSending.value) { sendVerificationCode() } } const onCodeInput = (index) => { if (verificationCode.value[index].length === 1) { if (index < 5) { codeInputs.value[index + 1]?.focus() } else { // 最后一个输入框填写完毕，检查是否所有输入框都已填写 if (verificationCode.value.every(digit => digit !== '')) { register() } } } } const onKeyDown = (event, index) => { if (event.key === 'Backspace' && index > 0 && verificationCode.value[index] === '') { codeInputs.value[index - 1]?.focus() } } const onPaste = (event) => { event.preventDefault() const pastedText = event.clipboardData.getData('text') const chars = pastedText.slice(0, 6).split('') verificationCode.value = chars.concat(Array(6 - chars.length).fill('')) nextTick(() => { const nextEmptyIndex = verificationCode.value.findIndex(v => v === '') if (nextEmptyIndex !== -1) { codeInputs.value[nextEmptyIndex]?.focus() } else { codeInputs.value[5]?.focus() // 如有输入框都已填写，触发注册操作 if (verificationCode.value.every(digit => digit !== '')) { register() } } }) } const goToLogin = () => { // 如果是从成功界面点击，则带上邮箱参数 if (showSuccessView.value) { router.push({ path: '/login', query: { email: email.value } }) } else { router.push('/login') } } const isPasswordValid = computed(() => { const hasUpperCase = /[A-Z]/.test(password.value) const hasLowerCase = /[a-z]/.test(password.value) const hasNumber = /\d/.test(password.value) const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password.value) const validTypes = [hasUpperCase, hasLowerCase, hasNumber, hasSymbol].filter(Boolean).length return password.value.length >= 9 && validTypes >= 3 && password.value === confirmPasswordValue.value }) // 添加手机号码格式验证的计算属性 const isPhoneNumberValid = computed(() => { const phoneRegex = /^1[3-9]\d{9}$/ return phoneRegex.test(phoneNumber.value) }) // 修改组织信息验证的计算属性 const isOrganizationInfoValid = computed(() => { return organizationName.value && !organizationNameError.value && userName.value && !userNameError.value && phoneNumber.value && !phoneNumberError.value && isPhoneNumberValid.value // 添加手机号码格式验证 }) const togglePasswordVisibility = () => { showPassword.value = !showPassword.value } const tid = ref(null) const register = async () => { const code = verificationCode.value.join('') try { const response = await doPost(`/auth-center/system/user/register?email=${encodeURIComponent(email.value)}&verifyCode=${code}`, {}) if (response.code === 0) { uid.value = response.data.uid tid.value = response.data.tid showVerificationInput.value = false showPasswordInput.value = true nextTick(() => { passwordInput.value?.focus() }) } else { throw new Error(response.message || '验证失败') } } catch (error) { console.error('验证失败:', error) } } const confirmPassword = async () => { if (!isPasswordValid.value || !uid.value) return try { const response = await doPut(`/auth-center/system/user/${uid.value}`, { passwd: password.value, tenantId: tid.value }) if (response.code === 0) { showPasswordInput.value = false showOrganizationInput.value = true // 显示组织名称输入界面 nextTick(() => { organizationInput.value?.focus() // 组织名称输入框自动获取焦点 }) } else { throw new Error(response.message || '设置密码失败') } } catch (error) { console.error('设置密码失败:', error) } } const validateOrganizationName = async () => { if (!organizationName.value) { organizationNameError.value = '组织名称不能为空' return } else if (organizationName.value.length > 32) { organizationNameError.value = '组织名称不能超过32个字符' return } try { const response = await doGet(`/auth-center/system/department/exists?name=${organizationName.value}&parentId=0`) if (response.code === 0 && response.data.exists) { organizationNameError.value = '该组织名称已存在，请更换其他名称' } else { organizationNameError.value = '' } } catch (error) { console.error('检查组织名称失败:', error) organizationNameError.value = '该名称已被占用' } } const validateUserName = () => { if (!userName.value) { userNameError.value = '称呼不能为空' } else if (userName.value.length > 32) { userNameError.value = '称呼不能超过32个字符' } else { userNameError.value = '' } } // 修改手机号码验证函数，在输入时实时验证 const validatePhoneNumber = () => { if (!phoneNumber.value) { phoneNumberError.value = '手机号码不能为空' return false } else if (!isPhoneNumberValid.value) { phoneNumberError.value = '请输入有效的11位手机号码' return false } else { phoneNumberError.value = '' return true } } const completeRegistration = async () => { if (!isOrganizationInfoValid.value) return try { // 先创建构 const departmentResponse = await doPost('/auth-center/system/department', { name: organizationName.value, parentId: 0, tenantId: tid.value }) if (departmentResponse.code !== 0) { throw new Error(departmentResponse.message || '创建机构失败') } const departmentId = departmentResponse.data.id // 然后更新用户信息 const userResponse = await doPut(`/auth-center/system/user/${uid.value}`, { nickname: userName.value, workMobile: phoneNumber.value, disabled: false, departmentId: departmentId, tenantId: tid.value }) if (userResponse.code !== 0) { throw new Error(userResponse.message || '更新用户信息失败') } // 显示成功界面 showOrganizationInput.value = false showSuccessView.value = true // 移除自动跳转的代码 // setTimeout(() => { // router.push({ path: '/login', query: { email: email.value } }) // }, 3000) } catch (error) { console.error('完成注册失败:', error) message.error(error.message || '注册失败，请稍后重试') } } const filterPassword = (value) => { password.value = value.replace(/[^\x00-\xff]/g, '') } const filterConfirmPassword = (value) => { confirmPasswordValue.value = value.replace(/[^\x00-\xff]/g, '') } // 在组件挂载时让邮箱输入框获取焦点 onMounted(() => { // 使用 setTimeout 确保在 DOM 完全渲染后设置焦点 setTimeout(() => { emailInput.value?.input?.focus() }, 100) }) // 添加手机号码回车事件处理函数 const handlePhoneNumberEnter = () => { if (isOrganizationInfoValid.value) { completeRegistration() } } </script> <style scoped> /* 复制 LoginPage.vue 中的所有样式到这里 */ /* 注意：可能需要调整一些类名和样式以适应注册页面的布局 */ .login-container { display: flex; flex-direction: column; justify-content: space-between; align-items: center; min-height: 100vh; height: 100vh; background-color: #f0f4f9; overflow: hidden; box-sizing: border-box; padding: 20px 0; overflow-x: hidden; } .login-box { display: flex; justify-content: center; align-items: center; background-color: #ffffff; border-radius: 8px; padding: 40px; width: 90%; max-width: 100%; height: auto; max-height: calc(100vh - 120px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); margin: auto; overflow-y: auto; box-sizing: border-box; overflow-x: hidden; } /* 其他样式保持不变，直接从 LoginPage.vue 复制过来 */ /* ... */ .input-wrapper, .password-input-wrapper, .organization-input-wrapper { position: relative; width: 100%; margin-bottom: 20px; /* 减少底部边距 */ } .error-message { position: absolute; top: -8px; /* 将错误消息移到输入框上方 */ left: 12px; /* 左侧偏移 */ color: #ff4d4f; font-size: 16px; /* 稍微减小字体大小 */ line-height: 1; padding: 0 4px; background-color: white; /* 添加白色背景，使其看起来像切割了边框 */ z-index: 1; } :deep(.n-input) { max-width: 100%; } :deep(.n-input__input-el) { height: 56px; line-height: 56px; font-size: 18px; } :deep(.n-input__prefix) { height: 56px; display: flex; align-items: center; } :deep(.n-input.n-input--error) { border-color: #ff4d4f; /* 确保错误状态下输入框边框为红色 */ } :deep(.n-input.n-input--error:hover), :deep(.n-input.n-input--error:focus) { border-color: #ff7875; box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2); } .button-wrapper { position: relative; width: 100%; margin-top: 20px; /* 调整按钮与输入框的间距 */ } .send-code-button { position: absolute; right: 0; top: 0; /* 调整按钮位置，使其与输入框顶部对齐 */ height: 56px; font-size: 16px; padding: 0 20px; } /* 其他样式保持不变 */ .login-header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; /* 增加这一行 */ } .login-header h2 { font-size: 28px; color: #333; margin: 0; /* 添加这一行以移除默认下边距 */ } /* 他样式保持不变 */ .verification-step { margin-top: 20px; } .verification-prompt { font-size: 16px; color: #333; margin-bottom: 15px; text-align: center; } .resend-wrapper { text-align: center; margin-top: 15px; } /* 其他现有的样式保持不变 */ .slide-fade-enter-active, .slide-fade-leave-active { transition: all 0.3s ease; } .slide-fade-enter-from, .slide-fade-leave-to { transform: translateY(-20px); opacity: 0; } .verification-code-input { margin-top: 20px; } .code-input-wrapper { display: flex; justify-content: space-between; gap: 10px; /* 添加间距 */ } .code-input-wrapper input { width: 50px; /* 调整为正方形 */ height: 50px; /* 调整为正方形 */ font-size: 18px; text-align: center; border: 1px solid #d9d9d9; border-radius: 4px; outline: none; padding: 0; /* 移除内边距 */ } .code-input-wrapper input:focus { border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); } .password-step { margin-top: 20px; position: relative; /* 添加这一行 */ } .password-prompt { font-size: 16px; color: #333; margin-bottom: 15px; text-align: center; } .password-input-wrapper { margin-bottom: 15px; } .password-toggle { cursor: pointer; } .password-button-wrapper { position: absolute; /* 修改为绝对定位 */ right: 0; /* 靠右齐 */ top: 100%; /* 放置在密码输入框下方 */ width: auto; /* 允许按钮宽度适应 */ margin-top: 10px; /* 与输入框保持一定距离 */ } .password-button-wrapper .n-button { height: 56px; /* 保持与其他按钮一致的高度 */ font-size: 16px; /* 保持与其他按钮一致的字体大小 */ padding: 0 20px; /* 保持与其他按钮一致的内距 */ } .password-hint { font-size: 12px; color: #888; margin-top: 5px; } .organization-step { margin-top: 20px; position: relative; } .organization-input-wrapper { margin-bottom: 15px; position: relative; } .organization-button-wrapper { position: absolute; right: 0; top: 100%; width: auto; margin-top: 10px; } .organization-button-wrapper .n-button { height: 56px; font-size: 16px; padding: 0 20px; } .input-wrapper { margin-bottom: 15px; position: relative; } .success-step { display: flex; flex-direction: column; align-items: center; padding: 40px 0; text-align: center; } .success-icon { margin-bottom: 24px; animation: zoomIn 0.3s ease-in-out; } .success-title { font-size: 24px; color: #52c41a; margin-bottom: 16px; animation: fadeIn 0.3s ease-in-out 0.1s both; } .success-description { font-size: 16px; color: #666; margin-bottom: 32px; animation: fadeIn 0.3s ease-in-out 0.2s both; } .success-actions { animation: fadeIn 0.3s ease-in-out 0.3s both; } @keyframes zoomIn { from { opacity: 0; transform: scale(0.5); } to { opacity: 1; transform: scale(1); } } @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } } .success-step { display: flex; flex-direction: column; align-items: center; padding: 40px 0; text-align: center; } .success-icon { margin-bottom: 24px; animation: zoomIn 0.3s ease-in-out; } .success-title { font-size: 24px; color: #52c41a; margin-bottom: 16px; animation: fadeIn 0.3s ease-in-out 0.1s both; } .success-description { font-size: 16px; color: #666; margin-bottom: 32px; animation: fadeIn 0.3s ease-in-out 0.2s both; } .success-actions { animation: fadeIn 0.3s ease-in-out 0.3s both; } @keyframes zoomIn { from { opacity: 0; transform: scale(0.5); } to { opacity: 1; transform: scale(1); } } @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } } </style><template> <div class="role-management"> <!-- 工具栏 --> <n-space class="toolbar"> <n-button type="primary" @click="refreshRoles" round> <template #icon> <n-icon><RefreshOutline /></n-icon> </template> 刷新数据 </n-button> <n-button type="info" @click="showAddDialog" round> <!-- 修改按钮颜色为蓝色 --> <template #icon> <n-icon><AddOutline /></n-icon> </template> 新增角色 </n-button> <n-button type="error" @click="batchDelete" :disabled="!selectedRoles.length" round> <template #icon> <n-icon><TrashOutline /></n-icon> </template> 批量删除 </n-button> </n-space> <!-- 角色列表 --> <n-data-table :columns="columns" :data="roles" :row-key="row => row.id" @update:checked-row-keys="handleSelectionChange" /> <!-- 编辑角色对话框 --> <n-modal v-model:show="dialogVisible" :title="isEdit ? '编辑角色' : '新增角色'" :style="{ width: dialogWidth }" preset="card" :mask-closable="false" :auto-focus="false" :transformOrigin="'center'" :class="{ 'fullscreen-modal': isFullscreen }" > <template #header-extra> <n-button v-if="showMaximizeButton" quaternary circle @click="toggleFullscreen"> <template #icon> <n-icon> <component :is="isFullscreen ? ContractOutline : ExpandOutline" /> </n-icon> </template> </n-button> </template> <n-form ref="formRef" :model="form" :rules="rules" label-placement="left" label-width="auto" require-mark-placement="right-hanging" :style="{ maxWidth: '640px' }" > <n-form-item label="角色名称" path="roleName"> <n-input v-model:value="form.roleName" placeholder="请输入角色名称" /> </n-form-item> <n-form-item label="角色代码" path="roleCode"> <n-input v-model:value="form.roleCode" placeholder="请输入角色代码" /> </n-form-item> <n-grid :cols="2" :x-gap="24"> <n-grid-item> <n-form-item label="菜单权限" path="menus"> <n-tree ref="menuTreeRef" :data="menuTree" checkable cascade :key-field="'id'" :label-field="'menuLabel'" :children-field="'subMenus'" :checked-keys="form.menus" @update:checked-keys="handleMenuCheck" :default-expand-all="true" :selectable="false" :show-irrelevant-nodes="false" /> </n-form-item> </n-grid-item> <n-grid-item> <n-form-item label="数据权限" path="dataScope"> <n-tooltip placement="top"> <template #trigger> <n-icon><HelpCircleOutline /></n-icon> </template> 不选择数据权限时仅能查询自己名下的数据 </n-tooltip> <n-tree ref="dataScopeTreeRef" :data="departmentTree" checkable cascade :key-field="'id'" :label-field="'name'" :children-field="'children'" @update:checked-keys="handleDataScopeCheck" :default-expand-all="true" :selectable="false" :show-irrelevant-nodes="false" /> </n-form-item> </n-grid-item> </n-grid> </n-form> <template #footer> <n-space justify="end"> <n-button @click="dialogVisible = false">取消</n-button> <n-button type="primary" @click="handleSave">确定</n-button> </n-space> </template> </n-modal> </div> </template> <script setup> import { ref, onMounted, computed, h, nextTick, onUnmounted, watch } from 'vue'; import { useDialog } from 'naive-ui'; import { RefreshOutline, AddOutline, TrashOutline, PencilOutline, HelpCircleOutline, ExpandOutline, ContractOutline } from '@vicons/ionicons5'; import { getRoles, getMenus, saveRole, updateRole, deleteRole, getRoleDetail } from '@/api/roles'; import { getDepartments } from '@/api/users'; import messages from '@/utils/messages'; import { NButton, NSpace, NIcon, NModal } from 'naive-ui'; const roles = ref([]); const menuTree = ref([]); const dialogVisible = ref(false); const isEdit = ref(false); const formRef = ref(null); const menuTreeRef = ref(null); const selectedRoles = ref([]); const departmentTree = ref([]); const dataScopeTreeRef = ref(null); const form = ref({ roleName: '', roleCode: '', menus: [], dataScope: '' }); const rules = { roleName: [ { required: true, message: '请输入角色名称（2-20个汉字）', trigger: ['blur', 'input'], validator: (rule, value) => { if (!value) { return new Error('角色名称不能为空'); } else if (value.length < 2 || value.length > 20) { return new Error('角色名称长度应在2-20个字符之间'); } return true; } } ], roleCode: [ { required: true, message: '请输入角色代码（2-20个大写字母或下划线）', trigger: ['blur', 'input'], validator: (rule, value) => { if (!value) { return new Error('角色代码不能为空'); } else if (!/^[A-Z_]{2,20}$/.test(value)) { return new Error('角色代码应为2-20个大写字母'); } return true; } } ], menus: [ { type: 'array', required: true, message: '请至少选择一个菜单权限', trigger: 'change', validator: (rule, value) => { if (!value || value.length === 0) { return new Error('请至少选择一个菜单权限'); } return true; } } ], dataScope: [ { trigger: 'change', validator: (rule, value) => { return true; } } ] }; const dialog = useDialog(); const columns = [ { type: 'selection',align: 'center'}, { title: '角色ID', key: 'id' ,align: 'center'}, { title: '角色代码', key: 'roleCode',align: 'center' }, { title: '角色名称', key: 'roleName' ,align: 'center'}, { title: '操作', key: 'actions', width: 120, // 减小列宽 align: 'center', render: (row) => { return h(NSpace, { justify: 'center', size: 'small' }, () => [ h(NIcon, { component: PencilOutline, size: 20, color: '#2080f0', style: { cursor: 'pointer' }, onClick: () => handleEdit(row) }), h(NIcon, { component: TrashOutline, size: 20, color: '#d03050', style: { cursor: 'pointer' }, onClick: () => handleDelete(row) }) ]); } } ]; // 获取角色列表 const fetchRoles = async () => { try { const response = await getRoles(); roles.value = response.data.map(role => ({ ...role, menus: role.menus || [] })); } catch (error) { messages.error('获角色列失败'); } }; // 获取菜单树 const fetchMenuTree = async () => { try { const response = await getMenus(); menuTree.value = buildMenuTree(response.data); } catch (error) { messages.error('获取菜单列表失败'); } }; // 构建菜单树 const buildMenuTree = (menus) => { const menuMap = {}; menus.forEach(menu => menuMap[menu.id] = { ...menu, subMenus: [] }); const rootMenus = []; menus.forEach(menu => { if (menu.parentId === 1) { rootMenus.push(menuMap[menu.id]); } else { menuMap[menu.parentId].subMenus.push(menuMap[menu.id]); } }); // 移除空的 subMenus 数组 const removeEmptySubMenus = (menu) => { if (menu.subMenus.length === 0) { delete menu.subMenus; } else { menu.subMenus.forEach(removeEmptySubMenus); } }; rootMenus.forEach(removeEmptySubMenus); return rootMenus; }; // 获取部门树 const fetchDepartmentTree = async () => { try { const response = await getDepartments(); departmentTree.value = buildDepartmentTree(response.data); } catch (error) { messages.error('获取组织机构树失败'); } }; // 构建部门树 const buildDepartmentTree = (departments) => { const departmentMap = {}; departments.forEach(dept => departmentMap[dept.id] = { ...dept, children: [] }); const rootDepartments = []; departments.forEach(dept => { if (!dept.parentId) { rootDepartments.push(departmentMap[dept.id]); } else { departmentMap[dept.parentId].children.push(departmentMap[dept.id]); } }); // 移除空的 children 数组 const removeEmptyChildren = (dept) => { if (dept.children.length === 0) { delete dept.children; } else { dept.children.forEach(removeEmptyChildren); } }; rootDepartments.forEach(removeEmptyChildren); return rootDepartments; }; // 刷新角色列表 const refreshRoles = () => { fetchRoles(); }; // 显示新增对话框 const showAddDialog = () => { isEdit.value = false; form.value = { roleName: '', roleCode: '', menus: [], dataScope: '' }; dialogVisible.value = true; }; // 处理编辑 const handleEdit = async (row) => { isEdit.value = true; dialogVisible.value = true; try { const response = await getRoleDetail(row.id); const roleDetail = response.data; form.value = { id: roleDetail.id, roleName: roleDetail.roleName, roleCode: roleDetail.roleCode, menus: roleDetail.menus || [], dataScope: roleDetail.dataScope || '' }; } catch (error) { messages.error('获取角色详情失败'); } }; // 处理删除 const handleDelete = (row) => { dialog.warning({ title: '警告', content: '确定要删除这个角色吗？', positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await deleteRole(row.id); messages.success('删除成功'); fetchRoles(); } catch (error) { messages.error('删除失败'); } } }); }; // 处理批量删除 const batchDelete = () => { if (selectedRoles.value.length === 0) { messages.warning('请选择要删除的角色'); return; } dialog.warning({ title: '警告', content: `确定要删除这 ${selectedRoles.value.length} 个角色吗？`, positiveText: '确定', negativeText: '取消', onPositiveClick: async () => { try { await Promise.all(selectedRoles.value.map(role => deleteRole(role.id))); messages.success('批量删除成功'); fetchRoles(); } catch (error) { messages.error('批量删除失败'); } } }); }; // 处理选择变化 const handleSelectionChange = (selection) => { selectedRoles.value = selection; }; // 处理菜单选择 const handleMenuCheck = (checkedKeys) => { form.value.menus = checkedKeys; // 使用 Promise 风格的验证，并处理可能的验证错误 if (formRef.value) { formRef.value.validate() .then(() => { // 验证成功 }) .catch((errors) => { // 验证失败，处理错误 console.error('表单验证失败:', errors); }); } }; // 处理数据权限选择 const handleDataScopeCheck = (checkedKeys, info = {}) => { if (!Array.isArray(checkedKeys)) { console.error('checkedKeys is not an array:', checkedKeys); return; } if (info.checkedNodes && Array.isArray(info.checkedNodes)) { const leafNodes = info.checkedNodes.filter(node => !node.children || node.children.length === 0); form.value.dataScope = leafNodes.map(node => node.key).join(','); } else { // 如果 checkedNodes 不可用，我们直接使用 checkedKeys form.value.dataScope = checkedKeys.join(','); } }; // 保存或更新角色 const handleSave = async () => { if (!formRef.value) return; try { await formRef.value.validate(); const roleData = { ...form.value, menus: form.value.menus }; if (isEdit.value) { await updateRole(roleData); messages.success('角色更新成功'); } else { await saveRole(roleData); messages.success('角色创建成功'); } dialogVisible.value = false; fetchRoles(); } catch (errors) { console.error('表单验证失败:', errors); messages.error('请检查表单填写是否正确'); } }; const isFullscreen = ref(false); const showMaximizeButton = ref(false); // 计算话框宽度 const dialogWidth = computed(() => { if (isFullscreen.value) { return '100%'; } return window.innerWidth < 1920 ? '75%' : '30%'; }) // 监听窗口大小变化 const handleResize = () => { showMaximizeButton.value = window.innerWidth < 1920; if (window.innerWidth < 1920 && dialogVisible.value) { isFullscreen.value = true; } } // 切换全屏状态 const toggleFullscreen = () => { isFullscreen.value = !isFullscreen.value; } // 监听对话框可见性变化 watch(dialogVisible, (newValue) => { if (newValue && window.innerWidth < 1920) { isFullscreen.value = true; } else { isFullscreen.value = false; } }); onMounted(() => { fetchRoles(); fetchMenuTree(); fetchDepartmentTree(); window.addEventListener('resize', handleResize); handleResize(); // 初始化时调用一次 }) onUnmounted(() => { window.removeEventListener('resize', handleResize); }) </script> <style scoped> .role-management { padding: 20px; } .toolbar { margin-bottom: 20px; } /* 表格内容居中展示 */ :deep(.n-data-table-td) { text-align: center; } /* 确保所有单元格内容居中 */ :deep(.n-data-table-td .n-data-table-td__content) { display: flex; justify-content: center; align-items: center; } /* 调整操作栏图标的间距 */ :deep(.n-data-table-td[data-col-key="actions"] .n-space) { gap: 8px !important; } /* 如果需要特定列左对齐，可以添加如下样式 */ :deep(.n-data-table-td[data-col-key="roleName"] .n-data-table-td__content), :deep(.n-data-table-td[data-col-key="roleCode"] .n-data-table-td__content) { justify-content: flex-start; } /* 其他样式可以根据需要进行调整 */ .fullscreen-modal { width: 100% !important; height: 100% !important; margin: 0 !important; padding: 0 !important; max-width: 100% !important; max-height: 100% !important; } :deep(.fullscreen-modal .n-card-header) { padding: 14px 20px !important; } :deep(.fullscreen-modal .n-card__content) { height: calc(100vh - 120px) !important; overflow-y: auto !important; } :deep(.fullscreen-modal .n-card__footer) { padding: 14px 20px !important; } /* 添加以下样式来调整最大化按钮的位置 */ :deep(.n-card-header .n-card-header__extra) { display: flex; align-items: center; } :deep(.n-card-header .n-card-header__extra .n-button) { margin-right: 8px; } /* 调整关闭按钮的样式，使其与最大化按钮对齐 */ :deep(.n-modal .n-card-header .n-base-close) { top: 14px; right: 20px; } </style><script setup> import { ref, onMounted, h } from 'vue' import { NSpace, NCard, NGrid, NGridItem, NDataTable, NButton, NModal, NForm, NFormItem, NInput, NTreeSelect, NSelect, NSwitch, NIcon, NTree, NDropdown } from 'naive-ui' import { getDepartments, getDepartmentMembers, getRoles, getUserDetails, createUser, updateUser } from '@/api/users' import { AddCircleOutline,PencilOutline,TrashOutline} from '@vicons/ionicons5' import { Edit, TrashCan } from '@vicons/carbon' import {PlusSquareOutlined,MinusSquareOutlined} from '@vicons/antd' import {MoreVertical16Filled} from '@vicons/fluent' const departmentTree = ref([]) const selectedDepartmentKeys = ref([]) const users = ref([]) const pagination = ref({ page: 1, pageSize: 50, itemCount: 0, showSizePicker: true, pageSizes: [10, 20, 50, 100] }) const selectedRowKeys = ref([]) const showUserModal = ref(false) const editingUser = ref({}) const roleOptions = ref([]) const userForm = ref(null) const expandedKeys = ref([]) // 新增的状态变量 const showDepartmentModal = ref(false) const editingDepartment = ref({}) const departmentForm = ref(null) // 新增的部门表单规则 const departmentRules = { name: { required: true, message: '请输入部门名称', trigger: 'blur' }, } const userRules = { username: { required: true, message: '请输入用名', trigger: 'blur' }, nickname: { required: true, message: '请输入昵称', trigger: 'blur' }, passwd: { required: true, message: '请输入密码', trigger: 'blur' }, workMobile: { required: true, message: '请输入手机号', trigger: 'blur' }, departmentId: { required: true, message: '请选择所属部门', trigger: 'change' }, roles: { type: 'array', required: true, message: '请选择角色', trigger: 'change' } } const columns = [ { title: 'ID', key: 'id', width: 80 }, // 添加回 ID 列 { title: '昵称', key: 'nickname' }, { title: '手机号', key: 'workMobile' }, { title: '角色', key: 'roles', render: (row) => { if (!roleOptions.value || roleOptions.value.length === 0) return '' return row.roles .map(roleId => roleOptions.value.find(role => role.value === roleId)?.label) .filter(Boolean) // 过滤掉可能的 undefined 值 .join(', ') } }, { title: '操作', key: 'actions', render: (row) => { return h(NSpace, { align: 'center' }, { default: () => [ h(NSwitch, { value: !row.disabled, onUpdateValue: (value) => toggleUserStatus(row, value) }), h(NButton, { quaternary: true, circle: true, size: 'small', onClick: () => editUser(row) }, { default: () => h(NIcon, { component: Edit }) }), h(NButton, { quaternary: true, circle: true, size: 'small', onClick: () => deleteUser(row) }, { default: () => h(NIcon, { component: TrashCan }) }) ] }) } } ] onMounted(async () => { await fetchRoles() await fetchDepartments() if (departmentTree.value.length > 0) { selectedDepartmentKeys.value = [departmentTree.value[0].key] await fetchUsers(selectedDepartmentKeys.value[0]) } }) function buildTreeData(departments) { const options = [] const map = {} departments.forEach(dept => { map[dept.id] = { key: dept.id.toString(), label: dept.name, children: [], isLeaf: true // 初始假设所有节点都是叶子节点 } }) departments.forEach(dept => { if (dept.parentId === 0) { options.push(map[dept.id]) } else { const parent = map[dept.parentId] if (parent) { parent.children.push(map[dept.id]) parent.isLeaf = false // 如果有子节点，则不是叶子节点 } } }) return options } async function fetchDepartments() { try { const response = await getDepartments() departmentTree.value = buildTreeData(response.data) if (departmentTree.value.length > 0) { selectedDepartmentKeys.value = [departmentTree.value[0].key] // 初始化 expandedKeys 为所有顶级部门的 key expandedKeys.value = departmentTree.value.map(dept => dept.key) await fetchUsers(selectedDepartmentKeys.value[0]) } } catch (error) { console.error('Failed to fetch departments:', error) } } async function fetchUsers(departmentId) { try { const response = await getDepartmentMembers(departmentId, pagination.value.page, pagination.value.pageSize) users.value = response.data.map(user => ({ ...user, roles: user.roles || [] // 确保每个用户都有 roles 属性，即使它可能为空 })) pagination.value.itemCount = response.total } catch (error) { console.error('Failed to fetch users:', error) users.value = [] } } async function fetchRoles() { try { const response = await getRoles() roleOptions.value = response.data.map(role => ({ label: role.roleName, value: role.id })) } catch (error) { console.error('Failed to fetch roles:', error) roleOptions.value = [] // 如果获取失败，设置为空数组 } } function handlePageChange(page) { pagination.value.page = page fetchUsers(selectedDepartmentKeys.value[0]) } function handlePageSizeChange(pageSize) { pagination.value.pageSize = pageSize pagination.value.page = 1 fetchUsers(selectedDepartmentKeys.value[0]) } function handleCheck(rowKeys) { selectedRowKeys.value = rowKeys } function handleAddUser() { editingUser.value = { username: '', nickname: '', passwd: '', workMobile: '', departmentId: null, roles: [], disabled: false } showUserModal.value = true } async function editUser(user) { try { const response = await getUserDetails(user.id) editingUser.value = response.data showUserModal.value = true } catch (error) { console.error('Failed to get user details:', error) } } function closeUserModal() { showUserModal.value = false editingUser.value = {} } async function saveUser() { try { await userForm.value.validate() if (editingUser.value.id) { await updateUser(editingUser.value) } else { await createUser(editingUser.value) } closeUserModal() await fetchUsers(selectedDepartmentKeys.value[0]) } catch (error) { console.error('Failed to save user:', error) } } async function toggleUserStatus(user, value) { try { await updateUser({ id: user.id, disabled: !value }) await fetchUsers(selectedDepartmentKeys.value[0]) } catch (error) { console.error('Failed to toggle user status:', error) } } async function deleteUser(user) { // Implement delete user logic } async function handleBatchDelete() { // Implement batch delete logic } function renderSwitcherIcon({ expanded, isLeaf }) { if (isLeaf) { return null } return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined) } // 修改 overrideNodeClickBehavior 函数 const overrideNodeClickBehavior = ({ option }) => { console.log('overrideNodeClickBehavior', option) // 阻止事件冒泡，以防止触发两次 event.stopPropagation() console.log('overrideNodeClickBehavior', event) // 更新选中状态 selectedDepartmentKeys.value = [option.key] // 加载用户列表 fetchUsers(option.key) // 处理展开/收起 if (option.children && option.children.length > 0) { if (expandedKeys.value.includes(option.key)) { expandedKeys.value = expandedKeys.value.filter(key => key !== option.key) } else { expandedKeys.value = [...expandedKeys.value, option.key] } } // 返回 'prevent-default' 来阻止默认行为 return 'prevent-default' } function handleTreeExpand(keys) { expandedKeys.value = keys } // 添加一个辅助函数来查找点 function findNodeByKey(tree, key) { for (const node of tree) { if (node.key === key) { return node } if (node.children) { const found = findNodeByKey(node.children, key) if (found) return found } } return null } // 新增、编辑和删除部门的函数 async function addDepartment(parentKey) { editingDepartment.value = { parentId: parentKey, name: '' } showDepartmentModal.value = true } async function renameDepartment(key) { const department = findNodeByKey(departmentTree.value, key) if (department) { editingDepartment.value = { ...department } showDepartmentModal.value = true } } async function deleteDepartment(key) { // 这里应该调用后端 API 来删除部门 // 删除成功后重新获取部门树 await fetchDepartments() } async function saveDepartment() { try { await departmentForm.value.validate() // 这里应该调用后端 API 来保存或更新部门 // 保存成功后重新获取部门树 await fetchDepartments() showDepartmentModal.value = false } catch (error) { console.error('Failed to save department:', error) } } // 处理拖动排序 function handleDrop({ node, dragNode, dropPosition }) { // 这里应该调用后端 API 来更新部门顺序 // 更新成功后重新获取部门树 fetchDepartments() } // 修改 renderSuffix 函数 function renderSuffix({ option }) { return () => h( 'div', { class: 'tree-node-action' }, h( NDropdown, { trigger: 'click', options: [ { label: '新增子部门', key: 'add', icon: renderIcon(AddCircleOutline) }, { label: '重命名', key: 'rename', icon: renderIcon(PencilOutline) }, { label: '删除', key: 'delete', icon: renderIcon(TrashOutline) } ], onSelect: (key) => { switch (key) { case 'add': addDepartment(option.key) break case 'rename': renameDepartment(option.key) break case 'delete': deleteDepartment(option.key) break } } }, { default: () => h(NIcon, { component: MoreVertical16Filled }) } ) ) } function renderIcon(icon) { return () => h(NIcon, null, { default: () => h(icon) }) } </script> <template> <n-space vertical :size="24"> <n-card title=""> <n-grid :cols="12" :x-gap="24"> <!-- 左侧组织机构树 --> <n-grid-item span="4"> <n-card title="组织机构"> <n-tree block-line :data="departmentTree" :expanded-keys="expandedKeys" :selected-keys="selectedDepartmentKeys" :render-suffix="renderSuffix" :render-switcher-icon="renderSwitcherIcon" :override-default-node-click-behavior="overrideNodeClickBehavior" selectable draggable @update:expanded-keys="handleTreeExpand" @drop="handleDrop" /> </n-card> </n-grid-item> <!-- 右侧用户列表 --> <n-grid-item span="8"> <n-card title="用户列表"> <template #header-extra> <n-space> <n-button @click="handleAddUser">新增用户</n-button> <n-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">批量删除</n-button> </n-space> </template> <n-data-table :columns="columns" :data="users" :pagination="pagination" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" :row-key="row => row.id" @update:checked-row-keys="handleCheck" /> </n-card> </n-grid-item> </n-grid> </n-card> <!-- 用户编辑对话框 --> <n-modal v-model:show="showUserModal" :mask-closable="false"> <n-card style="width: 600px" :title="editingUser.id ? '编辑用户' : '新增用户'" :bordered="false" size="huge" role="dialog" aria-modal="true" > <n-form :model="editingUser" :rules="userRules" ref="userForm" label-placement="left" label-width="auto" require-mark-placement="right-hanging" :style="{ maxWidth: '640px' }" > <n-form-item label="用户名" path="username"> <n-input v-model:value="editingUser.username" placeholder="请输入用户名" /> </n-form-item> <n-form-item label="昵称" path="nickname"> <n-input v-model:value="editingUser.nickname" placeholder="请输入昵称" /> </n-form-item> <n-form-item label="密码" path="passwd"> <n-input v-model:value="editingUser.passwd" type="password" placeholder="请输入密码" /> </n-form-item> <n-form-item label="手机号" path="workMobile"> <n-input v-model:value="editingUser.workMobile" placeholder="请输入手机号" /> </n-form-item> <n-form-item label="所属部门" path="departmentId"> <n-tree-select v-model:value="editingUser.departmentId" :options="departmentTree" placeholder="请选择所属部门" /> </n-form-item> <n-form-item label="角色" path="roles"> <n-select v-model:value="editingUser.roles" multiple :options="roleOptions" placeholder="请选择角色" /> </n-form-item> <n-form-item label="状态" path="disabled"> <n-switch v-model:value="editingUser.disabled" /> </n-form-item> </n-form> <template #footer> <n-space justify="end"> <n-button @click="closeUserModal">取消</n-button> <n-button type="primary" @click="saveUser">保存</n-button> </n-space> </template> </n-card> </n-modal> <!-- 部门编辑对话框 --> <n-modal v-model:show="showDepartmentModal" :mask-closable="false"> <n-card style="width: 400px" :title="editingDepartment.key ? '编辑部门' : '新增部门'" :bordered="false" size="huge" role="dialog" aria-modal="true" > <n-form :model="editingDepartment" :rules="departmentRules" ref="departmentForm" label-placement="left" label-width="auto" require-mark-placement="right-hanging" > <n-form-item label="部门名称" path="name"> <n-input v-model:value="editingDepartment.name" placeholder="请输入部门名称" /> </n-form-item> </n-form> <template #footer> <n-space justify="end"> <n-button @click="showDepartmentModal = false">取消</n-button> <n-button type="primary" @click="saveDepartment">保存</n-button> </n-space> </template> </n-card> </n-modal> </n-space> </template> <style scoped> .n-tree { --n-item-height: 40px; } /* 添加以下样式以调整操作列的布局 */ .n-data-table .n-button.n-button--quaternary { padding: 0; margin: 0 4px; } /* 可以添加以下样式来调整展开/收起图标的大小 */ .n-tree .n-tree-node-switcher { transform: none !important; } /* 调整图标大小和对齐方式 */ .n-tree .n-tree-node-switcher svg { font-size: 18px; width: 1em; height: 1em; } /* 确保叶子节点没有左边 */ .n-tree .n-tree-node-content__prefix { width: auto; } /* 隐藏叶子节点的切换器 */ .n-tree .n-tree-node--leaf .n-tree-node-switcher { visibility: hidden; width: 0; } /* 修改节点操作按钮的样式 */ .n-tree .n-tree-node-content { position: relative; } .tree-node-action { position: absolute; right: 8px; top: 50%; transform: translateY(-50%); opacity: 0; transition: opacity 0.3s; cursor: pointer; } .n-tree-node:hover .tree-node-action, .n-tree-node--selected .tree-node-action { opacity: 1; } .tree-node-action .n-icon { font-size: 16px; } /* 确保下拉菜单在树节点之上 */ .n-dropdown-menu { z-index: 1000; } </style><template> <div class="center-div"></div> </template> <script setup> import * as ww from '@wecom/jssdk' import { onMounted } from 'vue' import { doPost } from '@/utils/requests' // 导入封装的 doPost 方法 // 初始化登录组件 onMounted(() => { const wwLogin = ww.createWWLoginPanel({ el: '.center-div', params: { login_type: 'CorpApp', appid: 'wwb5a4214455e4bf68', agentid: '1000002', redirect_uri: 'https://jwd.vooice.tech/wecom', state: 'loginState', redirect_type: 'callback', }, onCheckWeComLogin({ isWeComLogin }) { console.log(isWeComLogin) }, onLoginSuccess({ code }) { console.log({ code }); // 使用封装的 doPost 方法发送请求 doPost(`/system/wecom/login?code=${code}`) .then(response => { console.log('Login successful:', response); window.location.href = '/wecom-success' }) .catch(error => { console.error('Login failed:', error); }); }, onLoginFail(err) { console.log(err) }, }) }) </script> <style scoped> /* 确保整个页面不显示滚动条 */ html, body { height: 100%; overflow: hidden; } /* 添加样式以实现垂直和水平居中 */ .center-div { display: flex; justify-content: center; align-items: center; height: 100vh; /* 使用视口高度确保居中对齐 */ } </style><template> <div> <!-- 显示完整的 URL --> <p>完整的 URL: {{ fullUrl }}</p> <!-- 显示 URL 参数 --> <p>URL 参数:</p> <ul> <li v-for="(value, key) in queryParams" :key="key"> {{ key }}: {{ value }} </li> </ul> </div> </template> <script> export default { data() { return { fullUrl: '', queryParams: {} }; }, created() { this.fullUrl = window.location.href; // 获取完整的 URL this.queryParams = this.$route.query; // 获取 URL 参数 } } </script><template> <div class="task-result-page"> <div v-if="loading">加载中...</div> <div v-else-if="error">{{ error }}</div> <div v-else class="result-container"> <h2>任务ID: {{ taskResult.taskId }}</h2> <!-- 任务状态显示 --> <div class="status" :class="taskResult.taskStatus.toLowerCase()"> 状态: {{ getStatusText(taskResult.taskStatus) }} </div> <!-- 当任务完成时才显示结果 --> <template v-if="taskResult.taskStatus === 'DONE'"> <!-- 图像结果展示区 --> <div class="image-results"> <div class="image-section"> <h3>角度检测结果</h3> <img :src="taskResult.taskResult.angeleImage" alt="角度检测图" /> </div> <div class="image-section"> <h3>抓取点检测结果</h3> <img :src="taskResult.taskResult.graspImage" alt="抓取检测图" /> </div> <div class="image-section"> <h3>掩码检测结果</h3> <img :src="taskResult.taskResult.maskImage[0]" alt="掩码检测图" /> </div> </div> <!-- 物体识别结果列表 --> <div class="detection-results"> <h3>识别结果列表</h3> <div class="objects-grid"> <div v-for="(item, index) in taskResult.taskResult.answers" :key="index" class="object-card"> <img :src="taskResult.taskResult.croppedImagesListBbox[index]" alt="物体图" /> <div class="object-info"> <p>标签: {{ taskResult.taskResult.labels[index] }}</p> <p>置信度: {{ (taskResult.taskResult.scores[index] * 100).toFixed(2) }}%</p> <div class="attributes"> <template v-for="(value, key) in item" :key="key"> <p>{{ key }}: {{ value }}</p> </template> </div> </div> </div> </div> </div> </template> <!-- 当任务正在运行时显示 --> <div v-else-if="taskResult.taskStatus === 'RUNNING'" class="running-status"> <div class="loading-spinner"></div> <p>任务正在处理中，请稍候...</p> <button @click="fetchTaskResult" class="refresh-btn">刷新状态</button> </div> </div> </div> </template> <script> import { doGet } from '@/utils/requests' export default { name: 'TaskResultPage', data() { return { taskResult: null, loading: true, error: null, pollingInterval: null } }, created() { this.fetchTaskResult() }, beforeUnmount() { if (this.pollingInterval) { clearInterval(this.pollingInterval) } }, methods: { getStatusText(status) { const statusMap = { 'RUNNING': '处理中', 'DONE': '已完成', 'FAILED': '失败' } return statusMap[status] || status }, async fetchTaskResult() { try { const taskId = this.$route.query.task_id if (!taskId) { throw new Error('未提供任务ID') } const { data } = await doGet('/open-apis/app/perception/result', { task_id: taskId }) this.taskResult = data // 如果任务正在运行，设置轮询 if (this.taskResult.taskStatus === 'RUNNING') { if (!this.pollingInterval) { this.pollingInterval = setInterval(() => { this.fetchTaskResult() }, 5000) // 每5秒轮询一次 } } else { // 如果任务已完成或失败，清除轮询 if (this.pollingInterval) { clearInterval(this.pollingInterval) this.pollingInterval = null } } } catch (err) { this.error = err.message } finally { this.loading = false } } } } </script> <style scoped> .task-result-page { padding: 20px; } .result-container { max-width: 1200px; margin: 0 auto; } .image-results { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; } .image-section { text-align: center; } .image-section img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); } .objects-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; } .object-card { border: 1px solid #eee; border-radius: 8px; padding: 10px; background: #fff; } .object-card img { width: 100%; height: 200px; object-fit: cover; border-radius: 4px; } .object-info { margin-top: 10px; } .attributes { margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee; } .attributes p { margin: 5px 0; font-size: 14px; } /* 新增状态相关样式 */ .status { padding: 8px 16px; border-radius: 4px; margin: 10px 0; display: inline-block; font-weight: bold; } .status.running { background-color: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; } .status.done { background-color: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; } .running-status { text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; margin: 20px 0; } .loading-spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px; } .refresh-btn { background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 20px; } .refresh-btn:hover { background: #40a9ff; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } </style><template> <div class="tasks-config-page"> <div class="filters"> <!-- 添加流程名称筛选输入框 --> <n-input v-model:value="searchQuery" placeholder="输入流程名称进行筛选" class="search-input"> <template #prefix> <n-icon><Search /></n-icon> </template> </n-input> <!-- 添加状态筛选选择器 --> <n-select v-model:value="statusFilter" :options="statusOptions" class="status-select" /> </div> <div v-for="(group, groupName, index) in filteredGroupedWorkflows" :key="groupName"> <!-- 在每个组之前添加分割线，但不在第一个组之前添加 --> <n-divider v-if="index > 0" /> <div class="workflow-group"> <div class="group-header"> <n-h2>{{ groupName }}</n-h2> <n-button type="primary" circle @click="openNewWorkflowDialog(groupName)"> <template #icon> <n-icon><Add /></n-icon> </template> </n-button> </div> <n-grid :x-gap="16" :y-gap="16" :cols="4"> <n-gi v-for="workflow in group" :key="workflow.id"> <n-card class="workflow-card"> <n-h3>{{ workflow.name }}</n-h3> <n-p>{{ workflow.description }}</n-p> <div class="card-footer"> <n-switch v-model:value="workflow.enabled" @update:value="toggleWorkflow(workflow)" /> <div> <n-button text @click="editWorkflow(workflow)"> <template #icon> <n-icon><Create /></n-icon> </template> </n-button> <n-button text @click="deleteWorkflow(workflow)"> <template #icon> <n-icon><TrashBin /></n-icon> </template> </n-button> </div> </div> </n-card> </n-gi> </n-grid> </div> </div> <n-button @click="openNewWorkflowDialog">新增流程</n-button> </div> </template> <script setup> import { h, ref, computed, onMounted } from 'vue' import { useMessage, useDialog, useModal } from 'naive-ui' import WorkflowEditor from './WorkflowEditor.vue' import { Add, Create, TrashBin, Search } from '@vicons/ionicons5' import { useWorkflowStore } from '@/stores/workflowStore' import { NH1, NH2, NH3, NP, NButton, NSwitch, NCard, NGrid, NGi, NSpace, NIcon, NDivider, NInput, NSelect } from 'naive-ui' const message = useMessage() const dialog = useDialog() const modal = useModal() const workflowStore = useWorkflowStore() const currentWorkflow = ref(null) const currentGroup = ref('') const searchQuery = ref('') const statusFilter = ref('all') const statusOptions = [ { label: '全部状态', value: 'all' }, { label: '用状态', value: 'enabled' }, { label: '停用状态', value: 'disabled' } ] // 直接使用 mainStore 中的 workflows const workflows = computed(() => workflowStore.workflows) // 在组件挂载时获取 workflows 数据 onMounted(() => { workflowStore.fetchWorkflows() }) // 筛选后的工作流 const filteredWorkflows = computed(() => { return workflows.value.filter(workflow => { const nameMatch = !searchQuery.value || workflow.name.toLowerCase().includes(searchQuery.value.toLowerCase()) const statusMatch = statusFilter.value === 'all' || (statusFilter.value === 'enabled' && workflow.enabled) || (statusFilter.value === 'disabled' && !workflow.enabled) return nameMatch && statusMatch }) }) // 将筛选后的工作流按组分类 const filteredGroupedWorkflows = computed(() => { const groups = {} filteredWorkflows.value.forEach(workflow => { if (!groups[workflow.group]) { groups[workflow.group] = [] } groups[workflow.group].push(workflow) }) return groups }) const openNewWorkflowDialog = (group) => { currentWorkflow.value = null currentGroup.value = group const m = modal.create({ title: '新增流程', content: () => h(WorkflowEditor, { ref: (el) => { workflowEditorRef.value = el }, workflow: currentWorkflow.value, }), maskClosable: false, preset: 'card', closeOnEsc: false, style: { width: '100vw', height: '100vh', maxWidth: '100vw' }, onEsc: () => handleCloseModal(m), onClose: () => handleCloseModal(m) }) } const editWorkflow = (workflow) => { currentWorkflow.value = { ...workflow } const m = modal.create({ title: '编辑流程', content: () => h(WorkflowEditor, { ref: (el) => { workflowEditorRef.value = el }, workflow: currentWorkflow.value, onSave: saveWorkflow, onClose: () => handleCloseModal(m) }), maskClosable: false, preset: 'card', style: { width: '100vw', height: '100vh', maxWidth: '100vw' }, onEsc: () => handleCloseModal(m), onClose: () => handleCloseModal(m) }) } const deleteWorkflow = (workflow) => { dialog.warning({ title: '警告', content: '确定要删除这个流程吗？', positiveText: '确定', negativeText: '取消', onPositiveClick: () => { workflowStore.deleteWorkflow(workflow.id) message.success('删除成功') }, onNegativeClick: () => { message.info('已取消删除') } }) } const toggleWorkflow = (workflow) => { workflowStore.updateWorkflow({ ...workflow, enabled: !workflow.enabled }) message.success(`流程已${workflow.enabled ? '启用' : '停用'}`) } const closeDialog = () => { dialogVisible.value = false currentWorkflow.value = null currentGroup.value = '' } const saveWorkflow = async (workflow) => { if (currentWorkflow.value) { await workflowStore.updateWorkflow(workflow) } else { await workflowStore.addWorkflow({ ...workflow, group: currentGroup.value }) } message.success('保存成功') modal.destroyAll() } const workflowEditorRef = ref(null) const handleEditorClose = async () => { if (workflowEditorRef.value) { const canClose = await workflowEditorRef.value.close() if (canClose) { dialogVisible.value = false } } } const handleAfterLeave = () => { currentWorkflow.value = null currentGroup.value = '' } const handleMaskClick = () => { handleEditorClose() } const handleCloseModal = async (modalInstance) => { if (workflowEditorRef.value) { const canClose = await workflowEditorRef.value.close() if (canClose) { modalInstance.destroy() } } else { modalInstance.destroy() } } </script> <style scoped> .tasks-config-page { padding: 20px; } .filters { display: flex; gap: 20px; margin-bottom: 20px; } .search-input { max-width: 300px; } .status-select { width: 120px; } .workflow-group { margin-bottom: 30px; } .group-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; } .workflow-card { height: 100%; } .card-footer { display: flex; justify-content: space-between; align-items: center; margin-top: 10px; } :deep(.n-modal-body-wrapper) { display: flex; flex-direction: column; height: calc(100vh - 108px); /* 减去标题和页脚的高度 */ } :deep(.n-modal-body) { flex: 1; overflow: hidden; padding: 0; } </style><template> <div> <h1>我的待办</h1> </div> </template> <script setup> import { useMainStore } from '@/stores/mainStore' const mainStore = useMainStore() </script> <style scoped></style><template> <div class="workflow-editor"> <!-- 顶部 Tab 导航 --> <div class="tab-nav-container"> <ul class="tab-nav"> <li v-for="tab in tabs" :key="tab.value" :class="{ active: currentTab === tab.value }" @click="currentTab = tab.value" > {{ tab.label }} </li> </ul> <div class="action-buttons"> <n-button v-if="currentTab === 'form'" @click="previewForm">预览</n-button> <n-button v-if="['form', 'process'].includes(currentTab)" type="primary" @click="saveForm">保存</n-button> </div> </div> <!-- Tab 内容 --> <div class="tab-content"> <!-- 表单设计 --> <div v-if="currentTab === 'form'" class="form-designer tab-pane"> <div class="component-list"> <h3>基础组件</h3> <div class="component-group"> <n-button v-for="component in basicComponents" :key="component.type" draggable="true" @dragstart="dragStart($event, component)" @click="addComponent(component)" class="component-button"> <template #icon> <n-icon><component :is="component.icon" /></n-icon> </template> {{ component.label }} </n-button> </div> <h3>高级组件</h3> <div class="component-group"> <n-button v-for="component in advancedComponents" :key="component.type" draggable="true" @dragstart="dragStart($event, component)" @click="addComponent(component)" class="component-button"> <template #icon> <n-icon><component :is="component.icon" /></n-icon> </template> {{ component.label }} </n-button> </div> </div> <div class="form-canvas" @dragover.prevent @drop="drop($event)"> <!-- 表单画布 --> <h3>表单画布</h3> <div v-for="(field, index) in formFields" :key="index" :class="['form-field', { 'selected': field.selected }]" :style="{ width: field.props.width }" @click="selectField(field)"> <div class="field-content"> <div class="field-label"> <span v-if="field.props.required" class="required-mark">*</span> {{ field.label }} <!-- 这里使用 field.label，它会随着属性设置中的更改而更新 --> <span v-if="field.description" class="field-description">{{ field.description }}</span> </div> <div class="field-preview"> <component :is="getComponentByType(field.type)" v-bind="field.props" disabled /> </div> </div> <div class="field-actions"> <n-button quaternary circle size="small" @click.stop="copyField(field)"> <template #icon> <n-icon><Copy /></n-icon> </template> </n-button> <n-button quaternary circle size="small" @click.stop="deleteField(index)"> <template #icon> <n-icon><Delete /></n-icon> </template> </n-button> </div> </div> </div> <div class="property-panel"> <!-- 控件属性 --> <h3>字段属性</h3> <n-form v-if="selectedField" label-placement="top"> <!-- 通用属性 --> <n-form-item label="字段标题"> <n-input v-model:value="selectedField.label" /> </n-form-item> <n-form-item label="描述信息"> <n-input v-model:value="selectedField.description" /> </n-form-item> <n-form-item label="提示文字"> <n-input v-model:value="selectedField.props.placeholder" /> </n-form-item> <n-form-item label="校验"> <n-space vertical align="start"> <n-checkbox v-model:checked="selectedField.props.required">必需</n-checkbox> <n-checkbox v-model:checked="selectedField.props.unique">唯一</n-checkbox> </n-space> </n-form-item> <n-form-item label="字段权限"> <n-select v-model:value="selectedField.props.permission" :options="permissionOptions" /> </n-form-item> <n-form-item label="字段宽度"> <n-select v-model:value="selectedField.props.width" :options="widthOptions" /> </n-form-item> <!-- 特定属性 --> <template v-if="selectedField.type === 'input'"> <n-form-item label="格式"> <n-select v-model:value="selectedField.props.format" :options="inputFormatOptions" /> </n-form-item> <n-form-item label="最大文本长度"> <n-input-number v-model:value="selectedField.props.maxLength" /> </n-form-item> </template> <template v-if="selectedField.type === 'textarea'"> <n-form-item label="最大文本长度"> <n-input-number v-model:value="selectedField.props.maxLength" /> </n-form-item> </template> <template v-if="selectedField.type === 'number'"> <n-form-item label="格式"> <n-select v-model:value="selectedField.props.format" :options="numberFormatOptions" /> </n-form-item> <n-form-item label="保留小数位数"> <n-input-number v-model:value="selectedField.props.precision" /> </n-form-item> <n-form-item label="千分位"> <n-checkbox v-model:checked="selectedField.props.thousandSeparator" /> </n-form-item> </template> <template v-if="selectedField.type === 'datetime'"> <n-form-item label="类型"> <n-select v-model:value="selectedField.props.type" :options="datetimeTypeOptions" /> </n-form-item> </template> <template v-if="selectedField.type === 'radio'"> <n-form-item label="选项"> <n-dynamic-input v-model:value="selectedField.props.options" :on-create="onCreateOption"> <template #default="{ value }"> <n-input v-model:value="value.label" placeholder="选项文本" /> </template> </n-dynamic-input> </n-form-item> <n-form-item label="选项排列方式"> <n-radio-group v-model:value="selectedField.props.layout"> <n-radio-button value="vertical">纵向</n-radio-button> <n-radio-button value="horizontal">横向</n-radio-button> </n-radio-group> </n-form-item> </template> </n-form> </div> </div> <!-- 其他 Tab 内容 --> <div v-else-if="currentTab === 'process'" class="tab-pane">流程设计内容</div> <div v-else-if="currentTab === 'extension'" class="tab-pane">扩展功能内容</div> <div v-else-if="currentTab === 'publish'" class="tab-pane">流程发布内容</div> <div v-else-if="currentTab === 'data'" class="tab-pane">数据管理内容</div> </div> </div> </template> <script setup> import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue' import { NInput, NButton, useDialog, NIcon, NDatePicker, NRadioGroup, NCheckboxGroup, NSelect, NSwitch, NDivider, NTabs, NUpload, NInputNumber, NCheckbox, NRadioButton, NDynamicInput } from 'naive-ui' import { useMessage } from 'naive-ui' // Tabler Icons import { Calendar, Select, Selector, User, Users,Trash as Delete } from '@vicons/tabler' // Material Icons import { AbcOutlined, AccountTreeOutlined, AccountTreeSharp, BackupTableFilled, AttachFileRound, GpsNotFixedOutlined, DrawFilled, SmartButtonFilled , ContentCopyFilled as Copy } from '@vicons/material' // Carbon Icons import { PageNumber, RadioButtonChecked, CheckboxChecked, Split, Image, RegionAnalysisVolume, Table, Magnify, Data1, Barcode, ApplicationMobile, CdCreateExchange } from '@vicons/carbon' const props = defineProps({ workflow: { type: Object, default: () => ({}) } }) const emit = defineEmits(['save', 'cancel', 'close']) const dialog = useDialog() const message = useMessage() const tabs = [ { label: '表单设计', value: 'form' }, { label: '流程设计', value: 'process' }, { label: '扩展功能', value: 'extension' }, { label: '流程发布', value: 'publish' }, { label: '数据管理', value: 'data' } ] const currentTab = ref('form') const formFields = ref([]) const selectedField = ref(null) const isModified = ref(false) const isClosing = ref(false) const keyupListener = ref(null) const basicComponents = [ { type: 'input', label: '单行文本', icon: AbcOutlined }, { type: 'textarea', label: '多行文本', icon: AbcOutlined }, { type: 'number', label: '数字', icon: PageNumber }, { type: 'datetime', label: '日期时间', icon: Calendar }, { type: 'radio', label: '单选按钮组', icon: RadioButtonChecked }, { type: 'checkbox', label: '复选框组', icon: CheckboxChecked }, { type: 'select', label: '下拉框', icon: Select }, { type: 'multiSelect', label: '下拉复选框', icon: Selector }, { type: 'memberSelect', label: '成员单选', icon: User }, { type: 'memberMultiSelect', label: '成员多选', icon: Users }, { type: 'departmentSelect', label: '部门单选', icon: AccountTreeOutlined }, { type: 'departmentMultiSelect', label: '部门多选', icon: AccountTreeSharp }, { type: 'divider', label: '分割线', icon: Split }, { type: 'tabs', label: '多标签页', icon: BackupTableFilled }, ] const advancedComponents = [ { type: 'image', label: '图片', icon: Image }, { type: 'attachment', label: '附件', icon: AttachFileRound }, { type: 'address', label: '地址', icon: RegionAnalysisVolume }, { type: 'location', label: '定位', icon: GpsNotFixedOutlined }, { type: 'subform', label: '子表单', icon: Table }, { type: 'query', label: '查询', icon: Magnify }, { type: 'dataSelect', label: '选择数据', icon: Data1 }, { type: 'signature', label: '手写签名', icon: DrawFilled }, { type: 'serialNumber', label: '流水号', icon: Barcode }, { type: 'phone', label: '手机号', icon: ApplicationMobile }, { type: 'ocr', label: '文字识别', icon: CdCreateExchange }, { type: 'button', label: '按钮', icon: SmartButtonFilled }, ] const permissionOptions = [ { label: '可见', value: 'visible' }, { label: '可编辑', value: 'editable' } ] const widthOptions = [ { label: '1/4', value: '25%' }, { label: '1/3', value: '33.33%' }, { label: '1/2', value: '50%' }, { label: '2/3', value: '66.67%' }, { label: '3/4', value: '75%' }, { label: '整行', value: '100%' } ] const inputFormatOptions = [ { label: '无', value: 'none' }, { label: '手机号码', value: 'mobile' }, { label: '电话号码', value: 'phone' }, { label: '邮政编码', value: 'postcode' }, { label: '身份证号码', value: 'idcard' }, { label: '邮箱地址', value: 'email' } ] const numberFormatOptions = [ { label: '数值', value: 'number' }, { label: '百分比', value: 'percent' } ] const datetimeTypeOptions = [ { label: '月', value: 'month' }, { label: '年月日', value: 'date' }, { label: '年月日时分', value: 'datetime' }, { label: '年月日时分秒', value: 'datetimesecond' } ] const onCreateOption = () => { return { label: '', value: Date.now().toString() } } // 监听修改 watch(formFields, () => { isModified.value = true }, { deep: true }) // 拖拽开始 const dragStart = (event, component) => { event.dataTransfer.setData('text/plain', JSON.stringify(component)) } // 放置组件 const drop = (event) => { try { const componentData = JSON.parse(event.dataTransfer.getData('text/plain')) addComponent(componentData) // addComponent 方法现在会自动选中新添加的组件 } catch (error) { console.error('Error dropping component:', error) } } // 选择字段 const selectField = (field) => { try { selectedField.value = field // 重置所有字段的背景色 formFields.value.forEach(f => { if (f.id === field.id) { f.selected = true } else { f.selected = false } }) } catch (error) { console.error('Error selecting field:', error) } } // 预览表单 const previewForm = () => { // 实现预览逻辑 console.log('预览表单') } // 保存表单或流程 const saveForm = () => { try { // 实现保存逻辑 if (currentTab.value === 'form') { // 保存表单逻辑 console.log('保存表单', formFields.value) } else if (currentTab.value === 'process') { // 保存程逻辑 console.log('保存流程') } isModified.value = false emit('save', { tab: currentTab.value, data: formFields.value }) } catch (error) { console.error('Error saving form:', error) throw error // 重新抛出错，让调用者知道保存失败 } } // 显示保存提示 const showSavePrompt = () => { return new Promise((resolve) => { dialog.warning({ title: '警告', content: '您有未保存的更改，是否保存？我是弹窗内处理。', positiveText: '保存', negativeText: '放弃', closable: false, maskClosable: false, onPositiveClick: () => { saveForm() resolve(true) }, onNegativeClick: () => { isModified.value = false resolve(false) }, onClose: () => { resolve(null) } }) }) } // 修改 closeEditor 方法 const closeEditor = async () => { if (isClosing.value) return false isClosing.value = true try { if (isModified.value) { const result = await showSavePrompt() if (result === null) { // 用户取消了操作 return false } // 如果用户选择保存，result 为 true；如果选择不保存，result 为 false if (result) { try { await saveForm() message.success('保存成功') } catch (error) { message.error('保存失败') return false } } } removeKeyupListener() emit('close', true) // 发送一个参数表示可以直接关闭 return true } finally { isClosing.value = false } } // 修改 handleKeyUp 方法 const handleKeyUp = async (event) => { if (event.key === 'Escape' && !isClosing.value) { event.preventDefault() // 阻止默认行为 event.stopPropagation() // 阻止事件冒泡 await closeEditor() } } // 添加和移除事件监听器的方法 const addKeyupListener = () => { if (!keyupListener.value) { keyupListener.value = (event) => handleKeyUp(event) window.addEventListener('keyup', keyupListener.value) } } const removeKeyupListener = () => { if (keyupListener.value) { window.removeEventListener('keyup', keyupListener.value) keyupListener.value = null } } // 修改 onMounted 和 onBeforeUnmount onMounted(() => { addKeyupListener() window.addEventListener('beforeunload', handleBeforeUnload) }) onBeforeUnmount(() => { removeKeyupListener() window.removeEventListener('beforeunload', handleBeforeUnload) }) const handleBeforeUnload = (event) => { if (isModified.value) { event.preventDefault() event.returnValue = '' } } const getFormData = () => { // 返回当前表单的数据 return { // ... 表单数据 } } // 暴露方法和属性给父组件 defineExpose({ close: closeEditor, isModified, getFormData }) // 添加一个新的函数来根据类型返回对应的组件 const getComponentByType = (type) => { const componentMap = { input: NInput, textarea: NInput, number: NInput, datetime: NDatePicker, radio: NRadioGroup, checkbox: NCheckboxGroup, select: NSelect, multiSelect: NSelect, memberSelect: NSelect, memberMultiSelect: NSelect, departmentSelect: NSelect, departmentMultiSelect: NSelect, divider: NDivider, tabs: NTabs, image: NUpload, attachment: NUpload, // 其他组件类型映射... } return componentMap[type] || 'div' // 如果没有匹配的组件,默认返回 div } // 修改 getDefaultProps 方法 const getDefaultProps = (type) => { const commonProps = { placeholder: '', required: false, unique: false, permission: 'editable', width: '100%' } const specificProps = { input: { format: 'none', maxLength: 100 }, textarea: { maxLength: 500 }, number: { format: 'number', precision: 2, thousandSeparator: false }, datetime: { type: 'date' }, radio: { options: [{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }], layout: 'vertical' }, // ... 其他组件类型的默认属性 } return { ...commonProps, ...(specificProps[type] || {}) } } // 修改 copyField 方法 const copyField = (field) => { try { const newField = JSON.parse(JSON.stringify(field)) newField.id = Date.now() formFields.value.push(newField) isModified.value = true selectField(newField) // 选中新复制的字段 } catch (error) { console.error('Error copying field:', error) } } // 修改 deleteField 方法 const deleteField = (index) => { try { formFields.value.splice(index, 1) isModified.value = true if (selectedField.value && selectedField.value.id === formFields.value[index]?.id) { if (index > 0) { selectField(formFields.value[index - 1]) // 选中上一个字段 } else if (formFields.value.length > 0) { selectField(formFields.value[0]) // 如果删除的是第一个，选中新的第一个 } else { selectedField.value = null // 如果没有字段了，清空选择 } } } catch (error) { console.error('Error deleting field:', error) } } // 修改 addComponent 方法 const addComponent = (component) => { try { const newComponent = { ...component, id: Date.now(), label: component.label, props: getDefaultProps(component.type) } formFields.value.push(newComponent) isModified.value = true selectField(newComponent) // 选中新添加的组件 } catch (error) { console.error('Error adding component:', error) } } // 修改 watch 以响应 props 的变化 watch( () => selectedField.value?.props, () => { if (selectedField.value) { const index = formFields.value.findIndex(field => field.id === selectedField.value.id) if (index !== -1) { formFields.value[index] = { ...selectedField.value } } } }, { deep: true } ) </script> <style scoped> .workflow-editor { display: flex; flex-direction: column; height: 100%; width: 100%; overflow: hidden; } .tab-nav-container { display: flex; justify-content: space-between; align-items: center; background-color: #f0f0f0; padding: 0 20px; } .tab-nav { display: flex; list-style-type: none; padding: 0; margin: 0; } .tab-nav li { padding: 10px 20px; cursor: pointer; border-bottom: 2px solid transparent; } .tab-nav li.active { color: green; border-bottom-color: green; } .action-buttons { display: flex; gap: 10px; } .tab-content { flex: 1; position: relative; overflow: hidden; display: flex; flex-direction: column; } .tab-pane { flex: 1; background-color: #ffffff; padding: 20px; overflow-y: auto; } .form-designer { display: flex; flex: 1; overflow: hidden; } .component-list, .property-panel { width: 200px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; background-color: #f9f9f9; } .form-canvas { flex: 1; border: 1px solid #ccc; margin: 0 10px; padding: 10px; overflow-y: auto; background-color: #ffffff; } .component-list { width: 250px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; background-color: #f9f9f9; } .component-group { display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; } .component-button { width: calc(50% - 5px); text-align: left; padding: 8px; } .component-button .n-icon { margin-right: 8px; } .form-field { position: relative; padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; cursor: move; box-sizing: border-box; transition: background-color 0.3s ease; } .form-field.selected { background-color: #e6f7e6; } .field-actions { position: absolute; top: 5px; right: 5px; display: flex; gap: 5px; z-index: 1; } .field-content { padding-right: 60px; /* 为操作按钮留出空间 */ } .field-label { margin-bottom: 5px; font-weight: bold; } .field-preview { pointer-events: none; opacity: 0.7; } .field-preview :deep(*) { cursor: default !important; } .property-panel { width: 250px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; background-color: #f9f9f9; } /* 添加新的样式 */ .property-panel :deep(.n-form-item) { margin-bottom: 18px; } .property-panel :deep(.n-form-item-label) { padding-bottom: 8px; } .property-panel :deep(.n-form-item-blank) { display: flex; flex-direction: column; } .required-mark { color: red; margin-right: 4px; } .field-description { font-weight: normal; color: #999; margin-left: 8px; font-size: 0.9em; } .property-panel :deep(.n-space) { width: 100%; } .property-panel :deep(.n-checkbox) { margin-left: 0; } </style>