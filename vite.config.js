import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// 根据环境变量决定代理目标地址
const getProxyConfig = () => {
  const env = process.env.NODE_ENV || 'development';

  // 如果是开发环境，使用本地代理
  if (env === 'development') {
    return {
      '/open-api': {
        target: 'http://localhost:9999',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/open-api/, '')
      },
      '/api': {
        target: 'https://sit-open.qj-robots.com', // 默认使用sit环境
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/open-api/open-apis')
      }
    };
  }

  // 根据不同环境设置不同的目标地址
  let targetUrl = 'https://open.qj-robots.com'; // 默认production

  if (env === 'uat') {
    targetUrl = 'https://uat-open.qj-robots.com';
  } else if (env === 'sit') {
    targetUrl = 'https://sit-open.qj-robots.com';
  }

  return {
    '/api': {
      target: targetUrl,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/open-api/open-apis')
    }
  };
};

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    proxy: getProxyConfig()
  }
})
