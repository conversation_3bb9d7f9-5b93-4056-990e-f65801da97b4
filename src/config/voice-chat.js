// 语音聊天配置
export const voiceChatConfig = {
  // 通信模式配置
  communication: {
    mode: 'http', // 'websocket' 或 'http'

    // WebSocket配置（备用）
    websocket: {
      getServerUrl() {
        const isHttps = window.location.protocol === 'https:'
        const isDev = import.meta.env.DEV

        if (isDev) {
          return isHttps ? 'wss://120.48.126.4:5008' : 'http://120.48.126.4:5007'
        }

        return isHttps ? 'wss://120.48.126.4:5008' : 'http://120.48.126.4:5007'
      },

      getSocketOptions() {
        return {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          forceNew: true,
          autoConnect: false
        }
      }
    },

    // HTTP配置（主要使用）
    http: {
      pollingInterval: 1000,    // 轮询间隔（毫秒）
      maxPollingCount: 150,     // 最大轮询次数（5分钟）
      timeout: 30000            // 请求超时时间
    }
  },

  // 获取HTTP服务器地址（用于fallback）
  getHttpServerUrl() {
    const isHttps = window.location.protocol === 'https:'
    const isDev = import.meta.env.DEV

    if (isDev) {
      if (isHttps) {
        return 'https://120.48.126.4:5008'
      }
      return 'http://120.48.126.4:5007'
    }

    if (isHttps) {
      return 'https://120.48.126.4:5008'
    }
    return 'http://120.48.126.4:5007'
  },

  // 音频配置
  audio: {
    sampleRate: 16000,
    channelCount: 1,
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true
  },

  // 录音配置
  recording: {
    defaultThreshold: 50,
    defaultPatience: 20,
    chunkSize: 100, // MediaRecorder timeslice
    mimeType: 'audio/wav',
    maxDuration: 60, // 最大录音时长（秒）
    maxFileSize: 10 * 1024 * 1024, // 最大文件大小（10MB）
    autoSubmit: false // 不自动提交，需要用户手动停止
  },

  // 文件上传配置
  upload: {
    maxRetries: 3,        // 最大重试次数
    retryDelay: 1000,     // 重试延迟（毫秒）
    chunkSize: 1024 * 1024, // 分片大小（1MB）
    timeout: 30000        // 上传超时时间
  },

  // 音量分析配置
  analysis: {
    fftSize: 1024,
    smoothingTimeConstant: 0.3
  }
}

// 检查浏览器兼容性
export const checkBrowserSupport = () => {
  const support = {
    mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    audioContext: !!(window.AudioContext || window.webkitAudioContext),
    mediaRecorder: !!window.MediaRecorder,
    fetch: !!window.fetch,
    blob: !!window.Blob
  }

  const unsupported = Object.entries(support)
    .filter(([key, value]) => !value)
    .map(([key]) => key)

  return {
    isSupported: unsupported.length === 0,
    unsupported,
    support
  }
}

// 获取推荐的音频格式
export const getSupportedAudioFormats = () => {
  if (!window.MediaRecorder) return []

  const formats = [
    'audio/wav',
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4'
  ]

  return formats.filter(format => MediaRecorder.isTypeSupported(format))
}
