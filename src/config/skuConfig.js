/**
 * SKU配置文件
 */

export const skuList = [
    {
    id: 'free',
    name: '免费体验',
    price: "0",
    apiCalls: "1000次",
    webServices: "100次",
  },
  {
    id: 'preception',
    name: '感知模型',
    price: "5,999",
    apiCalls: "100万次",
    webServices: "4万次",
  },
  {
    id: 'decision',
    name: '决策模型',
    price: "7,999",
    apiCalls: "100万次",
    webServices: "4万次",
  },
  {
    id: 'both',
    name: '感知+决策',
    price: "12,999",
    apiCalls: "各200万次",
    webServices: "各4万次",
  }
];

// SKU表格基础列定义
export const baseSkuColumns = [
  {
    title: '服务',
    key: 'name',
    align: 'center',
    width: 120
  },
  {
    title: '价格',
    key: 'price',
    align: 'center',
    width: 120,
    render(row) {
      return `¥${row.price}`;
    }
  },
  {
    title: 'API调用次数',
    key: 'apiCalls',
    align: 'center',
    width: 140
  },
  {
    title: 'Web服务次数',
    key: 'webServices',
    align: 'center',
    width: 140
  }
];

// 表单验证规则 - 精确控制实时校验
export const accountFormRules = {
  organizationName: [
    {
      required: true,
      message: '请输入机构名称',
      trigger: 'blur'  // 失去焦点时验证必填
    },
    {
      max: 20,
      message: '机构名称不能超过20个字符',
      trigger: 'input'  // 输入时验证长度
    }
  ],
  username: [
    {
      required: true,
      message: '请输入登录名',
      trigger: 'blur'  // 失去焦点时验证必填
    },
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: 'blur'  // 失去焦点时验证邮箱格式
    }
  ],
  nickname: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur'  // 失去焦点时验证必填
    },
    {
      max: 20,
      message: '用户名不能超过20个字符',
      trigger: 'input'  // 输入时验证长度
    }
  ],
  mobile: [
    {
      required: true,
      message: '请输入手机号码',
      trigger: 'blur'  // 失去焦点时验证必填
    },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'  // 失去焦点时验证手机号格式
    }
  ],
  selectedSku: {
    required: true,
    message: '请选择一个服务套餐',
    trigger: 'change'  // 选择变化时验证
  }
};

// 默认表单数据
export const defaultAccountForm = {
  organizationName: '',
  username: '',
  nickname: '',
  mobile: '',
  selectedSku: null
};
