import os

def process_files(start_path='src'):
    # 存储处理后的内容
    processed_content = ''
    output_file = 'vue_content.txt'
    
    # 支持的文件类型
    supported_extensions = ('.vue', '.js', '.css')
    
    # 按目录层级排序的文件列表
    file_list = []
    
    # 确保起始路径存在
    if not os.path.exists(start_path):
        print(f"错误：目录 {start_path} 不存在")
        return
    
    # 遍历目录并收集文件
    for root, dirs, files in os.walk(start_path):
        level = root.replace(start_path, '').count(os.sep)
        for file in files:
            if file.endswith(supported_extensions):
                file_path = os.path.join(root, file)
                file_list.append((level, file_path))
    
    # 按目录层级排序
    file_list.sort(key=lambda x: (x[0], x[1]))
    
    # 处理文件
    for level, file_path in file_list:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 移除换行符和多余的空白字符
                content = ' '.join(content.split())
                processed_content += content
                print(f"已处理文件: {file_path}")
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        print(f"处理完成！输出文件: {output_file}")
        print(f"总字符数: {len(processed_content)}")
    except Exception as e:
        print(f"写入输出文件时出错: {str(e)}")

if __name__ == '__main__':
    process_files()
