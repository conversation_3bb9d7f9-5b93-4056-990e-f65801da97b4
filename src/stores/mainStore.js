import { defineStore } from 'pinia'
import { doGet, doPost } from '@/utils/requests'
import { h } from 'vue'
import { NIcon } from 'naive-ui'
import {
    SpeedometerOutline,
    PersonOutline,
    BuildOutline,
    MenuOutline,
    WalletOutline,
    ChatbubbleOutline,
    StarOutline,
    HeadsetOutline,
    SearchOutline,
    SyncOutline,
    BookOutline,
    HelpCircleOutline,
    BriefcaseOutline,
    FilterOutline
} from '@vicons/ionicons5'

const iconMap = {
    'Odometer': SpeedometerOutline,
    'UserFilled': PersonOutline,
    'Tools': BuildOutline,
    'Menu': MenuOutline,
    'Money': WalletOutline,
    'Message': ChatbubbleOutline,
    'StarFilled': StarOutline,
    'Service': HeadsetOutline,
    'Search': SearchOutline,
    'Refresh': SyncOutline,
    'Reading': BookOutline,
    'HelpFilled': HelpCircleOutline,
    'Suitcase': BriefcaseOutline,
    'Filter': FilterOutline
}

function renderIcon(icon) {
    return () => {
        const IconComponent = iconMap[icon] || HelpCircleOutline
        return h(NIcon, null, { default: () => h(IconComponent) })
    }
}

export const useMainStore = defineStore('main', {
    state: () => ({
        menus: [],
        user: null,
        isLoggedIn: false,
        menusLoaded: false,
        menusFetching: false,
    }),
    actions: {
        async fetchMenus() {
            this.menusFetching = true
            try {
                const response = await doGet('/auth-center/system/menus')
                if (response.code === 200 && Array.isArray(response.data)) {
                    this.menus = this.buildMenuTree(response.data)
                    this.menusLoaded = true
                } else {
                    throw new Error('Invalid menu data')
                }
            } catch (error) {
                // 如果获取失败，使用默认菜单
                this.menus = this.getDefaultMenus()
            } finally {
                this.menusFetching = false
            }
        },
        buildMenuTree(menuList) {
            const menuMap = new Map()
            const rootMenus = []

            // 第一遍遍历，创建所有菜单项
            menuList.forEach(menu => {
                menuMap.set(menu.id, {
                    label: menu.menuLabel,
                    key: menu.menuPath || String(menu.id),
                    icon: menu.menuIcon ? renderIcon(menu.menuIcon) : null,
                    viewPath: menu.viewPath, // 确保这里的 viewPath 是正确的
                    children: []
                })
            })

            // 第二遍遍历，构建树结构
            menuList.forEach(menu => {
                const menuItem = menuMap.get(menu.id)
                if (menu.parentId && menu.parentId !== 1) {
                    const parentMenu = menuMap.get(menu.parentId)
                    if (parentMenu) {
                        parentMenu.children.push(menuItem)
                    }
                } else {
                    rootMenus.push(menuItem)
                }
            })

            // 移除没有子菜单的 children 属性
            const cleanupEmptyChildren = (menus) => {
                return menus.map(menu => {
                    if (menu.children.length === 0) {
                        const { children, ...rest } = menu
                        return rest
                    }
                    return {
                        ...menu,
                        children: cleanupEmptyChildren(menu.children)
                    }
                })
            }

            return cleanupEmptyChildren(rootMenus)
        },
        setUser(user) {
            this.user = user
            this.isLoggedIn = !!user
            if (user) {
                localStorage.setItem('user', JSON.stringify(user))
            } else {
                localStorage.removeItem('user')
            }
        },
        async logout() {
            try {
                await doPost('/auth-center/system/logout')
            } catch (error) {
                console.error('登出失败:', error)
            } finally {
                this.user = null
                this.isLoggedIn = false
                this.menusLoaded = false
                this.menus = []
                localStorage.removeItem('access_token')
                // 不再需要清除 localStorage 中的菜单数据
            }
        },
        async checkLoginStatus() {
            const storedUser = localStorage.getItem('user')
            if (storedUser) {
                this.setUser(JSON.parse(storedUser))
                return true
            }

            try {
                const response = await doGet('/system/user')
                if (response.data) {
                    this.setUser(response.data)
                    return true
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
            }

            this.logout()
            return false
        },
        getDefaultMenus() {
            return [
                {
                    label: '我的待办',
                    key: '/tasks',
                    viewPath: 'tasks/TasksPage'  // 确保这里的文件名正确
                },
            ]
        }
    },
    getters: {
        getMenus: (state) => state.menus,
        getUser: (state) => state.user,
        isUserLoggedIn: (state) => state.isLoggedIn,
    }
})
