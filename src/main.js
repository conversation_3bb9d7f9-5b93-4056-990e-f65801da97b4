import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import naive from 'naive-ui'
import { zhCN, dateZhCN } from 'naive-ui'
import App from '@/App.vue'
import IndexPage from '@/views/IndexPage.vue'
import LoginPage from '@/views/system/LoginPage.vue'
import NotFoundPage from '@/views/system/NotFoundPage.vue'
import { useMainStore } from '@/stores/mainStore'
import TasksPage from '@/views/tasks/TasksPage.vue'
import TaskResultPage from '@/views/tasks/TaskResultPage.vue'
import RegisterPage from '@/views/system/RegisterPage.vue'
import JoinPage from '@/views/contacts/JoinPage.vue'
import InvitePage from '@/views/contacts/InvitePage.vue'
import AppDetail from '@/views/app/AppDetail.vue'
import Rooms from '@/views/app/Rooms.vue'
import AdminPage from '@/views/system/Admin.vue'
import IsaacPage from '@/views/isaac/IsaacPage.vue'
import DeepseekLive from '@/views/isaac/DeepSeekLive.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: IndexPage },
    { path: '/login', component: LoginPage },
    { path: '/register', component: RegisterPage },
    { path: '/logout', component: LoginPage },
    { path: '/tasks', component: TasksPage },
    { path: '/join', component: JoinPage },
    { path: '/invite', component: InvitePage },
    { path: '/task', component: TaskResultPage },
    {
      path: '/app/:id',
      name: 'AppDetail',
      component: AppDetail,
      props: true  // 启用 props 传递路由参数
    },
    {
      path: '/decision/rooms',
      name: 'decision-rooms',
      component: Rooms
    },
    {
      path: '/isaac/page',
      name: 'isaac-page',
      component: IsaacPage
    },
    {
      path: '/isaac/deepseek',
      name: 'deepseek-live',
      component: DeepseekLive
    },
    {
      path: '/voice-chat',
      name: 'voice-chat',
      component: () => import('@/views/chat/VoiceChat.vue')
    },
    {
      path: '/voice-chat/5008',
      name: 'voice-chat-5008',
      component: () => import('@/views/chat/VoiceChat5008.vue')
    },
    {
      path: '/video-chat',
      name: 'video-chat',
      component: () => import('@/views/chat/VideoChat.vue')
    },
    {
      path: '/camera-recognition',
      name: 'camera-recognition',
      component: () => import('@/views/chat/CameraRecognition.vue')
    },
    {
      path: '/decision/robot-config',  // 添加新路由
      name: 'robot-config',
      component: () => import('@/views/app/RobotConfig.vue')  // 需要创建这个组件
    },
    {
      path: '/decision/work',  // 添加工作页面路由
      name: 'work-page',
      component: () => import('@/views/app/WorkPage.vue')
    },
    {
      path: '/admin',  // 添加账号管理页面路由
      name: 'admin',
      component: AdminPage
    },
    { path: '/:pathMatch(.*)*', component: NotFoundPage }
  ]
})

// 定义白名单路由
const whiteList = ['/login', '/join', '/invite', '/register', '/task', '/voice-chat', '/voice-chat/5008', '/video-chat', '/camera-recognition']  // 添加聊天相关页面到白名单

router.beforeEach(async (to, from, next) => {
  console.log('路由跳转:', { to, from })
  const token = localStorage.getItem('access_token')

  // 在白名单中的路由直接放行
  if (whiteList.includes(to.path)) {
    next()
    return
  }

  if (token) {
    const mainStore = useMainStore()

    try {
      if (!mainStore.isUserLoggedIn) {
        await mainStore.checkLoginStatus()
      }

      if (to.path === '/' && !mainStore.menusLoaded) {
        await mainStore.fetchMenus()
      }

      next()
    } catch (error) {
      console.error('路由守卫错误:', error)
      localStorage.removeItem('token')
      next(`/login?redirect=${to.path}`)
    }
  } else {
    next(`/login?redirect=${to.path}`)
  }
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(naive)

app.mount('#app')
