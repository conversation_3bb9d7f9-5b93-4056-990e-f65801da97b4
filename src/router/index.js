import { createRouter, createWebHistory } from 'vue-router'
import IndexPage from '@/views/IndexPage.vue'
import LoginPage from '@/views/LoginPage.vue'
import AppDetail from '@/views/app/AppDetail.vue'
import IsaacController from '@/views/isaac/IsaacController.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: IndexPage
    },
    {
      path: '/login',
      component: LoginPage
    },
    {
      path: '/join',
      name: 'Join',
      component: () => import('@/views/contacts/InvitePage.vue')
    },
    {
      path: '/app/:id',
      name: 'AppDetail',
      component: AppDetail,
      props: true
    },
    {
      path: '/decision/rooms',
      name: 'decision-rooms',
      component: () => import('@/views/app/Rooms.vue')
    },
    {
      path: '/decision/robot-config',
      name: 'robot-config',
      component: () => import('@/views/app/RobotConfig.vue')
    },
    {
      path: '/decision/work',
      name: 'work-page',
      component: () => import('@/views/app/WorkPage.vue')
    },
    {
      path: '/isaac/isaac-controller',
      name: 'isaac-controller',
      component: IsaacController
    },
    {
      path: '/isaac/robot-controller',
      name: 'robot-controller',
      component: () => import('@/views/isaac/RobotController.vue')
    },
    {
      path: '/voice-chat',
      name: 'voice-chat',
      component: () => import('@/views/chat/VoiceChat.vue')
    },
    {
      path: '/voice-chat/5008',
      name: 'voice-chat-5008',
      component: () => import('@/views/chat/VoiceChat5008.vue')
    },
    {
      path: '/video-chat',
      name: 'video-chat',
      component: () => import('@/views/chat/VideoChat.vue')
    },
    {
      path: '/camera-recognition',
      name: 'camera-recognition',
      component: () => import('@/views/chat/CameraRecognition.vue')
    }
  ]
})

export default router
