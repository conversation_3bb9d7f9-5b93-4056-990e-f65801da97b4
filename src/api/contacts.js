import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

export const contactsApi = {
    // 获取组织机构树
    getOrganizations(isAdmin = false) {
        const url = isAdmin
            ? '/auth-center/system/department/admin/list'
            : '/auth-center/system/department/list'
        return doGet(url)
    },

    // 获取部门成员
    getMembers(departmentId, keywords = '', page = 1, size = 20, isAdmin = false) {
        const params = {
            department_id: departmentId,
            keywords,
            page,
            size
        }
        const url = isAdmin
            ? '/auth-center/system/user/admin/page'
            : '/auth-center/system/user/page'
        return doGet(url, params)
    },

    // 批量更新成员状态
    batchUpdateMemberStatus(userIds, status, isAdmin = false) {
        const url = isAdmin
            ? '/auth-center/system/user/status/admin'
            : '/auth-center/system/user/status'
        return doPut(url, {
            user_ids: userIds,
            status
        })
    },

    // 创建组织
    createOrganization(data, isAdmin = false) {
        const url = isAdmin
            ? '/auth-center/system/department/admin'
            : '/auth-center/system/department'
        return doPost(url, {
            name: data.name,
            parentId: data.parentId || 0,
            type: data.type
        })
    },

    // 添加新成员
    createMember(data) {
        return doPost('/auth-center/system/user', data)
    },

    // 删除用户
    deleteUser(userId, isAdmin = false) {
        const url = isAdmin
            ? `/auth-center/system/user/admin/${userId}`
            : `/auth-center/system/user/${userId}`
        return doDelete(url)
    },

    // 开通账户
    createAccount(data) {
        return doPost('/auth-center/system/user/admin', {
            deptName: data.organizationName,
            username: data.username,    // 登录名（邮箱）
            nickname: data.nickname,    // 用户名
            workMobile: data.mobile,
            skuId: data.selectedSku
        })
    },

    // 修改用户信息
    updateUser(userId, data, isAdmin = false) {
        const url = isAdmin
            ? `/auth-center/system/user/admin/${userId}`
            : `/auth-center/system/user/${userId}`

        const requestData = {
            nickname: data.nickname,
            workMobile: data.workMobile,
            username: data.username
        }

        // 管理员模式下添加SKU和剩余次数字段
        if (isAdmin && data.skuId !== undefined) {
            requestData.skuId = data.skuId
        }
        if (isAdmin && data.perceptionWebCount !== undefined) {
            requestData.perceptionWebCount = data.perceptionWebCount
        }
        if (isAdmin && data.perceptionApiCount !== undefined) {
            requestData.perceptionApiCount = data.perceptionApiCount
        }
        if (isAdmin && data.decisionWebCount !== undefined) {
            requestData.decisionWebCount = data.decisionWebCount
        }
        if (isAdmin && data.decisionApiCount !== undefined) {
            requestData.decisionApiCount = data.decisionApiCount
        }

        return doPut(url, requestData)
    },

    // 获取用户详情（管理员模式）
    getUserDetail(userId, isAdmin = false) {
        const url = isAdmin
            ? `/auth-center/system/user/admin/detail/${userId}`
            : `/auth-center/system/user/detail/${userId}`
        return doGet(url)
    }
}