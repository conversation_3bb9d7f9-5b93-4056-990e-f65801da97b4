import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// ISAAC API基础URL
const BASE_URL = '/app-center/isaac'

// ISAAC API接口
export const isaacApi = {
  // 连接ISAAC平台
  connect: () => doPost(`${BASE_URL}/connect`),

  // 断开连接
  disconnect: () => doPost(`${BASE_URL}/disconnect`),

  // 获取机器人状态
  getRobotStatus: () => doGet(`${BASE_URL}/robot/status`),

  // 更新机器人位置
  updateRobotPosition: (position) => doPut(`${BASE_URL}/robot/position`, position),

  // 同步房间配置
  syncRooms: (rooms) => doPost(`${BASE_URL}/rooms/sync`, { rooms }),

  // 同步物品配置
  syncItems: (items) => doPost(`${BASE_URL}/items/sync`, { items }),

  // 获取所有房间
  getRooms: () => doGet(`${BASE_URL}/rooms`),

  // 获取指定房间的物品
  getRoomItems: (roomId) => doGet(`${BASE_URL}/rooms/${roomId}/items`),

  // 获取机器人目标点
  getRobotTarget: () => doGet(`${BASE_URL}/robot/target`),

  // 设置机器人目标点
  setRobotTarget: (target) => doPost(`${BASE_URL}/robot/target`, target),

  // 获取环境状态
  getEnvironmentStatus: () => doGet(`${BASE_URL}/environment/status`),

  // 重置环境
  resetEnvironment: () => doPost(`${BASE_URL}/environment/reset`),

  // 保存场景数据
  saveScene: (sceneData) => doPost(`${BASE_URL}/scene/save/task`, sceneData),

  // 发送场景数据
  sendScene: (sceneData) => doPost(`${BASE_URL}/scene/send/task`, sceneData),

  // 获取上传参数
  getRgbUploadParams: () => doGet(`${BASE_URL}/upload/rgb/params`),
  getDepthUploadParams: () => doGet(`${BASE_URL}/upload/depth/params`),
  getSlamUploadParams: () => doGet(`${BASE_URL}/upload/slam/params`),

  getLatestKeys: () => doGet(`${BASE_URL}/latest-keys`),

  // 获取SLAM元数据
  getSlamMeta: () => doGet(`${BASE_URL}/slam/meta`),

  getDisplayText: () => doGet(`${BASE_URL}/display/text`),

  getDisplayTarget: () => doGet(`${BASE_URL}/display/target`),

  // 获取机器人操作状态
  setRobotOperation: (command) => doPost(`${BASE_URL}/robot/operation`, command),

  stopRobot: () => doPost(`${BASE_URL}/stop`)
}

