import { doPost, doGet } from '@/utils/requests'

/**
 * 语音聊天API
 */
export const voiceChatApi = {
  /**
   * 提交语音处理任务
   * @param {Object} params - 参数对象
   * @param {string} params.audioKey - 音频文件在COS中的key
   * @param {string} params.audioFormat - 音频格式 (webm, wav, mp3等)
   * @param {number} params.duration - 音频时长（秒）
   * @param {Object} params.metadata - 额外的元数据
   * @param {string} customUrl - 自定义URL（可选）
   * @returns {Promise} 返回任务信息
   */
  submitVoiceTask: (params, customUrl) => {
    const url = customUrl || '/open-apis/voice/chat/submit'
    const requestBody = {
      audioKey: params.audioKey,
      audioFormat: params.audioFormat,
      duration: params.duration,
      metadata: params.metadata || {},
      timestamp: Date.now()
    }

    // 如果有图片key，添加到请求体中
    if (params.imageKey) {
      requestBody.imageKey = params.imageKey
    }

    return doPost(url, requestBody)
  },

  /**
   * 获取语音处理任务结果
   * @param {string} taskId - 任务ID
   * @param {string} customUrl - 自定义URL（可选）
   * @returns {Promise} 返回任务结果
   */
  getTaskResult: (taskId, customUrl) => {
    const url = customUrl || `/open-apis/voice/chat/result?taskId=${taskId}`
    return doGet(url)
  },

  /**
   * 取消语音处理任务
   * @param {string} taskId - 任务ID
   * @param {string} customUrl - 自定义URL（可选）
   * @returns {Promise} 返回操作结果
   */
  cancelTask: (taskId, customUrl) => {
    const url = customUrl || '/open-apis/voice/chat/cancel'
    return doPost(url, { taskId })
  },

  /**
   * 获取语音聊天历史记录
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页大小
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @returns {Promise} 返回历史记录
   */
  getChatHistory: (params = {}) => {
    const queryParams = new URLSearchParams({
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...(params.startTime && { startTime: params.startTime }),
      ...(params.endTime && { endTime: params.endTime })
    })
    return doGet(`/open-apis/voice/chat/history?${queryParams}`)
  },

  /**
   * 删除聊天记录
   * @param {string} chatId - 聊天记录ID
   * @returns {Promise} 返回操作结果
   */
  deleteChatRecord: (chatId) => {
    return doPost('/open-apis/voice/chat/delete', { chatId })
  },

  /**
   * 获取语音聊天配置
   * @returns {Promise} 返回配置信息
   */
  getConfig: () => {
    return doGet('/open-apis/voice/chat/config')
  },

  /**
   * 更新语音聊天配置
   * @param {Object} config - 配置对象
   * @param {number} config.maxDuration - 最大录音时长（秒）
   * @param {number} config.maxFileSize - 最大文件大小（字节）
   * @param {Array} config.supportedFormats - 支持的音频格式
   * @returns {Promise} 返回操作结果
   */
  updateConfig: (config) => {
    return doPost('/open-apis/voice/chat/config', config)
  },

  /**
   * 健康检查
   * @param {string} customUrl - 自定义URL（可选）
   * @returns {Promise} 返回服务状态
   */
  healthCheck: (customUrl) => {
    const url = customUrl || '/open-apis/voice/chat/health'
    return doGet(url)
  }
}

/**
 * 任务状态枚举
 */
export const TaskStatus = {
  PENDING: 'PENDING',           // 等待处理
  PROCESSING: 'PROCESSING',     // 处理中
  COMPLETED: 'COMPLETED',       // 已完成
  FAILED: 'FAILED',            // 失败
  CANCELLED: 'CANCELLED',      // 已取消
  TIMEOUT: 'TIMEOUT'           // 超时
}

/**
 * 音频格式枚举
 */
export const AudioFormat = {
  WEBM: 'webm',
  WAV: 'wav',
  MP3: 'mp3',
  OGG: 'ogg',
  MP4: 'mp4'
}

/**
 * 响应类型枚举
 */
export const ResponseType = {
  TEXT: 'text',               // 文本回复
  AUDIO: 'audio',            // 音频回复
  MIXED: 'mixed'             // 混合回复（文本+音频）
}
