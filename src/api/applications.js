import { doGet, doPost, doPut } from '@/utils/requests'

export const applicationApi = {
  // 获取应用列表
  async getApplications() {
    const response = await doGet('/app-center/app/info/list')
    // 转换后端数据格式为前端使用的格式
    return response.data.map(app => ({
      id: app.appId,
      name: app.appName,
      iconName: app.iconName,
      iconColor: app.iconColor,
      enabled: app.appState !== 'DISABLED',
      owner: app.ownerName,
      myRole: app.roleName,
      latestUpdate: app.versionDesc
    }))
  },

  // 创建应用
  async createApplication(application) {
    console.log('发送创建应用请求:', application) // 添加日志便于调试
    const requestBody = {
      appName: application.name,
      appDesc: application.description || '',
      iconName: application.iconName,
      iconColor: application.iconColor
    }

    const response = await doPost('/app-center/app/info', requestBody)
    console.log('创建应用响应:', response)
  },

  // 更新应用
  async updateApplication(appId, data) {
    const requestBody = {
      appName: data.appName,
      appDesc: data.appDesc,
      iconName: data.iconName,
      iconColor: data.iconColor,
      docUrl: data.docUrl,
      appState: data.appState,
      ownerId: data.ownerId,
      ownerName: data.ownerName
    }

    await doPut(`/app-center/app/info/${appId}`, requestBody)
    // 不需要处理返回值，直接返回
  },

  // 删除应用
  async deleteApplication(id) {
    // TODO: 实现真实的删除接口
    return { success: true }
  },

  // 获取应用详情
  async getApplicationDetail(appId) {
    const response = await doGet(`/app-center/app/info/detail/${appId}`)
    return {
      id: response.data.id,
      appId: response.data.appCode,
      appName: response.data.appName,
      iconName: response.data.iconName,
      iconColor: response.data.iconColor,
      appState: response.data.appState,
      description: response.data.appDesc,
      appSecret: response.data.appSecret,
      createTime: response.data.createTime,
      updateTime: response.data.updateTime,
      latestReleaseVersion: response.data.latestReleaseVersion,
      owner: response.data.ownerName,
      ownerId: response.data.ownerId,
      tenantId: response.data.tenantId,
      docUrl: response.data.docUrl
    }
  },

  async getAppCallbackConfig(appId) {
    const response = await doGet(`/app-center/app/callback/config/detail/${appId}`)
    if (response.data) {
      return {
        id: response.data.id || '',
        appId: response.data.appId || '',
        aesKey: response.data.aesKey || '',
        callbackUrl: response.data.callbackUrl || ''
      }
    }
    return {
      id: '',
      appId:  '',
      aesKey:  '',
      callbackUrl:  ''
    }
  },

  async updateAppCallbackConfig(data) {
    const requestBody = {
      id: data.id,
      aesKey: data.aesKey,
      callbackUrl: data.callbackUrl
    }

    await doPut(`/app-center/app/callback/config`, requestBody)
    // 不需要处理返回值，直接返回
  },

  // 添加验证回调配置的方法
  validateCallback(data) {
    return doPost('/app-center/app/callback/config/validate', data)
  },

  // 更新应用回调配置
  updateAppCallbackConfig(data) {
    return doPut('/app-center/app/callback/config', data)
  }
} 