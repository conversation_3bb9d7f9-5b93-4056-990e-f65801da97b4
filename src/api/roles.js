import { doGet, doPost, doPut, doDelete } from '@/utils/requests'

// 获取角色列表
export function getRoles() {
    return doGet('/system/role')
}

// 获取菜单列表
export function getMenus() {
    return doGet('/system/menus')
}

// 保存新角色
export function saveRole(data) {
    return doPost('/system/role', data)
}

// 更新角色
export function updateRole(data) {
    return doPut(`/system/role/${data.id}`, data)
}

// 删除角色
export function deleteRole(id) {
    return doDelete(`/system/role/${id}`)
}

// 获取角色详情
export function getRoleDetail(id) {
    return doGet(`/system/role/${id}`)
}
