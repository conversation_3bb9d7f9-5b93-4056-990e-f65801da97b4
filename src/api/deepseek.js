import { doGet, doPost, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'

class DeepSeekAPI {
  constructor() {
    this.isPolling = false;
    this.pollingInterval = null;
    this.workDonePollingInterval = null;
  }

  async clearMessage(session_id = '') {
    try {
      // 如果有会话 ID，则传递给后端
      const url = session_id
        ? `/app-center/DeepSeekLive/message/${encodeURIComponent(session_id)}`
        : '/app-center/DeepSeekLive/message';

      await doDelete(url);
      // 只要HTTP状态码正确就返回
      return { success: true };
    } catch (error) {
      console.error('清空消息失败:', error);
      messages.error('清空消息失败');
      throw error;
    }
  }

  async sendMessage(message, model_name = 'deepseek-chat', session_id = '', request_id = '') {
    try {
      await doPost('/app-center/DeepSeekLive/message', {
        content: message,
        model_name: model_name,
        session_id: session_id,
        request_id: request_id
      });
      // 只要HTTP状态码正确就返回
      return { success: true };
    } catch (error) {
      console.error('发送消息失败:', error);
      messages.error('发送消息失败');
      throw error;
    }
  }

  async pollMessage(session_id = '', request_id = '') {
    if (this.isPolling) return;

    this.isPolling = true;
    try {
      // 构建查询参数
      const params = {};
      if (session_id) params.session_id = session_id;
      if (request_id) params.request_id = request_id;

      const response = await doGet('/app-center/DeepSeekLive/message', params);
      this.isPolling = false;
      return response;
    } catch (error) {
      this.isPolling = false;
      console.error('获取消息失败:', error);
      messages.error('获取消息失败');
      throw error;
    }
  }

  startPolling(callback, interval = 2000) {
    if (this.pollingInterval) return;

    this.pollingInterval = setInterval(async () => {
      try {
        const response = await this.pollMessage();
        if (response && response.data) {
          callback(response.data);
        }
      } catch (error) {
        // 错误已在pollMessage中处理
      }
    }, interval);
  }

  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.isPolling = false;
  }

  async checkWorkDone(session_id = '') {
    try {
      // 构建带有session_id的URL
      const url = session_id
        ? `/app-center/DeepSeekLive/work/done/${encodeURIComponent(session_id)}`
        : '/app-center/DeepSeekLive/work/done';

      const response = await doGet(url);
      return response;
    } catch (error) {
      console.error('检查任务完成状态失败:', error);
      return null;
    }
  }

  startWorkDonePolling(callback, session_id = '', interval = 1000) {
    if (this.workDonePollingInterval) {
      this.stopWorkDonePolling(); // 如果已经在轮询，先停止
    }

    this.workDonePollingInterval = setInterval(async () => {
      try {
        const response = await this.checkWorkDone(session_id);
        if (response && response.data) {
          callback(response);
          // 不再停止轮询，继续执行直到会话结束
        }
      } catch (error) {
        // 错误已在checkWorkDone中处理
      }
    }, interval);
  }

  stopWorkDonePolling() {
    if (this.workDonePollingInterval) {
      clearInterval(this.workDonePollingInterval);
      this.workDonePollingInterval = null;
    }
  }
}

export const deepseekApi = new DeepSeekAPI();