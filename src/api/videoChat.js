import { doPost, doGet } from '@/utils/requests'

/**
 * 视频聊天API
 */
export const videoChatApi = {
  /**
   * 提交视频问答任务（新接口）
   * @param {Object} params - 参数对象
   * @param {string} params.video - COS返回的key（必填）
   * @param {string} params.timestamp - 时分秒格式，例如00:01:05（必填）
   * @param {string} params.question - 用户输入的问题（必填）
   * @returns {Promise} 返回任务信息
   */
  submitVideoChat: async (params) => {
    // 验证必填参数
    if (!params.video || !params.timestamp || !params.question) {
      throw new Error('video、timestamp、question 都是必填参数')
    }

    try {
      // 直接使用 fetch 来避免响应拦截器的干扰
      const response = await fetch('/open-api/open-apis/video/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          video: params.video,
          timestamp: params.timestamp,
          question: params.question
        })
      })

      // 检查 HTTP 状态码
      if (response.status === 200) {
        // 尝试解析响应体
        const responseText = await response.text()
        let responseData = null

        try {
          responseData = JSON.parse(responseText)
        } catch (e) {
          // 如果不是 JSON，直接返回文本
          responseData = responseText
        }

        return {
          status: 200,
          success: true,
          data: responseData,
          message: 'success'
        }
      } else {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
    } catch (error) {
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络')
      }
      throw error
    }
  },

  /**
   * 轮询视频聊天结果
   * @returns {Promise} 返回聊天结果
   */
  pollVideoChatResult: () => {
    return doGet('/open-apis/video/chat')
  },

  /**
   * 获取视频问答任务结果
   * @param {string} taskId - 任务ID
   * @returns {Promise} 返回任务结果
   */
  getTaskResult: (taskId) => {
    return doGet(`/open-apis/video/chat/result?taskId=${taskId}`)
  },

  /**
   * 取消视频问答任务
   * @param {string} taskId - 任务ID
   * @returns {Promise} 返回操作结果
   */
  cancelTask: (taskId) => {
    return doPost('/open-apis/video/chat/cancel', { taskId })
  },

  /**
   * 获取视频聊天历史记录
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页大小
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @returns {Promise} 返回历史记录
   */
  getChatHistory: (params = {}) => {
    const queryParams = new URLSearchParams({
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...(params.startTime && { startTime: params.startTime }),
      ...(params.endTime && { endTime: params.endTime })
    })
    return doGet(`/open-apis/video/chat/history?${queryParams}`)
  },

  /**
   * 删除聊天记录
   * @param {string} chatId - 聊天记录ID
   * @returns {Promise} 返回操作结果
   */
  deleteChatRecord: (chatId) => {
    return doPost('/open-apis/video/chat/delete', { chatId })
  },

  /**
   * 获取视频聊天配置
   * @returns {Promise} 返回配置信息
   */
  getConfig: () => {
    return doGet('/open-apis/video/chat/config')
  },

  /**
   * 更新视频聊天配置
   * @param {Object} config - 配置对象
   * @param {number} config.maxDuration - 最大视频时长（秒）
   * @param {number} config.maxFileSize - 最大文件大小（字节）
   * @param {Array} config.supportedFormats - 支持的视频格式
   * @returns {Promise} 返回操作结果
   */
  updateConfig: (config) => {
    return doPost('/open-apis/video/chat/config', config)
  },

  /**
   * 健康检查
   * @returns {Promise} 返回服务状态
   */
  healthCheck: () => {
    return doGet('/open-apis/video/chat/health')
  }
}

/**
 * 任务状态枚举
 */
export const TaskStatus = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
}

/**
 * 响应类型枚举
 */
export const ResponseType = {
  TEXT: 'text',
  ERROR: 'error'
}
