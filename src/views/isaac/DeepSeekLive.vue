<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { deepseekApi } from '@/api/deepseek'
import messages from '@/utils/messages'
import { NSpin, NInput, NButton, NEmpty, NIcon, NSelect } from 'naive-ui'
import { SendOutline, AddOutline, MicOutline } from '@vicons/ionicons5'

// 生成随机 ID
const generateRandomId = (length = 32) => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// DeepSeek聊天相关状态
const chatMessages = ref([])
const inputMessage = ref('')
const isSending = ref(false)
const isLoadingHistory = ref(false)
const isRecording = ref(false)
const recognition = ref(null)
const chatMessagesContainer = ref(null) // 聊天消息容器引用
const sessionId = ref(generateRandomId()) // 会话 ID
const requestId = ref('') // 请求 ID
const workDoneMessageShown = ref(false) // 是否已显示任务完成消息

// 模型选择相关状态
const selectedModel = ref('deepseek-chat')
const modelOptions = [
  { label: 'Gemini-2.0-flash', value: 'gemini' },
  { label: 'GPT-4o', value: 'gpt-4o' },
  { label: 'DeepSeek-V3', value: 'deepseek-chat' },
  { label: 'DeepSeek-R1-Local', value: 'deepseek_r1_local' }
]

// 初始化语音识别
const initSpeechRecognition = () => {
  if ('webkitSpeechRecognition' in window) {
    recognition.value = new webkitSpeechRecognition()
    recognition.value.continuous = false
    recognition.value.interimResults = false
    recognition.value.lang = 'zh-CN'

    recognition.value.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      inputMessage.value = transcript
      isRecording.value = false
    }

    recognition.value.onerror = () => {
      isRecording.value = false
      messages.error('语音识别失败')
    }

    recognition.value.onend = () => {
      isRecording.value = false
    }
  }
}

// 处理语音输入
const handleVoiceInput = () => {
  if (!recognition.value) {
    messages.error('您的浏览器不支持语音识别')
    return
  }

  if (!isRecording.value) {
    recognition.value.start()
    isRecording.value = true
  } else {
    recognition.value.stop()
    isRecording.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 生成新的会话 ID
  sessionId.value = generateRandomId()
  console.log('页面加载，初始化会话 ID:', sessionId.value)

  loadChatHistory()
  initSpeechRecognition()
})

// 监听聊天消息变化，自动滚动到底部
watch(chatMessages, () => {
  scrollToBottom()
}, { deep: true })

// 本地存储key
const STORAGE_KEY = 'deepseek_chat_history'

// 保存聊天记录到localStorage
const saveChatHistory = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(chatMessages.value))
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  const userMessage = inputMessage.value.trim()
  // 添加用户消息到列表
  chatMessages.value.push({
    id: Date.now(),
    role: 'user',
    content: userMessage,
    timestamp: new Date().toLocaleTimeString()
  })
  saveChatHistory()

  // 清空输入框
  inputMessage.value = ''

  // 生成新的请求 ID
  requestId.value = generateRandomId()
  console.log('发送消息，会话 ID:', sessionId.value, '请求 ID:', requestId.value)

  // 发送请求
  isSending.value = true
  try {
    // 发送消息，传入选中的模型、会话 ID 和请求 ID
    await deepseekApi.sendMessage(
      userMessage,
      selectedModel.value,
      sessionId.value,
      requestId.value
    )

    // 重置任务完成消息状态
    workDoneMessageShown.value = false

    // 开始轮询检查任务是否完成
    deepseekApi.startWorkDonePolling((response) => {
      // 如果消息已经显示，不重复显示
      if (!workDoneMessageShown.value) {
        // 当任务完成时，添加一条系统消息
        const mainMessage = '我已经完成了您派发的任务。';

        // 创建消息对象
        const messageObj = {
          id: Date.now() + 2,
          role: 'system',
          content: mainMessage,
          timestamp: new Date().toLocaleTimeString()
        };

        // 如果有data内容，则添加引用内容
        if (response.data && response.data !== '1') {
          messageObj.quote = response.data;
        }

        chatMessages.value.push(messageObj);
        saveChatHistory();
        scrollToBottom();

        // 标记消息已显示
        workDoneMessageShown.value = true;
      }
    }, sessionId.value)

    const maxRetries = 60 // 最大轮询次数
    const pollingInterval = 1000 // 轮询间隔（毫秒）
    let retryCount = 0

    while (retryCount < maxRetries) {
      const response = await deepseekApi.pollMessage(sessionId.value, requestId.value)

      if (response.data !== null) {
        // 添加AI回复到列表
        chatMessages.value.push({
          id: Date.now() + 1,
          role: 'assistant',
          content: response.data.reply || '抱歉，我无法回答这个问题。',
          timestamp: new Date().toLocaleTimeString()
        })
        saveChatHistory()
        scrollToBottom() // 滚动到底部
        break
      }

      // 如果data为null，等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, pollingInterval))
      retryCount++

      // 如果达到最大重试次数，显示超时消息
      if (retryCount === maxRetries) {
        chatMessages.value.push({
          id: Date.now() + 1,
          role: 'system',
          content: '响应超时，请稍后重试。',
          timestamp: new Date().toLocaleTimeString(),
          isError: true
        })
        saveChatHistory()
        scrollToBottom() // 滚动到底部
      }
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    messages.error('发送消息失败')
    // 停止任务完成轮询
    deepseekApi.stopWorkDonePolling()
    // 添加错误消息
    chatMessages.value.push({
      id: Date.now() + 1,
      role: 'system',
      content: '消息发送失败，请重试。',
      timestamp: new Date().toLocaleTimeString(),
      isError: true
    })
    saveChatHistory()
    scrollToBottom() // 滚动到底部
  } finally {
    isSending.value = false
  }
}

// 处理按键事件
const handleKeyPress = (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    sendMessage()
  }
}

// 获取输入框引用
const inputRef = ref(null)

// 滚动到聊天底部
const scrollToBottom = () => {
  if (chatMessagesContainer.value) {
    setTimeout(() => {
      chatMessagesContainer.value.scrollTop = chatMessagesContainer.value.scrollHeight
    }, 100)
  }
}

// 加载历史消息
const loadChatHistory = () => {
  isLoadingHistory.value = true
  try {
    const savedHistory = localStorage.getItem(STORAGE_KEY)
    if (savedHistory) {
      chatMessages.value = JSON.parse(savedHistory)
      scrollToBottom() // 加载历史消息后滚动到底部
    }
  } catch (error) {
    console.error('加载历史消息失败:', error)
    messages.error('加载历史消息失败')
  } finally {
    isLoadingHistory.value = false
  }
}

// 新建对话
const newChat = () => {
  // 清除聊天记录
  chatMessages.value = []
  localStorage.removeItem(STORAGE_KEY)

  // 生成新的会话 ID
  sessionId.value = generateRandomId()
  console.log('新建对话，会话 ID:', sessionId.value)

  // 重置任务完成消息状态
  workDoneMessageShown.value = false

  // 停止所有轮询
  deepseekApi.stopPolling()
  deepseekApi.stopWorkDonePolling()

  // 传递会话 ID 清除消息
  deepseekApi.clearMessage(sessionId.value);

  messages.success('已清除聊天记录')
}

// 组件挂载时加载历史消息
onMounted(() => {
  loadChatHistory()
})

// 组件卸载时停止所有轮询
onUnmounted(() => {
  deepseekApi.stopPolling()
  deepseekApi.stopWorkDonePolling()
})
</script>

<template>
  <div class="deepseek-live">
    <!-- 左侧聊天区域 -->
    <div class="chat-section">
      <!-- 聊天历史区域 -->
      <div class="chat-header">
        <h2>Chat With Robot</h2>
        <n-button
          type="default"
          circle
          @click="newChat"
          title="新建对话"
        >
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
        </n-button>
      </div>
      <div class="chat-messages" ref="chatMessagesContainer">
        <n-spin v-if="isLoadingHistory" />
        <template v-else-if="chatMessages.length > 0">
          <!-- 循环渲染消息 -->
          <template v-for="message in chatMessages" :key="message.id">
            <!-- 消息框 -->
            <div :class="['message', message.role, { 'error': message.isError }]">
              <div class="message-content">
                <div class="message-text">
                  <!-- 常规消息处理 -->
                  <template v-for="(part, index) in message.content.split(/```(.*?)```/g)" :key="index">
                    <pre class="code-block" v-if="index % 2 === 1"><code>{{ part }}</code></pre>
                    <span v-else>{{ part }}</span>
                  </template>
                </div>
                <div class="message-time">{{ message.timestamp }}</div>
              </div>
            </div>

            <!-- 引用内容（如果有），作为单独的元素存在 -->
            <div v-if="message.quote" class="message-quote">
              {{ message.quote }}
            </div>
          </template>
        </template>
        <n-empty v-else description="暂无聊天记录" />
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <!-- 文本输入区 -->
          <div class="input-text-area">
            <n-input
              ref="inputRef"
              v-model:value="inputMessage"
              type="textarea"
              placeholder="立即与我聊天吧！"
              :autosize="{ minRows: 5, maxRows: 10 }"
              :maxlength="1024"
              show-count
              @keydown="handleKeyPress"
              class="message-input"
            />
          </div>

          <!-- 工具栏 -->
          <div class="input-toolbar">
            <!-- 左侧工具 -->
            <div class="toolbar-left">
              <n-button
                type="default"
                circle
                size="small"
                :class="{ 'recording': isRecording }"
                @click="handleVoiceInput"
                title="语音输入"
              >
                <template #icon>
                  <n-icon><MicOutline /></n-icon>
                </template>
              </n-button>

              <div class="model-selector">
                <n-select
                  v-model:value="selectedModel"
                  :options="modelOptions"
                  size="small"
                  :consistent-menu-width="false"
                />
              </div>
            </div>

            <!-- 右侧工具 -->
            <div class="toolbar-right">
              <n-button
                type="primary"
                circle
                :loading="isSending"
                :disabled="!inputMessage.trim()"
                @click="sendMessage"
              >
                <template #icon>
                  <n-icon><SendOutline /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧直播区域 -->
    <div class="live-section">
      <iframe src="/isaac_live.html" frameborder="0"></iframe>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use './deepseek.scss';

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);

  h2 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color-1);
  }
}

.code-block {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  margin: 8px 0;
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.45;
  overflow-x: auto;
}

/* 样式已移至 deepseek.scss */

/* 覆盖消息框的样式，增加间距 */
.message {
  margin-bottom: 8px !important; /* 覆盖deepseek.scss中的样式 */
}

/* 当消息有引用时，增加额外的间距 */
.message + .message-quote + .message {
  margin-top: 24px !important;
}

.recording {
  color: #f56c6c;
  animation: pulse 1.5s infinite;
}

/* 消息引用样式 */
.message-quote {
  display: block;
  margin: 0 20px 20px 60px; /* 调整上边距，使其与消息框分离 */
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-left: 4px solid #ddd;
  border-radius: 4px;
  font-size: 0.9em;
  color: #666;
  white-space: pre-wrap;
  word-break: break-word;
  max-width: 70%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 添加阴影使其更突出 */
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>