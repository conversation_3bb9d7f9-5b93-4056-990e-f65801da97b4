.isaac-page {
  display: flex;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
}

.tab {
  flex: 1;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.tab:hover {
  background-color: #f5f5f5;
}

.tab.active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
}

.list-content {
  flex: 1;
  overflow-y: auto;
}

.list {
  padding: 8px;
}

.list-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.list-item:hover {
  background: #e6f7ff;
}

.add-scene-button {
  background: #18a058;
  color: white;
}

.add-scene-button:hover {
  background: #73d13d;
}

.scene-title {
  display: flex;
  align-items: center;
}

.scene-title-input {
  font-size: 18px;
  color: #1f1f1f;
  border: none;
  border-bottom: 1px solid transparent;
  background: transparent;
  padding: 4px 8px;
  margin: 0;
  width: 200px;
  transition: all 0.3s;
}

.scene-title-input:hover,
.scene-title-input:focus {
  border-bottom-color: #1890ff;
  outline: none;
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.canvas-header {
  padding: 0 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 45px;
}

.canvas-header h2 {
  margin: 0;
  font-size: 18px;
  color: #1f1f1f;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.save-button {
  padding: 6px 16px;
  background: #18a058;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.send-button {
  padding: 6px 16px;
  background: rgb(241, 244, 246);
  color: rgb(84, 84, 84);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.canvas {
  flex: 1;
  position: relative;
  background: #fafafa;
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  grid-template-rows: repeat(10, 1fr);
  position: relative;
  padding: 0;
  margin: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(to right, #e8e8e8 1px, transparent 1px),
      linear-gradient(to bottom, #e8e8e8 1px, transparent 1px);
    background-size: 10% 10%;
    pointer-events: none;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
    pointer-events: none;
    z-index: 1;
  }

  /* 添加网格编号 */
  &>div[data-grid-number] {
    position: absolute;
    width: 10%;
    height: 10%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 12px;
    pointer-events: none;
    z-index: 1;
  }
}

.canvas-item {
  position: absolute;
  background: white;
  border: 2px solid #52c41a;
  border-radius: 4px;
  padding: 4px;
  width: 10%;
  height: 10%;
  cursor: move;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
  box-sizing: border-box;
  font-size: 12px;
  transition: all 0.2s ease;
  transform-origin: center center;
  text-align: center;
}

.canvas-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.canvas-item:hover .delete-icon,
.canvas-item:hover .rotate-x-icon,
.canvas-item:hover .rotate-y-icon,
.canvas-item:hover .rotate-z-icon {
  opacity: 1;
}

/* 预览区域样式 */
.preview-area {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 300px;
  height: 200px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform-origin: bottom right;
  transition: none;
  z-index: 1000;
  display: flex;
  flex-direction: column;

  &.fullscreen {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    transform-origin: bottom right;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.preview-header {
  height: 32px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: move;
  user-select: none;
  position: relative;
}

.fullscreen-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #666;
  cursor: pointer;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-icon:hover {
  color: #333;
}

.preview-content {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.preview-content iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.preview-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.3s;
  z-index: 100;
  pointer-events: auto;
}

.preview-area:hover .preview-icon {
  opacity: 1;
}

.preview-icon:hover {
  background: rgba(0, 0, 0, 0.7);
}

.category-item {
  margin-bottom: 8px;
}

.category-header {
  padding: 8px 12px;
  background: #e6f7ff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.category-header:hover {
  background: #bae7ff;
}

.category-icon {
  margin-right: 8px;
  font-size: 12px;
}

.category-children {
  padding-left: 16px;
  margin-top: 4px;
}

.list-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f0f0f0;
  pointer-events: none;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}


.delete-icon,
.rotate-x-icon,
.rotate-y-icon,
.rotate-z-icon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15px;
  height: 15px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  opacity: 0;
  padding: 5px;
  font-weight: bold;
}

.delete-icon {
  top: -23px;
  right: -23px;
  color: #ff4d4f;
}

.rotate-x-icon {
  top: -23px;
  left: -23px;
  color: #666;

  &.current {
    background-color: #18a058;
    color: white;
  }
}

.rotate-y-icon {
  bottom: -23px;
  left: -23px;
  color: #666;

  &.current {
    background-color: #18a058;
    color: white;
  }
}

.rotate-z-icon {
  bottom: -23px;
  right: -23px;
  color: #666;

  &.current {
    background-color: #18a058;
    color: white;
  }
}

.delete-icon:hover,
.rotate-x-icon:hover,
.rotate-y-icon:hover,
.rotate-z-icon:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.rotate-text,
.delete-svg {
  pointer-events: none;
  /* 确保文本和SVG不会拦截点击事件 */
}

.scene-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-title-input {
  border: none;
  outline: none;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  width: 100%;
  max-width: 200px;
  background: transparent;
  padding: 4px 8px;
  border-radius: 4px;
}

.scene-title-input:hover,
.scene-title-input:focus {
  background: rgba(0, 0, 0, 0.05);
}

.edit-icon {
  width: 24px;
  height: 24px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.edit-icon:hover {
  color: #333;
}

.direction-controls {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  z-index: 10;

  .direction-icon {
    position: absolute;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.95);
    color: #666;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: auto;
    font-size: 14px;
    transition: transform 0.2s, background 0.3s;
    z-index: 11;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    padding: 6px;

    svg {
      width: 100%;
      height: 100%;
      pointer-events: none;
      /* 确保SVG不会拦截点击事件 */
    }

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    &.top {
      top: -30px;
      left: calc(50% - 16px);

      &.current {
        background: #18a058 !important;
        color: #fff !important;
      }
    }

    &.right {
      right: -30px;
      top: calc(50% - 16px);

      &.current {
        background: #18a058 !important;
        color: #fff !important;
      }
    }

    &.bottom {
      bottom: -30px;
      left: calc(50% - 16px);

      &.current {
        background: #18a058 !important;
        color: #fff !important;
      }
    }

    &.left {
      left: -30px;
      top: calc(50% - 16px);

      &.current {
        background: #18a058 !important;
        color: #fff !important;
      }
    }
  }
}

.canvas-item:hover .direction-controls {
  opacity: 1;
}