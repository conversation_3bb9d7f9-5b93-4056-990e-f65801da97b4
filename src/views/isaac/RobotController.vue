<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { deepseekApi } from '@/api/deepseek'
import messages from '@/utils/messages'
import { NSpin, NInput, NButton, NEmpty, NIcon, NSelect } from 'naive-ui'
import { SendOutline, AddOutline, MicOutline } from '@vicons/ionicons5'
import { isaacApi } from '@/api/isaac'
// —— SLAM 地图真实坐标四角点（请根据你的地图实际值修改） ——  
const mapCorners = {
  topLeft:     { x: -2100.00, y: 1900.00 },   // 左上角
  topRight:    { x: 2050.00, y: 1900.00 },   // 右上角
  bottomLeft:  { x: -2100.00, y: -1850.00 },   // 左下角
  bottomRight: { x: 2050.00, y: -1850.00 }    // 右下角
}

// 将归一化（[0,1]）显示坐标映射到真实地图坐标系的双线性插值函数
function mapDisplayToReal(normX, normY) {
  const { topLeft, topRight, bottomLeft, bottomRight } = mapCorners
  const x =
    (1 - normX) * (1 - normY) * topLeft.x +
      normX   * (1 - normY) * topRight.x +
    (1 - normX) *   normY   * bottomLeft.x +
      normX   *   normY   * bottomRight.x

  const y =
    (1 - normX) * (1 - normY) * topLeft.y +
      normX   * (1 - normY) * topRight.y +
    (1 - normX) *   normY   * bottomLeft.y +
      normX   *   normY   * bottomRight.y

  return { x, y }
}

// 生成随机 ID
const generateRandomId = (length = 32) => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// DeepSeek聊天相关状态
const chatMessages = ref([])
const inputMessage = ref('')
const isSending = ref(false)
const isLoadingHistory = ref(false)
const isRecording = ref(false)
const recognition = ref(null)
const chatMessagesContainer = ref(null)
const sessionId = ref(generateRandomId())
const requestId = ref('')
const workDoneMessageShown = ref(false)

// 模型选择相关状态
const selectedModel = ref('deepseek-chat')
const modelOptions = [
  { label: 'Gemini-2.0-flash', value: 'gemini' },
  { label: 'GPT-4o', value: 'gpt-4o' },
  { label: 'DeepSeek-V3', value: 'deepseek-chat' },
  { label: 'DeepSeek-R1-Local', value: 'deepseek_r1_local' }
]

// CDN 基础地址
const cdnBaseUrl = 'https://cos-cdn-v1.qj-robots.com/'

// 图片/视频 key
const rgbKey = ref('')
const depthKey = ref('')
const slamKey = ref('')

// 存储媒体类型
const rgbMediaType = ref('image') // image, video
const depthMediaType = ref('image')
const slamMediaType = ref('image')

// 存储 display_text
const lastDisplayText = ref('')

// 判断媒体类型
const determineMediaType = (filename) => {
  if (!filename) return 'image'
  const extension = filename.split('.').pop()?.toLowerCase()
  if (['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'].includes(extension)) {
    return 'image'
  }
  if (['mp4', 'webm', 'avi', 'mov'].includes(extension)) {
    return 'video'
  }
  if (extension === 'stream') {
    return 'stream'
  }
  return 'image'
}

// 解析 display_text 获取媒体类型
const updateMediaTypesFromText = (text) => {
  if (!text) return
  const match = text.match(/The (.+?)，(.+?)，(.+?) has been uploaded/)
  if (match) {
    const [_, rgbFile, depthFile, slamFile] = match
    rgbMediaType.value = determineMediaType(rgbFile)
    depthMediaType.value = determineMediaType(depthFile)
    slamMediaType.value = determineMediaType(slamFile)
    console.log('Media Types from display_text:', {
      rgb: rgbMediaType.value,
      depth: depthMediaType.value,
      slam: slamMediaType.value
    })
  }
}

// 文本显示相关
const lastTextTimestamp = ref(0)
let textPollingInterval = null

const fetchDisplayText = async () => {
  try {
    const res = await isaacApi.getDisplayText()
    console.log('获取文本响应:', JSON.stringify(res.data, null, 2))
    
    const { text, timestamp } = res.data
    if (!text || !timestamp) {
      console.log('无有效文本或时间戳:', { text, timestamp })
      return
    }

    console.log('检查文本:', { text, timestamp, lastTextTimestamp: lastTextTimestamp.value })

    if (Number(timestamp) > lastTextTimestamp.value) {
      console.log('发现新文本，显示:', text)
      chatMessages.value.push({
        id: Date.now(),
        role: 'system',
        content: text,
        timestamp: new Date().toLocaleTimeString()
      })
      saveChatHistory()
      scrollToBottom()
      lastTextTimestamp.value = Number(timestamp)
      lastDisplayText.value = text
      updateMediaTypesFromText(text)
      console.log('更新时间戳:', lastTextTimestamp.value)
    } else {
      console.log('文本未更新，跳过显示:', { timestamp, lastTextTimestamp: lastTextTimestamp.value })
    }
  } catch (e) {
    console.error('获取显示文本失败:', e.message, e.stack)
  }
}

// 启动文本轮询
const startTextPolling = () => {
  console.log('启动文本轮询')
  stopTextPolling()
  fetchDisplayText()
  textPollingInterval = setInterval(fetchDisplayText, 1000) // 文本频率1Hz
}

// 停止文本轮询
const stopTextPolling = () => {
  if (textPollingInterval) {
    console.log('停止文本轮询')
    clearInterval(textPollingInterval)
    textPollingInterval = null
  }
}

// display/target 轮询相关
let targetPollingInterval = null
const targetPosition = ref({ x: 0, y: 0, yaw: 0 })

const fetchDisplayTarget = async () => {
  try {
    const res = await isaacApi.getDisplayTarget()
    console.log('获取坐标响应:', JSON.stringify(res.data, null, 2))

    if (!res.data) {
      console.log('响应数据为空')
      messages.error('获取坐标失败: 响应为空')
      return
    }

    const { x, y, yaw } = res.data
    console.log('解析坐标:', { x, y, yaw })

    if (x == null || y == null || yaw == null) {
      console.log('坐标数据缺失:', { x, y, yaw })
      messages.error('坐标数据不完整')
      return
    }

    console.log('更新坐标:', { x, y, yaw })
    targetPosition.value = { x: Number(x), y: Number(y), yaw: Number(yaw) }
  } catch (e) {
    console.error('获取坐标失败:', e.message, e.stack, e.response?.data)
    messages.error('获取坐标失败: 网络或服务器错误')
  }
}

const startTargetPolling = () => {
  console.log('启动坐标轮询')
  stopTargetPolling()
  fetchDisplayTarget()
  targetPollingInterval = setInterval(fetchDisplayTarget, 100) // 坐标频率10hz
}

const stopTargetPolling = () => {
  if (targetPollingInterval) {
    console.log('停止坐标轮询')
    clearInterval(targetPollingInterval)
    targetPollingInterval = null
  }
}

let imageInterval = null

const fetchImageKeys = async () => {
  try {
    const res = await isaacApi.getLatestKeys()
    rgbKey.value = res.data.rgb
    depthKey.value = res.data.depth
    slamKey.value = res.data.slam
    console.log('Keys:', res.data)
  } catch (e) {
    messages.error('获取媒体key失败')
  }
}

// slam自适应遮罩相关
const slamImg = ref(null)
const slamVideo = ref(null)
const slamWrapper = ref(null)
const overlayStyle = ref({})
const slamImgStyle = ref({})
const isKeyboardLocked = ref(false)
const arrowData = ref(null)

// 红圈样式
const circleStyle = ref({})

const updateSlamOverlay = () => {
  nextTick(() => {
    const media = slamImg.value || slamVideo.value
    const wrapper = slamWrapper.value
    if (!media || !wrapper) {
      console.log('媒体或包装器未准备好，跳过更新')
      return
    }

    const mediaNaturalRatio = media.naturalWidth ? media.naturalWidth / media.naturalHeight : 4/3
    const wrapperRatio = wrapper.clientWidth / wrapper.clientHeight

    let width, height
    if (mediaNaturalRatio >= wrapperRatio) {
      width = '100%'
      height = `${wrapper.clientWidth / mediaNaturalRatio}px`
    } else {
      height = '100%'
      width = `${wrapper.clientHeight * mediaNaturalRatio}px`
    }
    slamImgStyle.value = {
      width,
      height,
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      objectFit: 'contain',
      borderRadius: '8px',
      background: '#222',
      zIndex: 1
    }
    overlayStyle.value = {
      width,
      height,
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      background: 'transparent',
      zIndex: 4,
      pointerEvents: 'auto'
    }

    // 计算红圈位置 - 修正后的逻辑
    if (targetPosition.value) {
      // 将真实坐标转换为归一化显示坐标
      const { x, y } = targetPosition.value
      
      // 计算归一化坐标 (0-1)
      // 注意：这里假设地图坐标系Y轴向上，与显示坐标系的Y轴方向可能相反
      const normX = (x - mapCorners.bottomLeft.x) / (mapCorners.bottomRight.x - mapCorners.bottomLeft.x)
      const normY = 1 - ((y - mapCorners.bottomLeft.y) / (mapCorners.topLeft.y - mapCorners.bottomLeft.y))
      
      // 转换为百分比坐标
      const circleX = normX * 100
      const circleY = normY * 100
      
      // 计算箭头方向（基于 targetPosition.value.yaw）
      const yaw = targetPosition.value.yaw ? parseFloat(targetPosition.value.yaw) : 0
      console.log('更新箭头方向，yaw:', yaw, 'targetPosition:', targetPosition.value)
      
      // 注意：yaw=0 通常指向地图上方（Y轴正方向），90度指向右（X轴正方向）
      const rad = (-(yaw - 90) * Math.PI) / 180 // 转换为弧度
      const arrowLength = 25 // 箭头长度（红圈半径的一半）
      
      circleStyle.value = {
        width: '50px',
        height: '50px',
        borderRadius: '50%',
        border: '2px solid red',
        position: 'absolute',
        left: `${circleX}%`,
        top: `${circleY}%`,
        transform: 'translate(-50%, -50%)',
        zIndex: 3,
        background: 'transparent',
        overflow: 'visible',
        '--arrow-x2': `${25 + arrowLength * Math.cos(rad)}px`,
        '--arrow-y2': `${25 + arrowLength * Math.sin(rad)}px`
      }
      
      console.log('更新红圈位置:', {
        realX: x,
        realY: y,
        normX,
        normY,
        circleX,
        circleY,
        circleStyle: circleStyle.value
      })
    }
  })
}

// 初始化语音识别
const initSpeechRecognition = () => {
  if ('webkitSpeechRecognition' in window) {
    recognition.value = new webkitSpeechRecognition()
    recognition.value.continuous = false
    recognition.value.interimResults = false
    recognition.value.lang = 'zh-CN'

    recognition.value.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      inputMessage.value = transcript
      isRecording.value = false
    }

    recognition.value.onerror = () => {
      isRecording.value = false
      messages.error('语音识别失败')
    }

    recognition.value.onend = () => {
      isRecording.value = false
    }
  }
}

// 处理语音输入
const handleVoiceInput = () => {
  if (!recognition.value) {
    messages.error('您的浏览器不支持语音识别')
    return
  }

  if (!isRecording.value) {
    recognition.value.start()
    isRecording.value = true
  } else {
    recognition.value.stop()
    isRecording.value = false
  }
}

// 发送控制命令
const sendRobotCommand = async (payload) => {
  try {
    const response = await isaacApi.setRobotTarget(payload)
    console.log('命令发送成功:', payload, response)
    saveChatHistory()
    scrollToBottom()
    // 在发送命令后立即获取最新的 targetPosition
    await fetchDisplayTarget()
  } catch (error) {
    console.error('发送命令失败:', error)
    messages.error('发送命令失败')
  }
}

// 键盘控制处理
const handleKeyUp = async (e) => {
  if (isKeyboardLocked.value) {
    console.log('键盘被锁定，忽略按键:', e.key)
    return
  }
  const key = e.key.toUpperCase()
  if (!['W', 'A', 'S', 'D', 'C', 'V', 'F', 'G'].includes(key)) {
    console.log('忽略非控制按键:', key)
    return
  }

  e.preventDefault()
  isKeyboardLocked.value = true

  try {
    if (['W', 'A', 'S', 'D'].includes(key)) {
      await fetchDisplayTarget()
      if (!targetPosition.value) {
        messages.error('无法获取当前位置')
        return
      }

      let { x, y, yaw } = targetPosition.value
      x = parseFloat(x)
      y = parseFloat(y)
      yaw = parseFloat(yaw)

      if (isNaN(x) || isNaN(y) || isNaN(yaw)) {
        console.error('无效的位置数据:', targetPosition.value)
        messages.error('位置数据格式错误，使用默认值 (0, 0, 0)')
        x = 0
        y = 0
        yaw = 0
      }

      console.log('解析后的位置数据:', { x, y, yaw })

      const rad = (yaw * Math.PI) / 180
      const step = 350
      const angleStep = 30

      let newX = x
      let newY = y
      let newAngle = yaw

      if (key === 'W') {
        newX += step * Math.cos(rad)
        newY += step * Math.sin(rad)
      } else if (key === 'S') {
        newX -= step * Math.cos(rad)
        newY -= step * Math.sin(rad)
      } else if (key === 'A') {
        newAngle = (yaw + angleStep) % 360
      } else if (key === 'D') {
        newAngle = (yaw - angleStep + 360) % 360
      }

      const payload = {
        x: newX.toFixed(3),
        y: newY.toFixed(3),
        yaw: newAngle.toFixed(3),
      }

      console.log('发送目标命令 payload:', payload)
      await sendRobotCommand(payload)
    } else if (['C', 'V', 'F', 'G'].includes(key)) {
      const commands = {
        'C': 'C_KEY_DOWN',
        'V': 'V_KEY_DOWN',
        'F': 'F_KEY_DOWN',
        'G': 'G_KEY_DOWN'
      }
      const payload = { command: commands[key] }
      console.log('发送操作命令:', payload)
      await isaacApi.setRobotOperation(payload)
      console.log('操作命令发送成功:', key)
    }
  } catch (error) {
    console.error('处理键盘命令失败:', error)
    messages.error('处理键盘命令失败')
  } finally {
    setTimeout(() => {
      isKeyboardLocked.value = false
      console.log('键盘锁释放')
    }, 100)
  }
}
const stopRobot = async () => {
  try {
    const response = await isaacApi.stopRobot()
    console.log('停止命令已发送:', response)
    messages.success('机器人已停止')
  } catch (error) {
    console.error('停止命令发送失败:', error)
    messages.error('停止失败: ' + error.message)
  }
}
// 鼠标点击处理
const handleOverlayMouseDown = (event) => {
  const overlay = event.currentTarget
  const rect = overlay.getBoundingClientRect()
  const x = (event.clientX - rect.left) / rect.width
  const y = (event.clientY - rect.top) / rect.height
  arrowData.value = { startX: x, startY: y, endX: x, endY: y }
}

const handleOverlayMouseMove = (event) => {
  if (!arrowData.value) return
  const overlay = event.currentTarget
  const rect = overlay.getBoundingClientRect()
  arrowData.value.endX = (event.clientX - rect.left) / rect.width
  arrowData.value.endY = (event.clientY - rect.top) / rect.height
}

const handleOverlayMouseUp = async (event) => {
  if (!arrowData.value) return
  const overlay = event.currentTarget
  const rect = overlay.getBoundingClientRect()
  const x2 = (event.clientX - rect.left) / rect.width
  const y2 = (event.clientY - rect.top) / rect.height

  // —— 用双线性插值映射到真实坐标 ——  
  const startPoint = mapDisplayToReal(
    arrowData.value.startX,
    arrowData.value.startY
  )
  const endPoint = mapDisplayToReal(x2, y2)
  const dx = endPoint.x - startPoint.x
  const dy = endPoint.y - startPoint.y

  let angle = Math.atan2(-dy, dx) * 180 / Math.PI
  angle = (-angle + 360) % 360

  const payload = {
  x: startPoint.x.toFixed(3),
  y: startPoint.y.toFixed(3),
  yaw: angle.toFixed(3)
  }


  await sendRobotCommand(payload)
  arrowData.value = null
}

// 组件挂载时初始化
onMounted(() => {
  sessionId.value = generateRandomId()
  console.log('页面加载，初始化会话 ID:', sessionId.value)

  loadChatHistory()
  initSpeechRecognition()
  fetchImageKeys()
  imageInterval = setInterval(fetchImageKeys, 1000) // 图片轮询频率
  startTextPolling()
  startTargetPolling()

  window.addEventListener('resize', updateSlamOverlay)
  window.addEventListener('keyup', handleKeyUp)
})

// 监听slam媒体加载和key变化
watch(slamKey, () => {
  nextTick(() => {
    if (slamMediaType.value === 'video' && slamVideo.value) {
      slamVideo.value.src = cdnBaseUrl + slamKey.value
      slamVideo.value.load()
      slamVideo.value.play().catch(e => console.error('SLAM视频播放失败:', e))
    }
    updateSlamOverlay()
  })
})

watch([rgbKey, depthKey], () => {
  nextTick(() => {
    if (rgbMediaType.value === 'video' && rgbVideo.value) {
      rgbVideo.value.src = cdnBaseUrl + rgbKey.value
      rgbVideo.value.load()
      rgbVideo.value.play().catch(e => console.error('RGB视频播放失败:', e))
    }
    if (depthMediaType.value === 'video' && depthVideo.value) {
      depthVideo.value.src = cdnBaseUrl + depthKey.value
      depthVideo.value.load()
      depthVideo.value.play().catch(e => console.error('Depth视频播放失败:', e))
    }
  })
})

watch(targetPosition, () => {
  console.log('监听器触发，targetPosition:', targetPosition.value)
  nextTick(() => {
    updateSlamOverlay()
  })
})

onMounted(() => {
  nextTick(() => {
    updateSlamOverlay()
  })
})

// 监听聊天消息变化，自动滚动到底部
watch(chatMessages, () => {
  scrollToBottom()
}, { deep: true })

// 本地存储key
const STORAGE_KEY = 'deepseek_chat_history'

// 保存聊天记录到localStorage
const saveChatHistory = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(chatMessages.value))
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  const userMessage = inputMessage.value.trim()
  chatMessages.value.push({
    id: Date.now(),
    role: 'user',
    content: userMessage,
    timestamp: new Date().toLocaleTimeString()
  })
  saveChatHistory()

  inputMessage.value = ''
  requestId.value = generateRandomId()
  console.log('发送消息，会话 ID:', sessionId.value, '请求 ID:', requestId.value)

  isSending.value = true
  try {
    await deepseekApi.sendMessage(
      userMessage,
      selectedModel.value,
      sessionId.value,
      requestId.value
    )

    workDoneMessageShown.value = false

    deepseekApi.startWorkDonePolling((response) => {
      if (!workDoneMessageShown.value) {
        const mainMessage = '我已经完成了您派发的任务。'
        const messageObj = {
          id: Date.now() + 2,
          role: 'system',
          content: mainMessage,
          timestamp: new Date().toLocaleTimeString()
        }

        if (response.data && response.data !== '1') {
          messageObj.quote = response.data
        }

        chatMessages.value.push(messageObj)
        saveChatHistory()
        scrollToBottom()
        workDoneMessageShown.value = true
      }
    }, sessionId.value)

    const maxRetries = 2
    const pollingInterval = 1000
    let retryCount = 0

    while (retryCount < maxRetries) {
      const response = await deepseekApi.pollMessage(sessionId.value, requestId.value)

      if (response.data !== null) {
        chatMessages.value.push({
          id: Date.now() + 1,
          role: 'assistant',
          content: response.data.reply || '抱歉，我无法回答这个问题。',
          timestamp: new Date().toLocaleTimeString()
        })
        saveChatHistory()
        scrollToBottom()
        break
      }

      await new Promise(resolve => setTimeout(resolve, pollingInterval))
      retryCount++

      if (retryCount === maxRetries) {
        chatMessages.value.push({
          id: Date.now() + 1,
          role: 'system',
          content: '响应超时，请稍后重试。',
          timestamp: new Date().toLocaleTimeString(),
          isError: true
        })
        saveChatHistory()
        scrollToBottom()
      }
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    messages.error('发送消息失败')
    deepseekApi.stopWorkDonePolling()
    chatMessages.value.push({
      id: Date.now() + 1,
      role: 'system',
      content: '消息发送失败，请重试。',
      timestamp: new Date().toLocaleTimeString(),
      isError: true
    })
    saveChatHistory()
    scrollToBottom()
  } finally {
    isSending.value = false
  }
}

// 处理按键事件
const handleKeyPress = (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    sendMessage()
  }
}

const inputRef = ref(null)
const rgbVideo = ref(null)
const depthVideo = ref(null)

const scrollToBottom = () => {
  if (chatMessagesContainer.value) {
    setTimeout(() => {
      chatMessagesContainer.value.scrollTop = chatMessagesContainer.value.scrollHeight
    }, 100)
  }
}

const loadChatHistory = () => {
  isLoadingHistory.value = true
  try {
    const savedHistory = localStorage.getItem(STORAGE_KEY)
    if (savedHistory) {
      chatMessages.value = JSON.parse(savedHistory)
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载历史消息失败:', error)
    messages.error('加载历史消息失败')
  } finally {
    isLoadingHistory.value = false
  }
}

const newChat = () => {
  lastTextTimestamp.value = 0
  chatMessages.value = []
  localStorage.removeItem(STORAGE_KEY)
  sessionId.value = generateRandomId()
  console.log('新建对话，会话 ID:', sessionId.value)
  workDoneMessageShown.value = false
  deepseekApi.stopPolling()
  deepseekApi.stopWorkDonePolling()
  deepseekApi.clearMessage(sessionId.value)
  chatMessages.value.push({
    id: Date.now(),
    role: 'system',
    content: '已清除聊天记录',
    timestamp: new Date().toLocaleTimeString()
  })
  saveChatHistory()
  scrollToBottom()
}

onUnmounted(() => {
  window.removeEventListener('resize', updateSlamOverlay)
  window.removeEventListener('keyup', handleKeyUp)
  clearInterval(imageInterval)
  stopTextPolling()
  stopTargetPolling()
  deepseekApi.stopPolling()
  deepseekApi.stopWorkDonePolling()
})

</script>

<template>
  <div class="deepseek-live">
    <!-- 聊天区域 -->
    <div class="chat-section">
      <div class="chat-header">
        <h2>Chat With Robot</h2>
        <n-button
          type="default"
          circle
          @click="newChat"
          title="新建对话"
        >
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
        </n-button>
      </div>
      <div class="chat-messages" ref="chatMessagesContainer">
        <n-spin v-if="isLoadingHistory" />
        <template v-else-if="chatMessages.length > 0">
          <template v-for="message in chatMessages" :key="message.id">
            <div :class="['message', message.role, { 'error': message.isError }]">
              <div class="message-content">
                <div class="message-text">
                  <template v-for="(part, index) in message.content.split(/```(.*?)```/g)" :key="index">
                    <pre class="code-block" v-if="index % 2 === 1"><code>{{ part }}</code></pre>
                    <span v-else>{{ part }}</span>
                  </template>
                </div>
                <div class="message-time">{{ message.timestamp }}</div>
              </div>
            </div>
            <div v-if="message.quote" class="message-quote">
              {{ message.quote }}
            </div>
          </template>
        </template>
        <n-empty v-else description="暂无聊天记录" />
      </div>
      <div class="chat-input-area">
        <div class="input-container">
          <div class="input-text-area">
            <n-input
              ref="inputRef"
              v-model:value="inputMessage"
              type="textarea"
              placeholder="立即与我聊天吧！"
              :autosize="{ minRows: 5, maxRows: 10 }"
              :maxlength="1024"
              show-count
              @keydown="handleKeyPress"
              class="message-input"
            />
          </div>
          <div class="input-toolbar">
            <div class="toolbar-left">
              <n-button
                type="default"
                circle
                size="small"
                :class="{ 'recording': isRecording }"
                @click="handleVoiceInput"
                title="语音输入"
              >
                <template #icon>
                  <n-icon><MicOutline /></n-icon>
                </template>
              </n-button>
              <div class="model-selector">
                <n-select
                  v-model:value="selectedModel"
                  :options="modelOptions"
                  size="small"
                  :consistent-menu-width="false"
                />
              </div>
            </div>
            <div class="toolbar-right">
              <n-button
                type="primary"
                circle
                :loading="isSending"
                :disabled="!inputMessage.trim()"
                @click="sendMessage"
              >
                <template #icon>
                  <n-icon><SendOutline /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 视口区域 -->
    <div class="viewport-section">
      <!-- RGB视口 -->
      <div class="viewport">
        <img
          v-if="rgbKey && rgbMediaType === 'image'"
          :src="cdnBaseUrl + rgbKey"
          alt="RGB"
          class="viewport-img"
        />
        <video
          v-else-if="rgbKey && rgbMediaType === 'video'"
          ref="rgbVideo"
          class="viewport-img"
          controls
          autoplay
          muted
          :src="cdnBaseUrl + rgbKey"
        />
        
        <!-- 停止按钮现在在viewport内部 -->
        <div class="stop-button-container">
          <n-button 
            type="error" 
            size="large"
            @click="stopRobot"
            class="stop-button"
          >
            紧急停止
          </n-button>
        </div>
      </div>
      <!-- SLAM视口 -->
      <div class="viewport">
        <div class="slam-img-wrapper" ref="slamWrapper">
          <img
            v-if="slamKey && slamMediaType === 'image'"
            :src="cdnBaseUrl + slamKey"
            alt="SLAM"
            class="slam-img"
            ref="slamImg"
            :style="slamImgStyle"
            @load="updateSlamOverlay"
          />
          <video
            v-else-if="slamKey && slamMediaType === 'video'"
            ref="slamVideo"
            class="slam-img"
            :style="slamImgStyle"
            controls
            autoplay
            muted
            :src="cdnBaseUrl + slamKey"
            @loadedmetadata="updateSlamOverlay"
          />
          <div
            class="overlay"
            :style="overlayStyle"
            @mousedown="handleOverlayMouseDown"
            @mousemove="handleOverlayMouseMove"
            @mouseup="handleOverlayMouseUp"
            @mouseleave="arrowData = null"
          >
            <div
              class="circle"
              :style="circleStyle"
            >
              <svg
                class="circle-arrow"
                :style="{
                  position: 'absolute',
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'none'
                }"
              >
                <defs>
                  <marker
                    id="circle-arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="10"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon points="0 0, 10 3.5, 0 7" fill="red" />
                  </marker>
                </defs>
                <line
                  :x1="'50%'"
                  :y1="'50%'"
                  :x2="circleStyle['--arrow-x2']"
                  :y2="circleStyle['--arrow-y2']"
                  stroke="red"
                  stroke-width="2"
                  marker-end="url(#circle-arrowhead)"
                />
              </svg>
            </div>
            <svg
              v-if="arrowData"
              class="arrow"
              :style="{
                position: 'absolute',
                width: '100%',
                height: '100%',
                pointerEvents: 'none'
              }"
            >
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="10"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="red" />
                </marker>
              </defs>
              <line
                :x1="arrowData.startX * 100 + '%'"
                :y1="arrowData.startY * 100 + '%'"
                :x2="arrowData.endX * 100 + '%'"
                :y2="arrowData.endY * 100 + '%'"
                stroke="red"
                stroke-width="2"
                marker-end="url(#arrowhead)"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use './robotController.scss';

.deepseek-live {
  display: flex;
  height: 100vh;
}

.chat-section {
  width: 25%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-color);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);

  h2 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color-1);
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.chat-input-area {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

.stop-button-container {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 10;
  
  .stop-button {
    font-weight: bold;
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.5);
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 0 20px rgba(255, 0, 0, 0.7);
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
}

/* 更新脉动动画 */
@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 50, 50, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 50, 50, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 50, 50, 0);
  }
}

.stop-button {
  animation: pulse-red 1.5s infinite;
}

.viewport-section {
  width: 75%;
  display: flex;
  height: 100%;
}

.viewport {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  overflow: hidden;
}

.viewport-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.slam-img-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slam-img {
  /* 动态样式由JS控制 */
}

.overlay {
  /* 动态样式由JS控制 */
  pointer-events: auto;
}

.circle {
  /* 动态样式由JS控制 */
  position: relative;
}

.circle-arrow {
  /* 动态样式由JS控制 */
}

.code-block {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  margin: 8px 0;
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.45;
  overflow-x: auto;
}

.message {
  margin-bottom: 8px !important;
}

.message + .message-quote + .message {
  margin-top: 24px !important;
}

.recording {
  color: #f56c6c;
  animation: pulse 1.5s infinite;
}

.message-quote {
  display: block;
  margin: 0 20px 20px 60px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-left: 4px solid #ddd;
  border-radius: 4px;
  font-size: 0.9em;
  color: #666;
  white-space: pre-wrap;
  word-break: break-word;
  max-width: 70%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>