.deepseek-live {
  display: flex;
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .chat-section {
    width: 30%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e8e8e8;
  }

  .live-section {
    width: 70%;
    height: 100%;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    max-height: calc(100vh - 300px); // 限制最大高度，留出足够空间给输入框

    .message {
      margin-bottom: 16px;

      &.user .message-content {
        margin-left: auto;
        background-color: #e6f7ff;
      }

      &.assistant .message-content {
        margin-right: auto;
        background-color: #f5f5f5;
      }

      &.system .message-content {
        margin: 0 auto;
        background-color: #fff1f0;
      }

      &.error .message-content {
        background-color: #fff1f0;
        border: 1px solid #ffa39e;
      }

      .message-content {
        max-width: 80%;
        padding: 12px;
        border-radius: 8px;

        .message-text {
          word-break: break-word;
          line-height: 1.5;
        }

        .message-time {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }
  }

  .chat-input-area {
    padding: 16px;
    border-top: 1px solid #e8e8e8;
    background-color: #f9f9f9;

    .input-container {
      position: relative;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      overflow: hidden;

      .input-text-area {
        padding: 12px 12px 24px 12px; // 增加底部内边距，为字符计数器留出空间

        .message-input {
          :deep(.n-input__textarea-el) {
            min-height: 120px; // 增加默认高度，对应约5行文字
            padding: 12px 16px;
            font-size: 14px;
            line-height: 1.6;
            border: none;
            resize: none;
            transition: all 0.3s;
            background-color: transparent;

            &:focus {
              box-shadow: none;
            }
          }

          // 字符计数器样式
          :deep(.n-input__count) {
            position: absolute;
            right: 12px;
            bottom: 8px;
            font-size: 12px;
            color: #999;
          }

          :deep(.n-input__border),
          :deep(.n-input__state-border) {
            display: none;
          }
        }
      }

      .input-toolbar {
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #f0f0f0;
        background-color: #fafafa;

        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 12px;

          .n-button {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;

            &:hover {
              transform: scale(1.05);
            }

            &:active {
              transform: scale(0.95);
            }
          }

          .model-selector {
            width: 150px;
          }
        }

        .toolbar-right {
          .n-button {
            width: 36px;
            height: 36px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;

            &:hover {
              transform: scale(1.05);
            }

            &:active {
              transform: scale(0.95);
            }
          }
        }
      }
    }
  }
}