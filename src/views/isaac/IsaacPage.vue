<script setup>
import { ref, onMounted,onUnmounted } from 'vue'
import isaacData from '@/assets/data/isaac.json'
import goodsData from '@/assets/data/goods.json'
import { throttle } from 'lodash-es'
import { isaacApi } from '@/api/isaac'
import messages from '@/utils/messages'

// 当前选中的标签页
const activeTab = ref('goods')
// 房间列表数据
const rooms = ref([])
// 物品列表数据
const items = ref([])
// 画布上的物品
const canvasItems = ref([])
// 预览区域状态
const isFullscreen = ref(false)

// 处理ESC键退出全屏
const handleKeyDown = (event) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    isFullscreen.value = false
  }
}

// 添加和移除键盘事件监听器
const addKeyboardListener = () => {
  window.addEventListener('keydown', handleKeyDown)
}

const removeKeyboardListener = () => {
  window.removeEventListener('keydown', handleKeyDown)
}

// 当前场景名称
const currentSceneName = ref('')
// 场景计数器
const sceneCounter = ref(1)
// 当前拖动的元素
const draggingItem = ref(null)
// 记录拖动开始时的位置
const dragStartPos = ref({ x: 0, y: 0 })
// 是否正在拖动
const isDragging = ref(false)
// 处理画布内元素拖动开始
const handleCanvasItemDragStart = (item, event) => {
  event.preventDefault()
  draggingItem.value = item
  isDragging.value = true
  const rect = event.target.getBoundingClientRect()
  dragStartPos.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleCanvasItemDrag)
  document.addEventListener('mouseup', handleCanvasItemDragEnd)
}


// 处理物品列表拖拽开始
const handleDragStart = (item, event) => {
  // 设置拖拽数据
  event.dataTransfer.setData('text/plain', JSON.stringify({
    id: item.id,
    name: item.name,
    type: item.type,
    gridSize: item.gridSize || 1,
    direction: 'right', // 默认朝向右边
    rotation: null // 默认无旋转
  }))

  // 设置拖拽效果
  event.dataTransfer.effectAllowed = 'copy'
}

// 处理拖拽悬停
const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'copy'
}

// 处理拖拽放置
const handleDrop = (event) => {
  event.preventDefault()

  const canvas = event.currentTarget
  const canvasRect = canvas.getBoundingClientRect()

  // 计算网格大小
  const gridWidth = canvasRect.width / 10
  const gridHeight = canvasRect.height / 10

  // 计算放置位置对应的网格坐标
  const gridX = Math.floor((event.clientX - canvasRect.left) / gridWidth)
  const gridY = Math.floor((event.clientY - canvasRect.top) / gridHeight)

  try {
    // 获取拖拽的物品数据
    const itemData = JSON.parse(event.dataTransfer.getData('text/plain'))
    const gridSize = itemData.gridSize || 1

    // 检查是否有足够的空间
    if (10 - gridX < gridSize) return

    // 计算物品将要占用的格子
    const occupiedGrids = calculateOccupiedGrids(gridX, gridY, gridSize)

    // 检查位置是否可用
    if (!isGridAvailable(occupiedGrids)) {
      console.warn('放置位置被占用')
      return
    }

    // 创建新的画布物品
    const newItem = {
      id: `canvas-${Date.now()}`,
      name: itemData.name,
      type: 'item',
      gridIndex: gridY * 10 + gridX,
      gridX,
      gridY,
      x: gridX * gridWidth,
      y: gridY * gridHeight,
      gridSize,
      isVertical: false,
      direction: itemData.direction || 'right',
      rotation: itemData.rotation || null
    }

    // 更新网格占用状态
    updateGridOccupancy(newItem.id, occupiedGrids)

    // 添加物品到画布
    canvasItems.value.push(newItem)
  } catch (error) {
    console.error('处理拖拽数据时出错:', error)
  }
}

// 网格占用状态数组（0-99）
const gridOccupancy = ref(new Array(100).fill(null))
// 物品占用的格子映射表 {itemId: [gridIndexes]}
const itemGridMap = ref({})

// 更新网格占用状态
const updateGridOccupancy = (itemId, gridIndexes, isOccupy = true) => {
  // 如果是占用操作
  if (isOccupy) {
    // 更新网格占用状态
    gridIndexes.forEach(index => {
      gridOccupancy.value[index] = itemId
    })
    // 更新物品占用格子映射
    itemGridMap.value[itemId] = gridIndexes
  } else {
    // 如果是释放操作
    const oldIndexes = itemGridMap.value[itemId] || []
    oldIndexes.forEach(index => {
      gridOccupancy.value[index] = null
    })
    delete itemGridMap.value[itemId]
  }
}

// 检查格子是否可用
const isGridAvailable = (gridIndexes, excludeItemId = null) => {
  return gridIndexes.every(index => {
    // 检查是否越界
    if (index < 0 || index >= 100) return false
    // 检查是否被占用（排除当前物品）
    const occupiedBy = gridOccupancy.value[index]
    // 如果格子未被占用，或者被当前物品占用，则可用
    if (!occupiedBy || occupiedBy === excludeItemId) return true
    // 如果格子被其他物品占用，则不可用
    return false
  })
}

// 计算物品占用的格子
const calculateOccupiedGrids = (gridX, gridY, size, isVertical = false) => {
  const occupiedGrids = []
  if (isVertical) {
    // 检查垂直方向是否超出边界
    if (gridY + size > 10) return occupiedGrids
    // 垂直方向占用的格子
    for (let i = 0; i < size; i++) {
      const gridIndex = (gridY + i) * 10 + gridX
      occupiedGrids.push(gridIndex)
    }
  } else {
    // 检查水平方向是否超出边界
    if (gridX + size > 10) return occupiedGrids
    // 水平方向占用的格子
    for (let i = 0; i < size; i++) {
      const gridIndex = gridY * 10 + (gridX + i)
      occupiedGrids.push(gridIndex)
    }
  }
  return occupiedGrids
}

// 使用节流处理拖动更新
const handleCanvasItemDrag = throttle((event) => {
  if (!draggingItem.value || !isDragging.value) return

  const canvas = document.querySelector('.canvas')
  if (!canvas) return

  const canvasRect = canvas.getBoundingClientRect()

  // 计算网格大小
  const gridWidth = canvasRect.width / 10
  const gridHeight = canvasRect.height / 10

  // 计算鼠标相对画布的位置
  const mouseX = event.clientX - canvasRect.left
  const mouseY = event.clientY - canvasRect.top

  // 计算最近的网格位置
  const gridX = Math.floor(mouseX / gridWidth)
  const gridY = Math.floor(mouseY / gridHeight)

  // 获取当前拖动物品的尺寸
  const itemSize = draggingItem.value.gridSize || 1

  // 检查是否有足够的空间
  const remainingSpaceInRow = 10 - gridX
  if (remainingSpaceInRow < itemSize) {
    return
  }

  // 检查目标位置是否被占用
  const isOccupied = canvasItems.value.some(existingItem => {
    if (existingItem.id === draggingItem.value.id) return false

    const existingSize = existingItem.gridSize || 1
    const existingStartX = Math.floor(existingItem.x / gridWidth)
    const existingStartY = Math.floor(existingItem.y / gridHeight)

    // 检查新位置的每个格子是否与现有物品重叠
    for (let i = 0; i < itemSize; i++) {
      const newItemX = gridX + i
      const newItemY = gridY

      for (let j = 0; j < existingSize; j++) {
        const existingX = existingStartX + j
        const existingY = existingStartY

        if (newItemX === existingX && newItemY === existingY) {
          return true
        }
      }
    }
    return false
  })

  if (isOccupied) return

  // 计算网格对齐后的坐标
  const alignedX = gridX * gridWidth
  const alignedY = gridY * gridHeight

  // 确保不超出画布边界
  const x = Math.max(0, Math.min(alignedX, canvasRect.width - gridWidth * itemSize))
  const y = Math.max(0, Math.min(alignedY, canvasRect.height - gridHeight))

  // 更新元素位置和网格信息
  const item = canvasItems.value.find(i => i.id === draggingItem.value.id)
  if (item) {
    item.x = x
    item.y = y
    item.gridX = gridX
    item.gridY = gridY
    item.gridIndex = gridY * 10 + gridX
  }
}, 16) // 约60fps的更新频率

// 处理画布内元素拖动结束
const handleCanvasItemDragEnd = () => {
  if (!isDragging.value) return

  isDragging.value = false
  draggingItem.value = null

  // 移除全局鼠标事件监听
  document.removeEventListener('mousemove', handleCanvasItemDrag)
  document.removeEventListener('mouseup', handleCanvasItemDragEnd)
}

// 初始化数据
// 初始化网格编号
const initGridNumbers = () => {
  const canvas = document.querySelector('.canvas')
  if (!canvas) return

  // 清除现有的网格编号
  const existingNumbers = canvas.querySelectorAll('[data-grid-number]')
  existingNumbers.forEach(el => el.remove())

  // 添加新的网格编号
  for (let i = 0; i < 100; i++) {
    const gridNumber = document.createElement('div')
    gridNumber.setAttribute('data-grid-number', i.toString())
    gridNumber.textContent = i.toString()
    gridNumber.style.left = `${(i % 10) * 10}%`
    gridNumber.style.top = `${Math.floor(i / 10) * 10}%`
    gridNumber.style.color = '#999'
    gridNumber.style.display = 'flex'
    gridNumber.style.alignItems = 'center'
    gridNumber.style.justifyContent = 'center'
    gridNumber.style.width = '10%'
    gridNumber.style.height = '10%'
    gridNumber.style.position = 'absolute'
    gridNumber.style.pointerEvents = 'none'
    gridNumber.style.zIndex = '1'
    gridNumber.style.fontSize = '12px'
    canvas.appendChild(gridNumber)
  }
}

onMounted(() => {
  // 初始化网格编号
  initGridNumbers()
  // 添加键盘事件监听
  addKeyboardListener()
  // 从 isaac.json 中读取房间数据
  rooms.value = Object.keys(isaacData).map(roomName => ({
    id: roomName,
    name: roomName,
    items: isaacData[roomName]
  }))

  // 从 goods.json 加载所有可用物品
  items.value = Object.entries(goodsData).map(([category, items]) => ({
    id: `category-${category}`,
    name: category,
    type: 'category',
    children: items.map(item => ({
      id: `item-${item.id}`,
      name: item.name,
      type: 'item',
      gridIndex: item.gridIndex,
      gridSize: item.size || 1
    }))
  }))
})

// 删除画布中的物品
const toggleItemDirection = (itemId) => {
  const item = canvasItems.value.find(i => i.id === itemId)
  if (!item) return

  // 获取当前占用的格子
  const currentOccupiedGrids = itemGridMap.value[item.id] || []

  // 先释放当前占用的格子
  updateGridOccupancy(item.id, [], false)

  // 计算新的方向
  const newIsVertical = !item.isVertical

  // 计算新的占用格子
  const newOccupiedGrids = calculateOccupiedGrids(
    item.gridX,
    item.gridY,
    item.gridSize,
    newIsVertical
  )

  // 如果计算结果为空数组（表示会超出边界）或位置不可用，则恢复原来的占用状态并返回
  if (newOccupiedGrids.length === 0 || !isGridAvailable(newOccupiedGrids, item.id)) {
    // 恢复原来的格子占用
    updateGridOccupancy(item.id, currentOccupiedGrids)
    return
  }

  // 更新物品方向
  item.isVertical = newIsVertical

  // 更新网格占用状态
  updateGridOccupancy(item.id, newOccupiedGrids)
}

// 修改removeCanvasItem函数
const removeCanvasItem = (itemId) => {
  // 释放物品占用的格子
  updateGridOccupancy(itemId, [], false)

  // 从画布中移除物品
  canvasItems.value = canvasItems.value.filter(item => item.id !== itemId)
}

// 修改loadRoomItems函数
const loadRoomItems = (room) => {
  // 清空当前网格占用状态
  gridOccupancy.value = new Array(100).fill(null)
  itemGridMap.value = {}

  // 更新物品列表
  items.value = room.items.map((item, index) => ({
    id: `${room.id}-${index}`,
    name: item.name,
    type: 'item'
  }))

  // 更新当前场景名称
  currentSceneName.value = room.name

  // 重置画布物品
  canvasItems.value = []

  // 获取画布元素和尺寸信息
  const canvas = document.querySelector('.canvas')
  if (!canvas) return

  const canvasRect = canvas.getBoundingClientRect()
  const gridWidth = canvasRect.width / 10
  const gridHeight = canvasRect.height / 10

  // 将房间物品加载到画布
  room.items.forEach((item, index) => {
    const gridIndex = item.gridIndex ?? index % 100
    const gridX = gridIndex % 10
    const gridY = Math.floor(gridIndex / 10)
    const isVertical = item.isVertical || false
    const gridSize = item.gridSize || 1

    // 计算物品将要占用的格子
    const occupiedGrids = calculateOccupiedGrids(gridX, gridY, gridSize, isVertical)

    // 检查位置是否可用
    if (!isGridAvailable(occupiedGrids)) {
      console.warn(`物品 ${item.name} 的位置发生冲突，跳过加载`)
      return
    }

    const newItem = {
      id: `${room.id}-${index}`,
      name: item.name,
      type: 'item',
      gridIndex,
      gridX,
      gridY,
      x: gridX * gridWidth,
      y: gridY * gridHeight,
      gridSize,
      isVertical,
      direction: item.direction || 'right', // 添加方向属性
      rotation: item.rotation || null // 添加旋转属性
    }

    // 更新网格占用状态
    updateGridOccupancy(newItem.id, occupiedGrids)

    // 添加物品到画布
    canvasItems.value.push(newItem)
  })

  // 更新网格编号样式
  initGridNumbers()
}
// 检查物品是否已在画布中
const isItemInCanvas = (itemName) => {
  return canvasItems.value.some(item => item.name === itemName)
}

// 预览窗口拖拽相关状态
const isDraggingPreview = ref(false)
const previewPosition = ref({ x: window.innerWidth - 320, y: window.innerHeight - 220 })

// 处理预览窗口拖拽开始
const handlePreviewDragStart = (event) => {
  if (isFullscreen.value) return
  isDraggingPreview.value = true
  const rect = event.currentTarget.getBoundingClientRect()
  dragStartPos.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }

  document.addEventListener('mousemove', handlePreviewDrag)
  document.addEventListener('mouseup', handlePreviewDragEnd)
}

// 处理预览窗口拖拽
const handlePreviewDrag = throttle((event) => {
  if (!isDraggingPreview.value) return

  const x = event.clientX - dragStartPos.value.x
  const y = event.clientY - dragStartPos.value.y

  // 确保预览窗口不会被拖出屏幕
  const maxX = window.innerWidth - 300
  const maxY = window.innerHeight - 200

  previewPosition.value = {
    x: Math.max(0, Math.min(x, maxX)),
    y: Math.max(0, Math.min(y, maxY))
  }
}, 16)

// 处理预览窗口拖拽结束
const handlePreviewDragEnd = () => {
  isDraggingPreview.value = false
  document.removeEventListener('mousemove', handlePreviewDrag)
  document.removeEventListener('mouseup', handlePreviewDragEnd)
}

// 组件卸载时移除事件监听
onUnmounted(() => {
  removeKeyboardListener()
})

// 新增场景
const addNewScene = () => {
  // 重置组件状态
  currentSceneName.value = `新场景${sceneCounter.value}`
  sceneCounter.value++
  canvasItems.value = []
  gridOccupancy.value = new Array(100).fill(null)
  itemGridMap.value = {}

  // 重新加载物品列表
  items.value = Object.entries(goodsData).map(([category, items]) => ({
    id: `category-${category}`,
    name: category,
    type: 'category',
    expanded: false,
    children: items.map(item => ({
      id: `item-${item.id}`,
      name: item.name,
      type: 'item',
      gridIndex: item.gridIndex,
      gridSize: item.size || 1
    }))
  }))

  // 重新初始化网格编号
  initGridNumbers()
}

// 更新场景名称
const updateSceneName = (newName) => {
  if (!newName.trim()) {
    messages.warning('场景名称不能为空')
    currentSceneName.value = '未命名场景'
    return
  }
  currentSceneName.value = newName.trim()
}

// 保存并发送画布数据
const saveCanvas = async () => {
  try {
    // 构建保存数据
    const saveData = {
      saving: true,
      title: currentSceneName.value || 'untitled',
      items: canvasItems.value.map(item => {
        // 根据当前物品位置重新计算occupiedGrids
        const occupiedGrids = calculateOccupiedGrids(
          item.gridX,
          item.gridY,
          item.gridSize,
          item.isVertical
        )

        return {
          id: item.id,
          name: item.name,
          gridSize: item.gridSize,
          occupiedGrids,
          direction: item.direction || 'right',
          rotation: item.rotation || null,
          isVertical: item.isVertical
        }
      })
    }

    // 然后发送场景
    await isaacApi.sendScene(saveData)

    messages.success('场景已保存并发送')
  } catch (error) {
    console.error('保存并发送场景失败:', error)
    messages.error('保存并发送场景失败')
  }
}

// 仅发送场景数据
const sendCanvas = async () => {
  try {
    // 构建保存数据
    const saveData = {
      title: currentSceneName.value || 'untitled',
      items: canvasItems.value.map(item => {
        // 根据当前物品位置重新计算occupiedGrids
        const occupiedGrids = calculateOccupiedGrids(
          item.gridX,
          item.gridY,
          item.gridSize,
          item.isVertical
        )

        return {
          id: item.id,
          name: item.name,
          gridSize: item.gridSize,
          occupiedGrids,
          direction: item.direction || 'right',
          rotation: item.rotation || null,
          isVertical: item.isVertical
        }
      })
    }

    await isaacApi.sendScene(saveData)
    messages.success('场景发送成功')
  } catch (error) {
    console.error('发送场景失败:', error)
    messages.error('场景发送失败')
  }
}
</script>

<template>
  <div class="isaac-page">
    <!-- 左侧工具栏 -->
    <div class="sidebar">
      <div class="tabs">
        <div class="tab" :class="{ active: activeTab === 'rooms' }" @click="activeTab = 'rooms'">
          场景
        </div>
        <div class="tab" :class="{ active: activeTab === 'goods' }" @click="activeTab = 'goods'">
          物品
        </div>
      </div>

      <!-- 列表内容区域 -->
      <div class="list-content">
        <!-- 房间列表 -->
        <div v-if="activeTab === 'rooms'" class="list">
          <!-- 新增场景按钮 -->
          <div class="list-item add-scene-button" @click="addNewScene">
            新增场景
          </div>
          <div v-for="room in rooms" :key="room.id" class="list-item" @click="loadRoomItems(room)">
            {{ room.name }}
          </div>
        </div>

        <!-- 物品列表 -->
        <div v-else class="list">
          <div v-for="category in items" :key="category.id" class="category-item">
            <div class="category-header" @click="category.expanded = !category.expanded">
              <span class="category-icon">{{ category.expanded ? '▼' : '▶' }}</span>
              {{ category.name }}
            </div>
            <div v-if="category.expanded" class="category-children">
              <div v-for="item in category.children" :key="item.id" class="list-item"
                :class="{ 'disabled': isItemInCanvas(item.name) }" draggable="true"
                @dragstart="(e) => handleDragStart(item, e)"
                @click="(e) => isItemInCanvas(item.name) && e.preventDefault()">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧画布区域 -->
    <div class="canvas-container">
      <!-- 标题区域 -->
      <div class="canvas-header">
        <div class="scene-title">
          <div class="scene-title-wrapper">
            <svg xmlns="http://www.w3.org/2000/svg" class="edit-icon" viewBox="0 0 576 512"><path d="M402.6 83.2l90.2 90.2c3.8 3.8 3.8 10 0 13.8L274.4 405.6l-92.8 10.3c-12.4 1.4-22.9-9.1-21.5-21.5l10.3-92.8L388.8 83.2c3.8-3.8 10-3.8 13.8 0zm162-22.9l-48.8-48.8c-15.2-15.2-39.9-15.2-55.2 0l-35.4 35.4c-3.8 3.8-3.8 10 0 13.8l90.2 90.2c3.8 3.8 10 3.8 13.8 0l35.4-35.4c15.2-15.3 15.2-40 0-55.2zM384 346.2V448H64V128h229.8c3.2 0 6.2-1.3 8.5-3.5l40-40c7.6-7.6 2.2-20.5-8.5-20.5H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V306.2c0-10.7-12.9-16-20.5-8.5l-40 40c-2.2 2.3-3.5 5.3-3.5 8.5z" fill="currentColor"></path></svg>
            <input v-model="currentSceneName" @change="updateSceneName($event.target.value)" placeholder="请输入场景名称"
              class="scene-title-input" maxlength="10" />
          </div>
        </div>
        <div class="button-group">
          <button class="send-button" @click="sendCanvas">仅发送</button>
                    <button class="save-button" @click="saveCanvas">保存并发送</button>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas" @drop="handleDrop" @dragover="handleDragOver">
        <div v-for="item in canvasItems" :key="item.id" class="canvas-item" :style="{
          left: item.x + 'px',
          top: item.y + 'px',
          width: item.isVertical ? '10%' : (item.gridSize || 1) * 10 + '%',
          height: item.isVertical ? (item.gridSize || 1) * 10 + '%' : '10%',
          // 移除transform旋转，保持原始显示状态
        }" @mousedown="handleCanvasItemDragStart(item, $event)">
          {{ item.name }}
          <div class="direction-controls">
            <div class="direction-icon top" :class="{ current: item.direction === 'up' }" @click.stop="item.direction = 'up'">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512">
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M112 244l144-144l144 144"></path>
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M256 120v292"></path>
              </svg>
            </div>
            <div class="direction-icon right" :class="{ current: item.direction === 'right' }" @click.stop="item.direction = 'right'">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" style="transform: rotate(90deg);">
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M112 244l144-144l144 144"></path>
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M256 120v292"></path>
              </svg>
            </div>
            <div class="direction-icon bottom" :class="{ current: item.direction === 'down' }" @click.stop="item.direction = 'down'">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" style="transform: rotate(180deg);">
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M112 244l144-144l144 144"></path>
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M256 120v292"></path>
              </svg>
            </div>
            <div class="direction-icon left" :class="{ current: item.direction === 'left' }" @click.stop="item.direction = 'left'">
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" style="transform: rotate(270deg);">
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M112 244l144-144l144 144"></path>
                <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="M256 120v292"></path>
              </svg>
            </div>
          </div>
          <!-- 删除图标 - 右上角 -->
          <div class="delete-icon" @click.stop="removeCanvasItem(item.id)">
            <svg class="delete-svg" data-v-bc68a08a="" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path data-v-bc68a08a="" d="M3 6h18"></path><path data-v-bc68a08a="" d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path data-v-bc68a08a="" d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
          </div>
          <!-- X旋转图标 - 左上角 -->
          <div class="rotate-x-icon" :class="{ current: item.rotation === 'rotate_x_90' }" @click.stop="item.rotation = item.rotation === 'rotate_x_90' ? null : 'rotate_x_90'">
            <span class="rotate-text">X</span>
          </div>
          <!-- Y旋转图标 - 左下角 -->
          <div class="rotate-y-icon" :class="{ current: item.rotation === 'rotate_y_90' }" @click.stop="item.rotation = item.rotation === 'rotate_y_90' ? null : 'rotate_y_90'">
            <span class="rotate-text">Y</span>
          </div>
          <!-- Z旋转图标 - 右下角 (替换原来的rotate-icon) -->
          <div class="rotate-z-icon" :class="{ current: item.rotation === 'rotate_z_90' }" @click.stop="item.rotation = item.rotation === 'rotate_z_90' ? null : 'rotate_z_90'">
            <span class="rotate-text">Z</span>
          </div>
      </div>

        <!-- 预览区域 -->
        <div class="preview-area" :class="{ 'fullscreen': isFullscreen }" :style="{
          left: isFullscreen ? 0 : previewPosition.x + 'px',
          top: isFullscreen ? 0 : previewPosition.y + 'px',
          right: isFullscreen ? 0 : 'auto',
          bottom: isFullscreen ? 0 : 'auto'
        }">
          <div class="preview-header" @mousedown="handlePreviewDragStart">
            <div class="preview-icon" @click.stop="isFullscreen = !isFullscreen">
              <svg v-if="!isFullscreen" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 24 24">
                <g fill="none">
                  <path
                    d="M6 6.75A.75.75 0 0 1 6.75 6h2.5a.75.75 0 0 1 0 1.5h-.69l1.72 1.72a.75.75 0 1 1-1.06 1.06L7.5 8.56v.691a.75.75 0 1 1-1.5 0V6.75zm11.25 11.251a.75.75 0 0 0 .75-.75V14.75a.75.75 0 0 0-1.5 0v.69l-1.72-1.72a.75.75 0 0 0-1.06 1.06l1.72 1.721h-.69a.75.75 0 0 0 0 1.5h2.5zm.53-11.78c.142.14.22.33.22.53v2.5a.75.75 0 0 1-1.5 0v-.69l-1.72 1.72a.75.75 0 1 1-1.06-1.061l1.72-1.72h-.69a.75.75 0 0 1 0-1.5h2.5a.75.75 0 0 1 .53.22zM6 17.25a.75.75 0 0 0 .75.75h2.501a.75.75 0 0 0 0-1.5h-.69l1.72-1.72A.75.75 0 0 0 9.22 13.72L7.5 15.44v-.69a.75.75 0 0 0-1.5 0v2.5zM3 5.25A2.25 2.25 0 0 1 5.25 3h13.5A2.25 2.25 0 0 1 21 5.25v13.5A2.25 2.25 0 0 1 18.75 21H5.25A2.25 2.25 0 0 1 3 18.75V5.25zm2.25-.75a.75.75 0 0 0-.75.75v13.5c0 .414.336.75.75.75h13.5a.75.75 0 0 0 .75-.75V5.25a.75.75 0 0 0-.75-.75H5.25z"
                  fill="currentColor"></path>
                </g>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                viewBox="0 0 32 32">
                <path d="M13 17H7a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2zm-6 8v-6h6v6z"
                  fill="currentColor"></path>
                <path d="M19 21v2h6a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2H11a2 2 0 0 0-2 2v6h2V7h14v14" fill="currentColor">
                </path>
              </svg>
            </div>
          </div>
          <iframe src="https://uat-open.qj-robots.com/isaac_live_resources.html" frameborder="0" class="preview-iframe"></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import './isaac.scss';
</style>