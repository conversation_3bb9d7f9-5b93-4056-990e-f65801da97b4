这是一个提示词文件，你可以忽略<-- done -->标记之后的内容，否则你需要帮我完成任务，并且你在完成开发后增加<-- done -->标记。
现在在src/views/tasks/TaskResultPage.vue中，我需要实现一个任务结果页面，这个页面需要展示任务的执行结果。
任务参数从URL的task_id参数获取，然后调用接口：
const requestOptions = {
  method: "GET",
  redirect: "follow"
};

fetch("https://uat-open.qj-robots.com/open-api/open-apis/app/perception/result?task_id=PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9", requestOptions)
  .then((response) => response.text())
  .then((result) => console.log(result))
  .catch((error) => console.error(error));
  接口的返回结构为：
  {
    "code": 0,
    "data": {
        "taskId": "PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9",
        "taskStatus": "DONE",
        "taskResult": {
            "angeleImage": "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/angle_image.png",
            "angles": [
                {
                    "angle": 90.0,
                    "anglesPoint": [
                        [
                            131,
                            126
                        ],
                        [
                            230,
                            126
                        ],
                        [
                            230,
                            370
                        ],
                        [
                            131,
                            370
                        ]
                    ]
                },
                {
                    "angle": 0.0,
                    "anglesPoint": [
                        [
                            320,
                            368
                        ],
                        [
                            320,
                            125
                        ],
                        [
                            413,
                            125
                        ],
                        [
                            413,
                            368
                        ]
                    ]
                },
                {
                    "angle": 90.0,
                    "anglesPoint": [
                        [
                            228,
                            127
                        ],
                        [
                            320,
                            127
                        ],
                        [
                            320,
                            369
                        ],
                        [
                            228,
                            369
                        ]
                    ]
                },
                {
                    "angle": 90.0,
                    "anglesPoint": [
                        [
                            594,
                            124
                        ],
                        [
                            689,
                            124
                        ],
                        [
                            689,
                            370
                        ],
                        [
                            594,
                            370
                        ]
                    ]
                },
                {
                    "angle": 90.0,
                    "anglesPoint": [
                        [
                            685,
                            121
                        ],
                        [
                            783,
                            121
                        ],
                        [
                            783,
                            368
                        ],
                        [
                            685,
                            368
                        ]
                    ]
                },
                {
                    "angle": 90.0,
                    "anglesPoint": [
                        [
                            412,
                            125
                        ],
                        [
                            504,
                            125
                        ],
                        [
                            504,
                            369
                        ],
                        [
                            412,
                            369
                        ]
                    ]
                },
                {
                    "angle": 0.0,
                    "anglesPoint": [
                        [
                            503,
                            368
                        ],
                        [
                            503,
                            125
                        ],
                        [
                            595,
                            125
                        ],
                        [
                            595,
                            368
                        ]
                    ]
                }
            ],
            "angles3D": [],
            "answers": [
                {
                    "有无盖子": "有盖子",
                    "颜色": "红色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无把手": "没有把手",
                    "有无图案": "有图案"
                },
                {
                    "开关状态": "打开",
                    "颜色": "红色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无拉环": "有拉环",
                    "有无图案": "有图案"
                },
                {
                    "开关状态": "关闭",
                    "颜色": "黑色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无拉环": "有拉环",
                    "有无图案": "有图案"
                },
                {
                    "开关状态": "打开",
                    "颜色": "橙色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无拉环": "有拉环",
                    "有无图案": "有图案"
                },
                {
                    "有无盖子": "有盖子",
                    "颜色": "红色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无把手": "没有",
                    "开合状态": "关",
                    "有无图案": "有图案"
                },
                {
                    "开关状态": "打开",
                    "颜色": "绿色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无拉环": "有拉环",
                    "有无图案": "有图案"
                },
                {
                    "开关状态": "关闭",
                    "颜色": "蓝色",
                    "里面有无物体": "看不出来",
                    "材质": "金属",
                    "有无拉环": "有拉环",
                    "有无图案": "有图案"
                }
            ],
            "boxes": [
                [
                    131.70354,
                    124.00318,
                    231.14961,
                    373.15262
                ],
                [
                    320.94662,
                    125.47907,
                    415.13785,
                    370.72116
                ],
                [
                    228.0314,
                    125.56999,
                    321.96786,
                    371.1144
                ],
                [
                    594.09753,
                    122.4115,
                    690.9055,
                    371.2103
                ],
                [
                    685.84656,
                    119.98538,
                    784.77246,
                    369.62576
                ],
                [
                    412.23596,
                    125.6195,
                    505.82126,
                    370.376
                ],
                [
                    503.20007,
                    125.07891,
                    597.22266,
                    370.82217
                ]
            ],
            "croppedImagesListAngle": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/000.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/001.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/002.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/003.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/004.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/005.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_angle/006.png"
            ],
            "croppedImagesListBbox": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/000.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/001.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/002.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/003.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/004.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/005.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_bbox/006.png"
            ],
            "croppedImagesListGrasp": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/000.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/001.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/002.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/003.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/004.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/005.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_grasp/006.png"
            ],
            "croppedImagesListPoint": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/000.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/001.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/002.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/003.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/004.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/005.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_point/006.png"
            ],
            "croppedImagesListSegment": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/000.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/001.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/002.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/003.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/004.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/005.png",
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/cropped_images_list_segment/006.png"
            ],
            "graspImage": "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/grasp_image.png",
            "grasps": [
                {
                    "graspAngle": 90.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            131.0,
                            126.0
                        ],
                        [
                            230.0,
                            126.0
                        ],
                        [
                            230.0,
                            370.0
                        ],
                        [
                            131.0,
                            370.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 0.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            320.0,
                            368.0
                        ],
                        [
                            320.0,
                            125.0
                        ],
                        [
                            413.0,
                            125.0
                        ],
                        [
                            413.0,
                            368.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 90.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            228.0,
                            127.0
                        ],
                        [
                            320.0,
                            127.0
                        ],
                        [
                            320.0,
                            369.0
                        ],
                        [
                            228.0,
                            369.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 90.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            594.0,
                            124.0
                        ],
                        [
                            689.0,
                            124.0
                        ],
                        [
                            689.0,
                            370.0
                        ],
                        [
                            594.0,
                            370.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 90.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            685.0,
                            121.0
                        ],
                        [
                            783.0,
                            121.0
                        ],
                        [
                            783.0,
                            368.0
                        ],
                        [
                            685.0,
                            368.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 90.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            412.0,
                            125.0
                        ],
                        [
                            504.0,
                            125.0
                        ],
                        [
                            504.0,
                            369.0
                        ],
                        [
                            412.0,
                            369.0
                        ]
                    ],
                    "graspWidth": null
                },
                {
                    "graspAngle": 0.0,
                    "graspDepth": null,
                    "graspHeight": null,
                    "graspPoint": [
                        [
                            503.0,
                            368.0
                        ],
                        [
                            503.0,
                            125.0
                        ],
                        [
                            595.0,
                            125.0
                        ],
                        [
                            595.0,
                            368.0
                        ]
                    ],
                    "graspWidth": null
                }
            ],
            "labels": [
                "红色易拉罐",
                "红色易拉罐",
                "红色易拉罐",
                "红色易拉罐",
                "红色易拉罐",
                "红色易拉罐",
                "红色易拉罐"
            ],
            "maskData": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/mask_data.json"
            ],
            "maskImage": [
                "https://obs-cdn-v1.qj-robots.com/tmp/perception/task/PT_Y6QTPGPAEZ7W4MBZEDWUZDZJ6FNY9/mask_image.png"
            ],
            "points": [],
            "questions": [
                "有几个?",
                "有几个?",
                "有几个?",
                "有几个?",
                "有几个?",
                "有几个?",
                "有几个?"
            ],
            "scores": [
                0.675439,
                0.55085427,
                0.5290866,
                0.5016186,
                0.41413304,
                0.3287131,
                0.3273055
            ]
        }
    },
    "message": "OK"
}
需要你帮我解析json并完整渲染出来。