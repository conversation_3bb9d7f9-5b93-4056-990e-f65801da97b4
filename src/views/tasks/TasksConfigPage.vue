<template>
  <div class="tasks-config-page">
    <div class="filters">
      <!-- 添加流程名称筛选输入框 -->
      <n-input v-model:value="searchQuery" placeholder="输入流程名称进行筛选" class="search-input">
        <template #prefix>
          <n-icon><Search /></n-icon>
        </template>
      </n-input>
      
      <!-- 添加状态筛选选择器 -->
      <n-select v-model:value="statusFilter" :options="statusOptions" class="status-select" />
    </div>

    <div v-for="(group, groupName, index) in filteredGroupedWorkflows" :key="groupName">
      <!-- 在每个组之前添加分割线，但不在第一个组之前添加 -->
      <n-divider v-if="index > 0" />
      <div class="workflow-group">
        <div class="group-header">
          <n-h2>{{ groupName }}</n-h2>
          <n-button type="primary" circle @click="openNewWorkflowDialog(groupName)">
            <template #icon>
              <n-icon><Add /></n-icon>
            </template>
          </n-button>
        </div>
        <n-grid :x-gap="16" :y-gap="16" :cols="4">
          <n-gi v-for="workflow in group" :key="workflow.id">
            <n-card class="workflow-card">
              <n-h3>{{ workflow.name }}</n-h3>
              <n-p>{{ workflow.description }}</n-p>
              <div class="card-footer">
                <n-switch v-model:value="workflow.enabled" @update:value="toggleWorkflow(workflow)" />
                <div>
                  <n-button text @click="editWorkflow(workflow)">
                    <template #icon>
                      <n-icon><Create /></n-icon>
                    </template>
                  </n-button>
                  <n-button text @click="deleteWorkflow(workflow)">
                    <template #icon>
                      <n-icon><TrashBin /></n-icon>
                    </template>
                  </n-button>
                </div>
              </div>
            </n-card>
          </n-gi>
        </n-grid>
      </div>
    </div>

    <n-button @click="openNewWorkflowDialog">新增流程</n-button>
  </div>
</template>

<script setup>
import { h, ref, computed, onMounted } from 'vue'
import { useMessage, useDialog, useModal } from 'naive-ui'
import WorkflowEditor from './WorkflowEditor.vue'
import { Add, Create, TrashBin, Search } from '@vicons/ionicons5'
import { useWorkflowStore } from '@/stores/workflowStore'
import {
  NH1, NH2, NH3, NP, NButton, NSwitch, NCard, NGrid, NGi, NSpace, NIcon, NDivider, NInput, NSelect
} from 'naive-ui'

const message = useMessage()
const dialog = useDialog()
const modal = useModal()
const workflowStore = useWorkflowStore()

const currentWorkflow = ref(null)
const currentGroup = ref('')
const searchQuery = ref('')
const statusFilter = ref('all')

const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '用状态', value: 'enabled' },
  { label: '停用状态', value: 'disabled' }
]

// 直接使用 mainStore 中的 workflows
const workflows = computed(() => workflowStore.workflows)

// 在组件挂载时获取 workflows 数据
onMounted(() => {
    workflowStore.fetchWorkflows()
})

// 筛选后的工作流
const filteredWorkflows = computed(() => {
  return workflows.value.filter(workflow => {
    const nameMatch = !searchQuery.value || workflow.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const statusMatch = statusFilter.value === 'all' || 
      (statusFilter.value === 'enabled' && workflow.enabled) ||
      (statusFilter.value === 'disabled' && !workflow.enabled)
    return nameMatch && statusMatch
  })
})

// 将筛选后的工作流按组分类
const filteredGroupedWorkflows = computed(() => {
  const groups = {}
  filteredWorkflows.value.forEach(workflow => {
    if (!groups[workflow.group]) {
      groups[workflow.group] = []
    }
    groups[workflow.group].push(workflow)
  })
  return groups
})

const openNewWorkflowDialog = (group) => {
  currentWorkflow.value = null
  currentGroup.value = group
  const m = modal.create({
    title: '新增流程',
    content: () => h(WorkflowEditor, {
      ref: (el) => { workflowEditorRef.value = el },
      workflow: currentWorkflow.value,
    }),
    maskClosable: false,
    preset: 'card',
    closeOnEsc: false,
    style: {
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw'
    },
    onEsc: () => handleCloseModal(m),
    onClose: () => handleCloseModal(m)
  })
}

const editWorkflow = (workflow) => {
  currentWorkflow.value = { ...workflow }
  const m = modal.create({
    title: '编辑流程',
    content: () => h(WorkflowEditor, {
      ref: (el) => { workflowEditorRef.value = el },
      workflow: currentWorkflow.value,
      onSave: saveWorkflow,
      onClose: () => handleCloseModal(m)
    }),
    maskClosable: false,
    preset: 'card',
    style: {
      width: '100vw',
      height: '100vh',
      maxWidth: '100vw'
    },
    onEsc: () => handleCloseModal(m),
    onClose: () => handleCloseModal(m)
  })
}

const deleteWorkflow = (workflow) => {
  dialog.warning({
    title: '警告',
    content: '确定要删除这个流程吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      workflowStore.deleteWorkflow(workflow.id)
      message.success('删除成功')
    },
    onNegativeClick: () => {
      message.info('已取消删除')
    }
  })
}

const toggleWorkflow = (workflow) => {
  workflowStore.updateWorkflow({ ...workflow, enabled: !workflow.enabled })
  message.success(`流程已${workflow.enabled ? '启用' : '停用'}`)
}


const closeDialog = () => {
  dialogVisible.value = false
  currentWorkflow.value = null
  currentGroup.value = ''
}

const saveWorkflow = async (workflow) => {
  if (currentWorkflow.value) {
    await workflowStore.updateWorkflow(workflow)
  } else {
    await workflowStore.addWorkflow({ ...workflow, group: currentGroup.value })
  }
  message.success('保存成功')
  modal.destroyAll()
}

const workflowEditorRef = ref(null)

const handleEditorClose = async () => {
  if (workflowEditorRef.value) {
    const canClose = await workflowEditorRef.value.close()
    if (canClose) {
      dialogVisible.value = false
    }
  }
}

const handleAfterLeave = () => {
  currentWorkflow.value = null
  currentGroup.value = ''
}

const handleMaskClick = () => {
  handleEditorClose()
}

const handleCloseModal = async (modalInstance) => {
  if (workflowEditorRef.value) {
    const canClose = await workflowEditorRef.value.close()
    if (canClose) {
      modalInstance.destroy()
    }
  } else {
    modalInstance.destroy()
  }
}
</script>

<style scoped>
.tasks-config-page {
  padding: 20px;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.search-input {
  max-width: 300px;
}

.status-select {
  width: 120px;
}

.workflow-group {
  margin-bottom: 30px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.workflow-card {
  height: 100%;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

:deep(.n-modal-body-wrapper) {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 108px); /* 减去标题和页脚的高度 */
}

:deep(.n-modal-body) {
  flex: 1;
  overflow: hidden;
  padding: 0;
}
</style>
