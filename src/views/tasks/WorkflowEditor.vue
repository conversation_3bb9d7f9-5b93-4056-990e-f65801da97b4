<template>
  <div class="workflow-editor">
    <!-- 顶部 Tab 导航 -->
    <div class="tab-nav-container">
      <ul class="tab-nav">
        <li 
          v-for="tab in tabs" 
          :key="tab.value" 
          :class="{ active: currentTab === tab.value }"
          @click="currentTab = tab.value"
        >
          {{ tab.label }}
        </li>
      </ul>
      <div class="action-buttons">
        <n-button v-if="currentTab === 'form'" @click="previewForm">预览</n-button>
        <n-button v-if="['form', 'process'].includes(currentTab)" type="primary" @click="saveForm">保存</n-button>
      </div>
    </div>

    <!-- Tab 内容 -->
    <div class="tab-content">
      <!-- 表单设计 -->
      <div v-if="currentTab === 'form'" class="form-designer tab-pane">
        <div class="component-list">
          <h3>基础组件</h3>
          <div class="component-group">
            <n-button v-for="component in basicComponents" :key="component.type" 
                      draggable="true" @dragstart="dragStart($event, component)"
                      @click="addComponent(component)"
                      class="component-button">
              <template #icon>
                <n-icon><component :is="component.icon" /></n-icon>
              </template>
              {{ component.label }}
            </n-button>
          </div>
          <h3>高级组件</h3>
          <div class="component-group">
            <n-button v-for="component in advancedComponents" :key="component.type" 
                      draggable="true" @dragstart="dragStart($event, component)"
                      @click="addComponent(component)"
                      class="component-button">
              <template #icon>
                <n-icon><component :is="component.icon" /></n-icon>
              </template>
              {{ component.label }}
            </n-button>
          </div>
        </div>
        <div class="form-canvas" @dragover.prevent @drop="drop($event)">
          <!-- 表单画布 -->
          <h3>表单画布</h3>
          <div v-for="(field, index) in formFields" :key="index" 
               :class="['form-field', { 'selected': field.selected }]"
               :style="{ width: field.props.width }"
               @click="selectField(field)">
            <div class="field-content">
              <div class="field-label">
                <span v-if="field.props.required" class="required-mark">*</span>
                {{ field.label }} <!-- 这里使用 field.label，它会随着属性设置中的更改而更新 -->
                <span v-if="field.description" class="field-description">{{ field.description }}</span>
              </div>
              <div class="field-preview">
                <component :is="getComponentByType(field.type)" v-bind="field.props" disabled />
              </div>
            </div>
            <div class="field-actions">
              <n-button quaternary circle size="small" @click.stop="copyField(field)">
                <template #icon>
                  <n-icon><Copy /></n-icon>
                </template>
              </n-button>
              <n-button quaternary circle size="small" @click.stop="deleteField(index)">
                <template #icon>
                  <n-icon><Delete /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
        <div class="property-panel">
          <!-- 控件属性 -->
          <h3>字段属性</h3>
          <n-form v-if="selectedField" label-placement="top">
            <!-- 通用属性 -->
            <n-form-item label="字段标题">
              <n-input v-model:value="selectedField.label" />
            </n-form-item>
            <n-form-item label="描述信息">
              <n-input v-model:value="selectedField.description" />
            </n-form-item>
            <n-form-item label="提示文字">
              <n-input v-model:value="selectedField.props.placeholder" />
            </n-form-item>
            <n-form-item label="校验">
              <n-space vertical align="start">
                <n-checkbox v-model:checked="selectedField.props.required">必需</n-checkbox>
                <n-checkbox v-model:checked="selectedField.props.unique">唯一</n-checkbox>
              </n-space>
            </n-form-item>
            <n-form-item label="字段权限">
              <n-select v-model:value="selectedField.props.permission" :options="permissionOptions" />
            </n-form-item>
            <n-form-item label="字段宽度">
              <n-select v-model:value="selectedField.props.width" :options="widthOptions" />
            </n-form-item>
            
            <!-- 特定属性 -->
            <template v-if="selectedField.type === 'input'">
              <n-form-item label="格式">
                <n-select v-model:value="selectedField.props.format" :options="inputFormatOptions" />
              </n-form-item>
              <n-form-item label="最大文本长度">
                <n-input-number v-model:value="selectedField.props.maxLength" />
              </n-form-item>
            </template>
            
            <template v-if="selectedField.type === 'textarea'">
              <n-form-item label="最大文本长度">
                <n-input-number v-model:value="selectedField.props.maxLength" />
              </n-form-item>
            </template>
            
            <template v-if="selectedField.type === 'number'">
              <n-form-item label="格式">
                <n-select v-model:value="selectedField.props.format" :options="numberFormatOptions" />
              </n-form-item>
              <n-form-item label="保留小数位数">
                <n-input-number v-model:value="selectedField.props.precision" />
              </n-form-item>
              <n-form-item label="千分位">
                <n-checkbox v-model:checked="selectedField.props.thousandSeparator" />
              </n-form-item>
            </template>
            
            <template v-if="selectedField.type === 'datetime'">
              <n-form-item label="类型">
                <n-select v-model:value="selectedField.props.type" :options="datetimeTypeOptions" />
              </n-form-item>
            </template>
            
            <template v-if="selectedField.type === 'radio'">
              <n-form-item label="选项">
                <n-dynamic-input v-model:value="selectedField.props.options" :on-create="onCreateOption">
                  <template #default="{ value }">
                    <n-input v-model:value="value.label" placeholder="选项文本" />
                  </template>
                </n-dynamic-input>
              </n-form-item>
              <n-form-item label="选项排列方式">
                <n-radio-group v-model:value="selectedField.props.layout">
                  <n-radio-button value="vertical">纵向</n-radio-button>
                  <n-radio-button value="horizontal">横向</n-radio-button>
                </n-radio-group>
              </n-form-item>
            </template>
          </n-form>
        </div>
      </div>

      <!-- 其他 Tab 内容 -->
      <div v-else-if="currentTab === 'process'" class="tab-pane">流程设计内容</div>
      <div v-else-if="currentTab === 'extension'" class="tab-pane">扩展功能内容</div>
      <div v-else-if="currentTab === 'publish'" class="tab-pane">流程发布内容</div>
      <div v-else-if="currentTab === 'data'" class="tab-pane">数据管理内容</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
import { NInput, NButton, useDialog, NIcon, NDatePicker, NRadioGroup, NCheckboxGroup, NSelect, NSwitch, NDivider, NTabs, NUpload, NInputNumber, NCheckbox, NRadioButton, NDynamicInput } from 'naive-ui'
import { useMessage } from 'naive-ui'

// Tabler Icons
import { Calendar, Select, Selector, User, Users,Trash as Delete } from '@vicons/tabler'

// Material Icons
import { 
  AbcOutlined, 
  AccountTreeOutlined, 
  AccountTreeSharp, 
  BackupTableFilled, 
  AttachFileRound, 
  GpsNotFixedOutlined, 
  DrawFilled, 
  SmartButtonFilled ,
  ContentCopyFilled as Copy
} from '@vicons/material'

// Carbon Icons
import { 
  PageNumber, 
  RadioButtonChecked, 
  CheckboxChecked, 
  Split, 
  Image, 
  RegionAnalysisVolume, 
  Table, 
  Magnify, 
  Data1, 
  Barcode, 
  ApplicationMobile, 
  CdCreateExchange 
} from '@vicons/carbon'

const props = defineProps({
  workflow: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save', 'cancel', 'close'])

const dialog = useDialog()
const message = useMessage()

const tabs = [
  { label: '表单设计', value: 'form' },
  { label: '流程设计', value: 'process' },
  { label: '扩展功能', value: 'extension' },
  { label: '流程发布', value: 'publish' },
  { label: '数据管理', value: 'data' }
]

const currentTab = ref('form')
const formFields = ref([])
const selectedField = ref(null)
const isModified = ref(false)
const isClosing = ref(false)
const keyupListener = ref(null)

const basicComponents = [
  { type: 'input', label: '单行文本', icon: AbcOutlined },
  { type: 'textarea', label: '多行文本', icon: AbcOutlined },
  { type: 'number', label: '数字', icon: PageNumber },
  { type: 'datetime', label: '日期时间', icon: Calendar },
  { type: 'radio', label: '单选按钮组', icon: RadioButtonChecked },
  { type: 'checkbox', label: '复选框组', icon: CheckboxChecked },
  { type: 'select', label: '下拉框', icon: Select },
  { type: 'multiSelect', label: '下拉复选框', icon: Selector },
  { type: 'memberSelect', label: '成员单选', icon: User },
  { type: 'memberMultiSelect', label: '成员多选', icon: Users },
  { type: 'departmentSelect', label: '部门单选', icon: AccountTreeOutlined },
  { type: 'departmentMultiSelect', label: '部门多选', icon: AccountTreeSharp },
  { type: 'divider', label: '分割线', icon: Split },
  { type: 'tabs', label: '多标签页', icon: BackupTableFilled },
]

const advancedComponents = [
  { type: 'image', label: '图片', icon: Image },
  { type: 'attachment', label: '附件', icon: AttachFileRound },
  { type: 'address', label: '地址', icon: RegionAnalysisVolume },
  { type: 'location', label: '定位', icon: GpsNotFixedOutlined },
  { type: 'subform', label: '子表单', icon: Table },
  { type: 'query', label: '查询', icon: Magnify },
  { type: 'dataSelect', label: '选择数据', icon: Data1 },
  { type: 'signature', label: '手写签名', icon: DrawFilled },
  { type: 'serialNumber', label: '流水号', icon: Barcode },
  { type: 'phone', label: '手机号', icon: ApplicationMobile },
  { type: 'ocr', label: '文字识别', icon: CdCreateExchange },
  { type: 'button', label: '按钮', icon: SmartButtonFilled },
]

const permissionOptions = [
  { label: '可见', value: 'visible' },
  { label: '可编辑', value: 'editable' }
]

const widthOptions = [
  { label: '1/4', value: '25%' },
  { label: '1/3', value: '33.33%' },
  { label: '1/2', value: '50%' },
  { label: '2/3', value: '66.67%' },
  { label: '3/4', value: '75%' },
  { label: '整行', value: '100%' }
]

const inputFormatOptions = [
  { label: '无', value: 'none' },
  { label: '手机号码', value: 'mobile' },
  { label: '电话号码', value: 'phone' },
  { label: '邮政编码', value: 'postcode' },
  { label: '身份证号码', value: 'idcard' },
  { label: '邮箱地址', value: 'email' }
]

const numberFormatOptions = [
  { label: '数值', value: 'number' },
  { label: '百分比', value: 'percent' }
]

const datetimeTypeOptions = [
  { label: '月', value: 'month' },
  { label: '年月日', value: 'date' },
  { label: '年月日时分', value: 'datetime' },
  { label: '年月日时分秒', value: 'datetimesecond' }
]

const onCreateOption = () => {
  return {
    label: '',
    value: Date.now().toString()
  }
}

// 监听修改
watch(formFields, () => {
  isModified.value = true
}, { deep: true })

// 拖拽开始
const dragStart = (event, component) => {
  event.dataTransfer.setData('text/plain', JSON.stringify(component))
}

// 放置组件
const drop = (event) => {
  try {
    const componentData = JSON.parse(event.dataTransfer.getData('text/plain'))
    addComponent(componentData) // addComponent 方法现在会自动选中新添加的组件
  } catch (error) {
    console.error('Error dropping component:', error)
  }
}

// 选择字段
const selectField = (field) => {
  try {
    selectedField.value = field
    // 重置所有字段的背景色
    formFields.value.forEach(f => {
      if (f.id === field.id) {
        f.selected = true
      } else {
        f.selected = false
      }
    })
  } catch (error) {
    console.error('Error selecting field:', error)
  }
}

// 预览表单
const previewForm = () => {
  // 实现预览逻辑
  console.log('预览表单')
}

// 保存表单或流程
const saveForm = () => {
  try {
    // 实现保存逻辑
    if (currentTab.value === 'form') {
      // 保存表单逻辑
      console.log('保存表单', formFields.value)
    } else if (currentTab.value === 'process') {
      // 保存程逻辑
      console.log('保存流程')
    }
    isModified.value = false
    emit('save', { tab: currentTab.value, data: formFields.value })
  } catch (error) {
    console.error('Error saving form:', error)
    throw error // 重新抛出错，让调用者知道保存失败
  }
}

// 显示保存提示
const showSavePrompt = () => {
  return new Promise((resolve) => {
    dialog.warning({
      title: '警告',
      content: '您有未保存的更改，是否保存？我是弹窗内处理。',
      positiveText: '保存',
      negativeText: '放弃',
      closable: false,
      maskClosable: false,
      onPositiveClick: () => {
        saveForm()
        resolve(true)
      },
      onNegativeClick: () => {
        isModified.value = false
        resolve(false)
      },
      onClose: () => {
        resolve(null)
      }
    })
  })
}

// 修改 closeEditor 方法
const closeEditor = async () => {
  if (isClosing.value) return false
  isClosing.value = true

  try {
    if (isModified.value) {
      const result = await showSavePrompt()
      if (result === null) {
        // 用户取消了操作
        return false
      }
      // 如果用户选择保存，result 为 true；如果选择不保存，result 为 false
      if (result) {
        try {
          await saveForm()
          message.success('保存成功')
        } catch (error) {
          message.error('保存失败')
          return false
        }
      }
    }
    removeKeyupListener()
    emit('close', true) // 发送一个参数表示可以直接关闭
    return true
  } finally {
    isClosing.value = false
  }
}

// 修改 handleKeyUp 方法
const handleKeyUp = async (event) => {
  if (event.key === 'Escape' && !isClosing.value) {
    event.preventDefault() // 阻止默认行为
    event.stopPropagation() // 阻止事件冒泡
    await closeEditor()
  }
}

// 添加和移除事件监听器的方法
const addKeyupListener = () => {
  if (!keyupListener.value) {
    keyupListener.value = (event) => handleKeyUp(event)
    window.addEventListener('keyup', keyupListener.value)
  }
}

const removeKeyupListener = () => {
  if (keyupListener.value) {
    window.removeEventListener('keyup', keyupListener.value)
    keyupListener.value = null
  }
}

// 修改 onMounted 和 onBeforeUnmount
onMounted(() => {
  addKeyupListener()
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeUnmount(() => {
  removeKeyupListener()
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

const handleBeforeUnload = (event) => {
  if (isModified.value) {
    event.preventDefault()
    event.returnValue = ''
  }
}

const getFormData = () => {
  // 返回当前表单的数据
  return {
    // ... 表单数据
  }
}

// 暴露方法和属性给父组件
defineExpose({
  close: closeEditor,
  isModified,
  getFormData
})

// 添加一个新的函数来根据类型返回对应的组件
const getComponentByType = (type) => {
  const componentMap = {
    input: NInput,
    textarea: NInput,
    number: NInput,
    datetime: NDatePicker,
    radio: NRadioGroup,
    checkbox: NCheckboxGroup,
    select: NSelect,
    multiSelect: NSelect,
    memberSelect: NSelect,
    memberMultiSelect: NSelect,
    departmentSelect: NSelect,
    departmentMultiSelect: NSelect,
    divider: NDivider,
    tabs: NTabs,
    image: NUpload,
    attachment: NUpload,
    // 其他组件类型映射...
  }
  return componentMap[type] || 'div' // 如果没有匹配的组件,默认返回 div
}

// 修改 getDefaultProps 方法
const getDefaultProps = (type) => {
  const commonProps = {
    placeholder: '',
    required: false,
    unique: false,
    permission: 'editable',
    width: '100%'
  }
  
  const specificProps = {
    input: { format: 'none', maxLength: 100 },
    textarea: { maxLength: 500 },
    number: { format: 'number', precision: 2, thousandSeparator: false },
    datetime: { type: 'date' },
    radio: { options: [{ label: '选项1', value: '1' }, { label: '选项2', value: '2' }], layout: 'vertical' },
    // ... 其他组件类型的默认属性
  }
  
  return { ...commonProps, ...(specificProps[type] || {}) }
}

// 修改 copyField 方法
const copyField = (field) => {
  try {
    const newField = JSON.parse(JSON.stringify(field))
    newField.id = Date.now()
    formFields.value.push(newField)
    isModified.value = true
    selectField(newField) // 选中新复制的字段
  } catch (error) {
    console.error('Error copying field:', error)
  }
}

// 修改 deleteField 方法
const deleteField = (index) => {
  try {
    formFields.value.splice(index, 1)
    isModified.value = true
    if (selectedField.value && selectedField.value.id === formFields.value[index]?.id) {
      if (index > 0) {
        selectField(formFields.value[index - 1]) // 选中上一个字段
      } else if (formFields.value.length > 0) {
        selectField(formFields.value[0]) // 如果删除的是第一个，选中新的第一个
      } else {
        selectedField.value = null // 如果没有字段了，清空选择
      }
    }
  } catch (error) {
    console.error('Error deleting field:', error)
  }
}

// 修改 addComponent 方法
const addComponent = (component) => {
  try {
    const newComponent = { 
      ...component, 
      id: Date.now(),
      label: component.label,
      props: getDefaultProps(component.type)
    }
    formFields.value.push(newComponent)
    isModified.value = true
    selectField(newComponent) // 选中新添加的组件
  } catch (error) {
    console.error('Error adding component:', error)
  }
}

// 修改 watch 以响应 props 的变化
watch(
  () => selectedField.value?.props,
  () => {
    if (selectedField.value) {
      const index = formFields.value.findIndex(field => field.id === selectedField.value.id)
      if (index !== -1) {
        formFields.value[index] = { ...selectedField.value }
      }
    }
  },
  { deep: true }
)
</script>

<style scoped>
.workflow-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.tab-nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
  padding: 0 20px;
}

.tab-nav {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.tab-nav li {
  padding: 10px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab-nav li.active {
  color: green;
  border-bottom-color: green;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.tab-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-pane {
  flex: 1;
  background-color: #ffffff;
  padding: 20px;
  overflow-y: auto;
}

.form-designer {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.component-list, .property-panel {
  width: 200px;
  border: 1px solid #ccc;
  padding: 10px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.form-canvas {
  flex: 1;
  border: 1px solid #ccc;
  margin: 0 10px;
  padding: 10px;
  overflow-y: auto;
  background-color: #ffffff;
}

.component-list {
  width: 250px;
  border: 1px solid #ccc;
  padding: 10px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.component-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.component-button {
  width: calc(50% - 5px);
  text-align: left;
  padding: 8px;
}

.component-button .n-icon {
  margin-right: 8px;
}

.form-field {
  position: relative;
  padding: 10px;
  border: 1px solid #ddd;
  margin-bottom: 10px;
  cursor: move;
  box-sizing: border-box;
  transition: background-color 0.3s ease;
}

.form-field.selected {
  background-color: #e6f7e6;
}

.field-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 5px;
  z-index: 1;
}

.field-content {
  padding-right: 60px; /* 为操作按钮留出空间 */
}

.field-label {
  margin-bottom: 5px;
  font-weight: bold;
}

.field-preview {
  pointer-events: none;
  opacity: 0.7;
}

.field-preview :deep(*) {
  cursor: default !important;
}

.property-panel {
  width: 250px;
  border: 1px solid #ccc;
  padding: 10px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

/* 添加新的样式 */
.property-panel :deep(.n-form-item) {
  margin-bottom: 18px;
}

.property-panel :deep(.n-form-item-label) {
  padding-bottom: 8px;
}

.property-panel :deep(.n-form-item-blank) {
  display: flex;
  flex-direction: column;
}

.required-mark {
  color: red;
  margin-right: 4px;
}

.field-description {
  font-weight: normal;
  color: #999;
  margin-left: 8px;
  font-size: 0.9em;
}

.property-panel :deep(.n-space) {
  width: 100%;
}

.property-panel :deep(.n-checkbox) {
  margin-left: 0;
}
</style>

