<template>
  <div class="task-result-page">
    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error }}</div>
    <div v-else class="result-container">
      <h2>任务ID: {{ taskResult.taskId }}</h2>
      
      <!-- 任务状态显示 -->
      <div class="status" :class="taskResult.taskStatus.toLowerCase()">
        状态: {{ getStatusText(taskResult.taskStatus) }}
      </div>
      
      <!-- 当任务完成时才显示结果 -->
      <template v-if="taskResult.taskStatus === 'DONE'">
        <!-- 图像结果展示区 -->
        <div class="image-results">
          <div class="image-section">
            <h3>角度检测结果</h3>
            <img :src="taskResult.taskResult.angeleImage" alt="角度检测图" />
          </div>
          
          <div class="image-section">
            <h3>抓取点检测结果</h3>
            <img :src="taskResult.taskResult.graspImage" alt="抓取检测图" />
          </div>

          <div class="image-section">
            <h3>掩码检测结果</h3>
            <img :src="taskResult.taskResult.maskImage[0]" alt="掩码检测图" />
          </div>
        </div>

        <!-- 物体识别结果列表 -->
        <div class="detection-results">
          <h3>识别结果列表</h3>
          <div class="objects-grid">
            <div v-for="(item, index) in taskResult.taskResult.answers" :key="index" class="object-card">
              <img :src="taskResult.taskResult.croppedImagesListBbox[index]" alt="物体图" />
              <div class="object-info">
                <p>标签: {{ taskResult.taskResult.labels[index] }}</p>
                <p>置信度: {{ (taskResult.taskResult.scores[index] * 100).toFixed(2) }}%</p>
                <div class="attributes">
                  <template v-for="(value, key) in item" :key="key">
                    <p>{{ key }}: {{ value }}</p>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 当任务正在运行时显示 -->
      <div v-else-if="taskResult.taskStatus === 'RUNNING'" class="running-status">
        <div class="loading-spinner"></div>
        <p>任务正在处理中，请稍候...</p>
        <button @click="fetchTaskResult" class="refresh-btn">刷新状态</button>
      </div>
    </div>
  </div>
</template>

<script>
import { doGet } from '@/utils/requests'

export default {
  name: 'TaskResultPage',
  data() {
    return {
      taskResult: null,
      loading: true,
      error: null,
      pollingInterval: null
    }
  },
  created() {
    this.fetchTaskResult()
  },
  beforeUnmount() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }
  },
  methods: {
    getStatusText(status) {
      const statusMap = {
        'RUNNING': '处理中',
        'DONE': '已完成',
        'FAILED': '失败'
      }
      return statusMap[status] || status
    },
    async fetchTaskResult() {
      try {
        const taskId = this.$route.query.task_id
        if (!taskId) {
          throw new Error('未提供任务ID')
        }

        const { data } = await doGet('/open-apis/app/perception/result', { task_id: taskId })
        this.taskResult = data
        
        // 如果任务正在运行，设置轮询
        if (this.taskResult.taskStatus === 'RUNNING') {
          if (!this.pollingInterval) {
            this.pollingInterval = setInterval(() => {
              this.fetchTaskResult()
            }, 5000) // 每5秒轮询一次
          }
        } else {
          // 如果任务已完成或失败，清除轮询
          if (this.pollingInterval) {
            clearInterval(this.pollingInterval)
            this.pollingInterval = null
          }
        }
      } catch (err) {
        this.error = err.message
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.task-result-page {
  padding: 20px;
}

.result-container {
  max-width: 1200px;
  margin: 0 auto;
}

.image-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.image-section {
  text-align: center;
}

.image-section img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.objects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.object-card {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 10px;
  background: #fff;
}

.object-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}

.object-info {
  margin-top: 10px;
}

.attributes {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.attributes p {
  margin: 5px 0;
  font-size: 14px;
}

/* 新增状态相关样式 */
.status {
  padding: 8px 16px;
  border-radius: 4px;
  margin: 10px 0;
  display: inline-block;
  font-weight: bold;
}

.status.running {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status.done {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.running-status {
  text-align: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.refresh-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
}

.refresh-btn:hover {
  background: #40a9ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>