<script setup>
import { ref, onMounted } from 'vue'
import { NGrid, NGridItem, NButton, NModal, NIcon } from 'naive-ui'
import { AddCircleOutline } from '@vicons/ionicons5'
import { applicationApi } from '@/api/applications'
import AppCard from '@/components/app/AppCard.vue'
import AppForm from '@/components/app/AppForm.vue'
import { useRouter } from 'vue-router'
import messages from '@/utils/messages'

const router = useRouter()
const applications = ref([])
const showCreateModal = ref(false)
const loading = ref(false)
const formRef = ref(null)

// 加载应用列表
const loadApplications = async () => {
  loading.value = true
  try {
    applications.value = await applicationApi.getApplications()
  } catch (error) {
    console.error('Failed to load applications:', error)
  } finally {
    loading.value = false
  }
}

// 创建应用
const handleCreateSubmit = async (formData) => {
  loading.value = true
  try {
    await applicationApi.createApplication(formData)
    messages.success('应用创建成功')
    showCreateModal.value = false
    // 重新加载应用列表
    await loadApplications()
  } catch (error) {
    console.error('Failed to create application:', error)
    messages.error('应用创建失败')
  } finally {
    loading.value = false
  }
}

// 点击应用卡片
const handleAppClick = (app) => {
  console.log('Navigate to app:', app.name)
  // TODO: 实现应用导航逻辑
  router.push(`/app/${app.id}`)
}

// 创建应用按钮点击
const handleCreateApp = () => {
  showCreateModal.value = true
}

// 添加一个新的方法来处理确认按钮点击
const handlePositiveClick = async () => {
  console.log('点击确认按钮')
  if (formRef.value) {
    await formRef.value.handleSubmit()
  }
}

onMounted(loadApplications)
</script>

<template>
  <div class="apps-container">
    <div class="section-header">
      <h2 class="section-title">我的应用</h2>
      <n-button type="primary" @click="handleCreateApp">
        <template #icon>
          <n-icon>
            <AddCircleOutline />
          </n-icon>
        </template>
        创建应用
      </n-button>
    </div>

    <!-- 添加空状态显示 -->
    <div v-if="applications.length === 0" class="empty-state">
      <p class="empty-text">您还没有任何应用</p>
      <n-button type="primary" @click="handleCreateApp">
        <template #icon>
          <n-icon>
            <AddCircleOutline />
          </n-icon>
        </template>
        立即创建
      </n-button>
    </div>

    <!-- 有应用时显示网格 -->
    <n-grid v-else :cols="2" :x-gap="16" :y-gap="16">
      <n-grid-item v-for="app in applications" :key="app.id">
        <app-card :app="app" @click="handleAppClick" />
      </n-grid-item>
    </n-grid>
  </div>

  <!-- 创建应用弹窗 -->
  <n-modal
    v-model:show="showCreateModal"
    preset="dialog"
    title="创建应用"
    positive-text="创建"
    negative-text="取消"
    style="width: 680px"
    @positive-click="handlePositiveClick"
    @negative-click="() => { showCreateModal = false }"
    :footer-style="{ backgroundColor: 'transparent' }"
    :positive-button-props="{ 
      disabled: loading,
      loading: loading,
    }"
    :negative-button-props="{ 
      disabled: loading 
    }"
  >
    <app-form
      ref="formRef"
      @submit="handleCreateSubmit"
      @cancel="() => { showCreateModal = false }"
    />
  </n-modal>
</template>

<style scoped>
.apps-container {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 小屏幕设备 (平板和小屏笔记本) */
@media screen and (min-width: 768px) {
  .apps-container {
    width: 90%;
  }
}

/* 中等屏幕设备 (大屏笔记本) */
@media screen and (min-width: 1024px) {
  .apps-container {
    width: 85%;
    max-width: 1200px;
  }
}

/* 大屏幕设备 (台式机和大显示器) */
@media screen and (min-width: 1440px) {
  .apps-container {
    width: 75%;
    max-width: 1400px;
  }
}

/* 超大屏幕设备 */
@media screen and (min-width: 1920px) {
  .apps-container {
    width: 65%;
    max-width: 1600px;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  height: 32px;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
  line-height: 32px;
  display: flex;
  align-items: center;
}

/* 添加空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  background-color: #fafafa;
  border-radius: 8px;
  margin-top: 16px;
}

.empty-text {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 16px;
}
</style> 