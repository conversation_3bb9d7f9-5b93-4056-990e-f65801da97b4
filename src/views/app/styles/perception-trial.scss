.perception-trial {
    width: 100%;
}

.trial-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
}

.left-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.right-actions {
    display: flex;
    align-items: center;
}

/* 新的左右布局样式 */
.main-content {
    display: flex;
    gap: 24px;
    min-height: 600px;
}

.left-panel {
    flex: 1;
    min-width: 400px;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding-right: 8px;
    box-sizing: border-box;
}

.right-panel {
    flex: 1;
    min-width: 400px;
    display: flex;
    flex-direction: column;
    padding-left: 8px;
    border-left: 1px solid #eee;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid #18a058;
    display: inline-block;
}

.upload-section {
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.input-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.action-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

:deep(.n-tabs) {
    display: flex;
    flex-direction: column;
    flex: 1;
}

:deep(.n-tabs-content) {
    flex: 1;
}

:deep(.n-tab-pane) {
    padding: 0;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.upload-box-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
}

.upload-box {
    min-height: 120px;
    flex: 1;
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    position: relative;
}

.upload-box:hover {
    border-color: #18a058;
}

.upload-trigger {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    min-height: 80px;
    pointer-events: none;
}

.upload-icon {
    color: #909399;
    margin-bottom: 8px;
    pointer-events: none;
}

.upload-text {
    color: #909399;
    font-size: 14px;
    margin: 0;
    pointer-events: none;
}

.image-preview {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f9fa;
    border-radius: 4px;
    overflow: hidden;
    pointer-events: none;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    pointer-events: none;
}

.image-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
    display: flex;
    gap: 8px;
    pointer-events: auto;
}

.remove-image {
    width: 24px;
    height: 24px;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remove-image:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.remove-image .n-icon {
    font-size: 16px;
    color: #d03050;
}

.zoom-image {
    width: 24px;
    height: 24px;
    padding: 0;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-image:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.zoom-image .n-icon {
    font-size: 16px;
    color: #2080f0;
}

.function-select {
    width: 100%;
}

.single-line-input,
.multi-line-input {
    width: 100%;
}

.send-button {
    width: 100%;
    height: 40px;
    box-sizing: border-box;
}

.send-button .n-icon {
    font-size: 16px;
}

/* 优化一下按钮样式 */
.n-button {
    display: flex;
    align-items: center;
    justify-content: center;
}

.n-button.n-button--quaternary {
    width: 32px;
    height: 32px;
    padding: 0;
}

/* 图标样式 */
.n-icon {
    font-size: 18px;
}

/* Tab 样式优化 */
:deep(.n-tabs-nav) {
    margin-bottom: 8px;
    flex-shrink: 0;
}

:deep(.n-tab-pane) {
    padding: 0;
}

/* 简化功能选项样式 */
.function-option {
    padding: 8px 0;
    font-size: 14px;
}

/* 移除不需要的样式 */
.function-detail,
.method-tag,
.path-text {
    display: none;
}

/* 修改下拉框样式 */
.function-select {
    width: 100%;
}

/* 设置下拉框文字居中 */
:deep(.n-base-selection-label) {
    justify-content: center;
    text-align: center;
}

:deep(.n-base-selection-placeholder) {
    justify-content: center;
    text-align: center;
}

:deep(.n-base-selection-input) {
    text-align: center;
}

/* 设置下拉选项文字居中 */
:deep(.n-select-option-body) {
    justify-content: center !important;
}

:deep(.n-select-option__content) {
    text-align: center;
    width: 100%;
}

/* 设置选项激活和hover状态下的样式也保持居中 */
:deep(.n-select-option.n-select-option--selected) .n-select-option__content {
    text-align: center;
    width: 100%;
}

:deep(.n-select-option:hover) .n-select-option__content {
    text-align: center;
    width: 100%;
}

/* 确保下拉框和单行输入框高度一致 */
.function-select,
.single-line-input {
    height: 34px;
}

/* 标签输入组件样式 */
.object-names-input {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.tags-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    margin-top: 8px;
}

.tags-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.tags-count {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: flex-start;
    align-content: flex-start;
}

.object-tag {
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    background: transparent;
    color: #18a058;
    border: 1px solid #18a058;
    display: flex;
    align-items: center;
    gap: 4px;
    max-width: 200px;
}

.object-tag:hover {
    background: rgba(24, 160, 88, 0.1);
    border-color: #16a085;
    color: #16a085;
}

/* 自定义标签的关闭按钮样式 */
:deep(.object-tag .n-tag__close) {
    color: #18a058 !important;
}

:deep(.object-tag:hover .n-tag__close) {
    color: #16a085 !important;
}

/* 当没有标签时隐藏容器 */
.tags-container:empty {
    display: none;
}

/* 问题输入区域样式 */
.questions-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    flex-shrink: 0;
    width: 100%;
    box-sizing: border-box;
}

.section-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid #18a058;
    display: inline-block;
}

.question-item {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    flex-shrink: 0;
    box-sizing: border-box;
}

.question-label {
    font-size: 14px;
    font-weight: 500;
    color: #18a058;
    min-width: 80px;
    flex-shrink: 0;
    text-align: right;
}

.question-input {
    flex: 1;
    height: 34px;
}

/* 多行文本框独立布局 */
.multi-line-input {
    flex: 1;
    margin-top: 16px;
    min-height: 120px;
    max-height: 200px;
    overflow-y: auto;
}

.result-container {
    display: flex;
    flex-direction: column;
}

.result-area {
    width: 100%;
}

.result-area.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    min-height: 200px;
}

.empty-result {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    color: #999;
}

.empty-result-with-suggestions {
    padding: 40px 20px;
    text-align: center;

    .empty-suggestions {
        margin-top: 20px;

        .suggestion-title {
            font-size: 16px;
            font-weight: 500;
            color: #666;
            margin-bottom: 12px;
        }

        .suggestion-list {
            text-align: left;
            display: inline-block;
            margin: 0 0 20px 0;
            padding: 0;
            list-style: none;

            li {
                position: relative;
                padding: 6px 0 6px 20px;
                color: #888;
                font-size: 14px;
                line-height: 1.5;

                &::before {
                    content: "•";
                    position: absolute;
                    left: 0;
                    color: #ccc;
                    font-weight: bold;
                }
            }
        }

        .suggestion-actions {
            margin-top: 16px;
        }
    }
}

.result-code {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.result-summary {
    text-align: center;
    font-size: 16px;
    color: #18a058;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: 500;
}

/* 添加禁用状态的样式 */
.multi-line-input:disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.task-info {
    width: 100%;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #eee;
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 统一所有任务信息项为一行显示，两端对齐 */
.task-info-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    width: 100%;
    min-height: 32px;
}

.task-info-item .label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
    flex-shrink: 0;
    white-space: nowrap;
}

.task-info-item .value {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    font-size: 14px;
    flex: 1;
    min-width: 0;
}

/* 对于包含文本的值，允许换行但保持右对齐 */
.task-info-item .value span {
    text-align: right;
    word-break: break-all;
    line-height: 1.4;
}

/* 操作按钮容器保持右对齐 */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    align-items: center;
}



/* 调整复制按钮样式 */
.task-info-item .n-button {
    padding: 2px;
    height: 20px;
    width: 20px;
    min-width: 20px;
    flex-shrink: 0;
}

.task-info-item .n-button .n-icon {
    font-size: 14px;
}

/* 调整标签样式 */
:deep(.n-tag) {
    font-size: 14px;
    padding: 4px 12px;
    border-radius: 4px;
}

/* 修改上传触发区域的样式 */
:deep(.n-upload-trigger) {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    width: 100%;
}

:deep(.n-upload) {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.n-upload-trigger-area) {
    flex: 1;
    width: 100%;
}

.upload-box {
    height: 100%;
    flex: 1;
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    position: relative;
}

.upload-trigger {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.upload-icon,
.upload-text {
    pointer-events: none;
}

/* 添加3D模式特定的样式 */
.upload-box-container-3d {
    flex-direction: column;
    gap: 8px;
    height: 100%;
    overflow: hidden;
}

.upload-box-container-3d .upload-box {
    flex: 1;
    min-height: 0;
    height: calc(50% - 4px);
    padding: 8px;
}

/* 调整上传框内部元素的样式 */
.upload-box-container-3d .upload-icon {
    font-size: 24px;
    margin-bottom: 4px;
}

.upload-box-container-3d .upload-text {
    font-size: 12px;
    margin: 0;
    line-height: 1.2;
}

/* 调整预览图片容器在3D模式下的样式 */
.upload-box-container-3d .image-preview {
    padding: 4px;
}

.additional-image-container {
    margin: 20px 0;
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
}

.image-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 4px;
}

.image-wrapper img {
    max-width: 100%;
    height: auto;
    object-fit: contain;
}

/* 添加响应式样式 */
@media screen and (max-width: 1920px) {
    .image-title {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .additional-image-container {
        margin: 16px 0;
        padding: 12px;
    }
}



/* 修改按钮样式 */
.action-buttons .n-button {
    min-width: 80px;
    padding: 0 16px;
    height: 28px;
    font-size: 14px;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
    .main-content {
        flex-direction: column;
        height: auto;
        min-height: auto;
    }

    .left-panel {
        max-width: none;
        min-width: auto;
        padding-right: 0;
        margin-bottom: 20px;
    }

    .right-panel {
        min-width: auto;
        padding-left: 0;
        border-left: none;
        border-top: 1px solid #eee;
        padding-top: 20px;
    }

    .upload-section {
        min-height: 200px;
        max-height: 280px;
    }

    .input-section {
        min-height: auto;
        max-height: 300px;
    }
}

@media screen and (max-width: 768px) {
    .left-panel {
        gap: 16px;
    }

    .section-title {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .upload-section {
        min-height: 180px;
        max-height: 220px;
    }

    .input-section {
        max-height: 250px;
    }

    .multi-line-input {
        min-height: 100px;
        max-height: 150px;
    }

    .task-info {
        padding: 12px;
        font-size: 12px;
    }

    /* 在小屏幕上保持所有任务信息项的一行布局 */
    .task-info-item {
        min-height: 28px;
        gap: 8px;
    }

    .task-info-item .label {
        font-size: 12px;
    }

    .task-info-item .value {
        font-size: 12px;
    }

    .task-info-item .value span {
        font-size: 12px;
    }

    /* 调整标签在小屏幕上的样式 */
    :deep(.n-tag) {
        font-size: 12px;
        padding: 2px 8px;
    }

    .action-buttons .n-button {
        min-width: 60px;
        font-size: 12px;
        padding: 0 8px;
        height: 20px;
    }
}

/* 添加取消按钮特定样式 */
.cancel-button {
    cursor: pointer;
}

.cancel-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

/* 移除取消按钮的hover效果 */
.cancel-button:hover {
    background-color: var(--n-color) !important;
    border-color: var(--n-border-color) !important;
}



/* 添加大图预览容器样式 */
.large-image-container {
    width: 100%;
    height: 80vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.large-image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 调整弹窗样式 */
:deep(.n-modal) {
    background: rgba(0, 0, 0, 0.85);
}

:deep(.n-card) {
    background: transparent;
    border: none;
    padding: 0;
}

:deep(.n-card__content) {
    padding: 0;
}