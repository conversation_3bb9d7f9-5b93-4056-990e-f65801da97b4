
.rooms-view {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 24px;
    box-sizing: border-box;
    background-color: #f5f7fa;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.left-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    height: 32px;
}

.page-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    line-height: 32px;
    display: flex;
    align-items: center;
}

.room-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;
}

.room-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.room-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
    border: 2px solid #eee;
    cursor: pointer;
}

.room-card:hover:not(.room-card--selected) {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.room-card--selected:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(24, 160, 88, 0.2);
    background: linear-gradient(135deg, rgba(24, 160, 88, 0.2) 0%, rgba(24, 160, 88, 0.1) 100%);
}

/* 自定义滚动条样式 */
.room-container::-webkit-scrollbar {
    width: 6px;
}

.room-container::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 3px;
}

.room-container::-webkit-scrollbar-track {
    background-color: transparent;
}

/* 添加新的样式 */
.target-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px 0;
}

.target-tag {
    max-width: 100%;
}

.more-tag {
    background-color: #f3f5f6 !important;
    border: none !important;
    color: #666 !important;
}

:deep(.more-tag .n-tag__border) {
    border: none;
}

:deep(.delete-btn) {
    color: #d03050;
    &:hover {
        color: #de576d;
        background-color: rgba(208, 48, 80, 0.1);
    }
}

:deep(.n-card-header) {
    padding: 16px 20px;
}

:deep(.n-card__content) {
    padding: 0 20px 20px 20px;
}

:deep(.n-divider) {
    margin: 0;
}

:deep(.n-tag) {
    display: inline-flex;
    align-items: center;
}

:deep(.n-tag__close) {
    margin-left: 4px;
    transition: all 0.3s;
}

:deep(.n-tag__close:hover) {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
}

:deep(.edit-btn) {
    color: #2080f0;
    &:hover {
        color: #3091ff;
        background-color: rgba(32, 128, 240, 0.1);
    }
}

:deep(.n-checkbox) {
    display: flex;
    align-items: center;
}

:deep(.n-checkbox__label) {
    font-size: 14px;
    color: #666;
}

:deep(.n-button) {
    color: #666;
    transition: all 0.3s ease;
    &:disabled {
        color: #ccc;
        cursor: not-allowed;
    }
}

:deep(.primary-btn) {
    color: #666;
    transition: all 0.2s ease;
    cursor: pointer;
}

:deep(.primary-btn.primary-btn--active) {
    color: #ffd700;
    background-color: rgba(255, 215, 0, 0.15);
    transform: scale(1.05);
}

:deep(.primary-btn.primary-btn--active:hover) {
    color: #ffed4a;
    background-color: rgba(255, 215, 0, 0.2);
    transform: scale(1.1);
}

:deep(.primary-btn:hover) {
    transform: scale(1.1);
    transition: all 0.2s ease;
}

.edit-dialog-content {
    min-width: 400px;
    padding: 16px 0;
}

.edit-dialog-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 标签容器样式 */
.tags-container {
    min-height: 120px;
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 8px;
    display: flex;
    align-items: center;
    /* 垂直居中 */
}

/* 标签列表样式 */
.tags-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
}

.target-tag {
    margin: 0;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
    .rooms-view {
        padding: 16px;
    }

    .header {
        padding: 12px 20px;
        margin-bottom: 16px;
    }

    .room-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 12px;
    }

    .page-title {
        font-size: 14px;
        line-height: 28px;
    }

    :deep(.n-card-header) {
        padding: 12px 16px;
    }

    :deep(.n-card__content) {
        padding: 0 16px 16px 16px;
    }

    .target-tags {
        gap: 6px;
        padding: 6px 0;
    }

    .edit-dialog-content {
        min-width: 320px;
    }

    .tags-container {
        min-height: 100px;
    }

    .tags-list {
        gap: 6px;
    }
}

/* 修改选中状态样式 */
.room-card--selected {
    border-color: rgba(24, 160, 88, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(24, 160, 88, 0.15);
}

/* 修改选中状态的悬停效果 */
.room-card--selected:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(24, 160, 88, 0.2);
}

.right-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

:deep(.n-checkbox) {
    display: flex;
    align-items: center;
}

:deep(.n-checkbox__label) {
    font-size: 14px;
    color: #666;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
    .right-actions {
        gap: 8px;
    }

    :deep(.n-checkbox__label) {
        font-size: 13px;
    }
}

/* 修改禁用状态样式 */
.room-card--disabled {
    opacity: 0.8;
    /* 降低透明度但保持可见 */
    cursor: not-allowed;
    /* 显示禁止光标 */
}

/* 禁用状态下的悬停效果 */
.room-card--disabled:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 确保按钮在禁用状态下仍然可以点击 */
.room-card--disabled .card-actions {
    opacity: 1;
    /* 保持按钮完全不透明 */
    cursor: pointer;
    /* 保持按钮可点击状态 */
}

/* 添加新增房间按钮样式 */
.add-room-btn {
    height: 100%;
    border: 2px dashed rgba(24, 160, 88, 0.8);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: rgba(24, 160, 88, 0.05);
    color: rgba(24, 160, 88, 0.8);
    min-height: 200px;
    /* 与卡片高度保持一致 */
}

.add-room-btn:hover {
    transform: translateY(-4px);
    background-color: rgba(24, 160, 88, 0.1);
    border-color: rgba(24, 160, 88, 1);
    color: rgba(24, 160, 88, 1);
    box-shadow: 0 8px 16px rgba(24, 160, 88, 0.15);
}

.add-room-btn .add-icon {
    transition: transform 0.3s ease;
}

.add-room-btn:hover .add-icon {
    transform: scale(1.2);
}

.add-room-btn span {
    font-size: 14px;
    font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
    .add-room-btn {
        min-height: 180px;
        /* 适应小屏幕 */
    }

    .add-room-btn span {
        font-size: 13px;
    }
}

.room-name-edit {
    margin-bottom: 16px;
}

.room-name-input {
    width: 100%;
}

:deep(.n-input .n-input__prefix) {
    margin-right: 8px;
    color: #666;
}

@media screen and (max-width: 1920px) {
    .room-name-edit {
        margin-bottom: 12px;
    }
}

/* 修改下一步按钮相关样式 */
.next-step {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.next-step-text {
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

/* 移除文本的隐藏和位移效果 */
.next-step--active .next-step-text {
    color: var(--n-primary-color);
    /* 激活时改变文字颜色 */
}

.next-step :deep(.n-button) {
    color: #666;
    transition: all 0.3s ease;
}

.next-step--active :deep(.n-button) {
    color: var(--n-primary-color);
}

.next-step--active :deep(.n-button:hover) {
    color: var(--n-primary-color-hover);
    background-color: rgba(var(--n-primary-color-rgb), 0.1);
}

.next-step :deep(.n-button:disabled) {
    color: #ccc;
    cursor: not-allowed;
}

/* 在 style 部分添加主要房间按钮的样式 */
:deep(.primary-btn) {
    color: #666;
    transition: all 0.2s ease;
    cursor: pointer;
}

:deep(.primary-btn--active) {
    color: #ffd700;
    background-color: rgba(255, 215, 0, 0.15);
    transform: scale(1.05);
}

:deep(.primary-btn--active:hover) {
    color: #ffed4a;
    background-color: rgba(255, 215, 0, 0.2);
    transform: scale(1.1);
}

:deep(.primary-btn:hover) {
    transform: scale(1.1);
    transition: all 0.2s ease;
}

.edit-dialog-content {
    min-width: 400px;
    padding: 16px 0;
}

.edit-dialog-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 标签容器样式 */
.tags-container {
    min-height: 120px;
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 8px;
    display: flex;
    align-items: center;
    /* 垂直居中 */
}

/* 标签列表样式 */
.tags-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
}

.target-tag {
    margin: 0;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
    .rooms-view {
        padding: 16px;
    }

    .header {
        padding: 12px 20px;
        margin-bottom: 16px;
    }

    .room-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 12px;
    }

    .page-title {
        font-size: 14px;
        line-height: 28px;
    }

    :deep(.n-card-header) {
        padding: 12px 16px;
    }

    :deep(.n-card__content) {
        padding: 0 16px 16px 16px;
    }

    .target-tags {
        gap: 6px;
        padding: 6px 0;
    }

    .edit-dialog-content {
        min-width: 320px;
    }

    .tags-container {
        min-height: 100px;
    }

    .tags-list {
        gap: 6px;
    }
}