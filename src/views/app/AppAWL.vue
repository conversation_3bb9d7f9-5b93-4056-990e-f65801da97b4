<script setup>
import { ref, onMounted, inject } from 'vue'
import {
    NCard, NSpace, NInput, NButton, NIcon,
    NPopconfirm, useMessage, NForm, NFormItem,
    NDataTable, NSwitch
} from 'naive-ui'
import {
    HelpCircleOutline, AddOutline,
    EllipseOutline, ArrowForwardOutline,
    TrashOutline
} from '@vicons/ionicons5'
import { useRoute } from 'vue-router'
import { doGet,doPost,doDelete,doPut } from '@/utils/requests'
import messages from '@/utils/messages'
const currentAppId = inject('currentAppId')

const route = useRoute()
const message = useMessage()
const appId = route.params.id

// IP白名单列表数据
const ipList = ref([])
const newIp = ref('')
const loading = ref(false)

// 表格列定义
const columns = [
    {
        title: 'IP地址',
        key: 'ip'
    },
    {
        title: '操作',
        key: 'actions',
        render: (row) => {
            return h(
                NPopconfirm,
                {
                    onPositiveClick: () => removeIp(row.ip)
                },
                {
                    trigger: () => 
                        h(
                            NButton,
                            {
                                quaternary: true,
                                circle: true,
                                size: 'small',
                                type: 'error'
                            },
                            { icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
                        ),
                    default: () => '确认删除此IP地址吗？'
                }
            )
        }
    }
]

// 添加状态枚举
const AWL_STATUS = {
    ENABLE: 'ENABLE',
    DISABLED: 'DISABLED'
}

// 修改IP白名单数据结构
const awlData = ref({
    id: null,
    appId: appId,
    ipWhiteList: '',
    awlStatus: AWL_STATUS.DISABLED
})



// 添加IP格式验证函数
const isValidIP = (ip) => {
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (!ipRegex.test(ip)) return false
  
  const parts = ip.split('.')
  return parts.every(part => {
    const num = parseInt(part, 10)
    return num >= 0 && num <= 255
  })
}

// 修改加载IP白名单列表方法
const loadIpList = async () => {
    try {
        loading.value = true
        const response = await doGet(`/app-center/app/awl/detail/${appId}`)
        
        if (response.data) {
            // 确保所有必要的字段都存在
            awlData.value = {
                id: response.data.id || null,
                appId: response.data.appId || appId,
                ipWhiteList: response.data.ipWhiteList || '',
                awlStatus: response.data.awlStatus || AWL_STATUS.DISABLED
            }
            
            // 处理 IP 列表
            ipList.value = awlData.value.ipWhiteList ? 
                awlData.value.ipWhiteList.split(',')
                    .filter(ip => ip.trim())
                    .map(ip => ({
                        ip: ip.trim(),
                        createTime: '-'
                    })) : []
        }
    } catch (error) {
        console.error('Error loading IP list:', error)
        messages.error('加载IP白名单失败')
        // 确保发生错误时有默认值
        awlData.value = {
            id: null,
            appId: appId,
            ipWhiteList: '',
            awlStatus: AWL_STATUS.DISABLED
        }
        ipList.value = []
    } finally {
        loading.value = false
    }
}

// 修改添加IP地址方法
const addIp = async () => {
    if (!newIp.value) {
        messages.error('请输入IP地址')
        return
    }

    try {
        // 分割输入的IP地址(支持逗号、分号、空格分隔)
        const ips = newIp.value.split(/[,;\s]+/).filter(ip => ip.trim())
        
        // 验证所有IP格式
        const invalidIps = ips.filter(ip => !isValidIP(ip.trim()))
        if (invalidIps.length > 0) {
            messages.error(`以下IP地址格式无效: ${invalidIps.join(', ')}`)
            return
        }

        // 检查重复的IP
        const existingIps = ipList.value.map(item => item.ip)
        const duplicateIps = ips.filter(ip => existingIps.includes(ip.trim()))
        const newUniqueIps = ips.filter(ip => !existingIps.includes(ip.trim()))

        // 如果全部都是重复的IP，提示用户
        if (newUniqueIps.length === 0) {
            messages.warning('输入的IP地址已全部存在于白名单中')
            newIp.value = '' // 清空输入框
            return
        }

        // 如果有部分重复的IP，提示用户
        if (duplicateIps.length > 0) {
            messages.info(`以下IP地址已存在，将被自动过滤: ${duplicateIps.join(', ')}`)
        }

        // 合并不重复的IP
        const allIps = [...existingIps, ...newUniqueIps]

        await doPut('/app-center/app/awl', {
            id: awlData.value.id,
            ipWhiteList: allIps.join(',')
        })
        
        messages.success('添加成功')
        newIp.value = ''
        await loadIpList()
    } catch (error) {
        messages.error('添加失败')
    }
}

// 修改删除IP地址方法
const removeIp = async (ip) => {
    try {
        // 从现有IP列表中移除指定IP
        const newIpList = ipList.value
            .filter(item => item.ip !== ip)
            .map(item => item.ip)
        
        await doPut('/app-center/app/awl', {
            id: awlData.value.id,
            ipWhiteList: newIpList.join(',')
        })
        
        messages.success('删除成功')
        await loadIpList()
    } catch (error) {
        messages.error('删除失败')
    }
}

// 修改toggleAwlStatus方法
const toggleAwlStatus = async (value) => {
    try {
        loading.value = true
        const newStatus = value ? AWL_STATUS.ENABLE : AWL_STATUS.DISABLED
        
        // 获取当前的IP列表
        const currentIpList = ipList.value.map(item => item.ip).join(',')
        
        // 如果是第一次开启，需要创建记录
        if (!awlData.value.id) {
            const response = await doPost('/app-center/app/awl', {
                appId: appId,
                awlStatus: newStatus,
                ipWhiteList: currentIpList  // 传递当前IP列表
            })
            if (response.data) {
                awlData.value = response.data
            }
        } else {
            // 更新现有记录
            await doPut('/app-center/app/awl', {
                id: awlData.value.id,
                awlStatus: newStatus,
                ipWhiteList: currentIpList  // 传递当前IP列表
            })
            awlData.value.awlStatus = newStatus
            awlData.value.ipWhiteList = currentIpList
        }
        
        messages.success(value ? '已开启IP白名单' : '已关闭IP白名单')
        await loadIpList() // 重新加载数据以确保状态同步
    } catch (error) {
        console.error('Toggle AWL status error:', error)
        messages.error('切换状态失败：' + (error.message || '未知错误'))
        // 如果失败，回滚UI状态
        awlData.value.awlStatus = value ? AWL_STATUS.DISABLED : AWL_STATUS.ENABLE
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    console.log('Component mounted, calling loadIpList')
    loadIpList()
})
</script>

<template>
    <div class="awl-config">
        <n-card class="help-card">
            <n-space vertical :size="16">
                <div class="help-header">
                    <n-icon size="24" color="#1890ff">
                        <help-circle-outline />
                    </n-icon>
                    <span class="help-title">IP白名单说明</span>
                </div>

                <div class="help-content">
                    <div class="help-item">
                        <div class="help-item-title">
                            <n-icon color="#52c41a" class="bullet-icon">
                                <ellipse-outline />
                            </n-icon>
                            什么是IP白名单？
                        </div>
                        <p class="help-item-desc">
                            IP白名单用于限制可以访问您应用API的IP地址，只有在白名单中的IP地址才能调用API。
                        </p>
                    </div>

                    <div class="help-item">
                        <div class="help-item-title">
                            <n-icon color="#52c41a" class="bullet-icon">
                                <ellipse-outline />
                            </n-icon>
                            如何使用？
                        </div>
                        <p class="help-item-desc">
                            1. 添需要允许访问的IP地址到白名单中；<br>
                            2. 如果白名单为空，则表示不限制IP访问；<br>
                            3. 建议添加固定的服务器IP地址，以提高安全性。
                        </p>
                    </div>
                </div>

                <n-space justify="end">
                    <n-button text type="primary" @click="window.open('文档URL', '_blank')">
                        查看详细文档
                        <template #icon>
                            <n-icon><arrow-forward-outline /></n-icon>
                        </template>
                    </n-button>
                </n-space>
            </n-space>
        </n-card>

        <n-card title="IP白名单管理" class="config-card">
            <template #header-extra>
                <n-space align="center">
                    <span class="status-text">
                        {{ awlData.awlStatus === AWL_STATUS.ENABLE ? '已开启' : '已关闭' }}
                    </span>
                    <n-switch
                        :value="awlData.awlStatus === AWL_STATUS.ENABLE"
                        @update:value="toggleAwlStatus"
                        :disabled="loading"
                    />
                </n-space>
            </template>
            
            <n-space vertical>
                <n-space align="center">
                    <n-input 
                        v-model:value="newIp" 
                        placeholder="请输入您的服务器出口IP地址，按回车键新增" 
                        style="min-width: 500px"
                        size="large"
                        :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE"
                        @keyup.enter="addIp"
                    />
                    <n-button 
                        type="primary" 
                        @click="addIp"
                        size="large"
                        :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE"
                    >
                        <template #icon>
                            <n-icon><add-outline /></n-icon>
                        </template>
                        添加
                    </n-button>
                </n-space>

                <n-space wrap>
                    <n-tag
                        v-for="item in ipList"
                        :key="item.ip"
                        closable
                        :disabled="awlData.awlStatus !== AWL_STATUS.ENABLE"
                        @close="removeIp(item.ip)"
                    >
                        {{ item.ip }}
                    </n-tag>
                </n-space>
            </n-space>
        </n-card>
    </div>
</template>

<style scoped>
.awl-config {
    padding: 24px;
}

.config-card {
    margin-bottom: 24px;
    min-width: 800px;
}

.help-card {
    margin-bottom: 24px;
    background-color: #fafafa;
}

.help-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.help-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
}

.help-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.help-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.help-item-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.bullet-icon {
    font-size: 8px;
}

.help-item-desc {
    margin: 0;
    padding-left: 20px;
    font-size: 14px;
    color: #595959;
    line-height: 1.6;
}

.status-text {
    font-size: 14px;
    color: #8c8c8c;
}

/* 更新n-tag样式 */
.n-tag {
    font-size: 18px; /* 增大字体 */
    padding: 10px 20px; /* 增大内边距 */
    margin: 6px; /* 增加间距 */
    display: flex;
    align-items: center;
}

.n-tag .n-tag__close {
    margin-left: 12px; /* 增加文本和关闭按之间的间距 */
    cursor: pointer; /* 添加鼠标指针样式 */
}
</style>
