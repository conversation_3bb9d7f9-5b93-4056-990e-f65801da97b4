<template>
  <div class="work-page">
    <n-card>
      <div class="header">
        <div class="left-actions">
          <n-button quaternary circle @click="handleBack">
            <template #icon>
              <n-icon>
                <ArrowLeftOutline />
              </n-icon>
            </template>
          </n-button>
          <span class="page-title">{{ scene }} - 工作页面</span>
        </div>
        <div class="right-actions">
          <n-tooltip
            v-if="settingError && settingErrorMessage"
            :show-arrow="false"
            placement="bottom-end"
          >
            <template #trigger>
              <n-tag
                :type="settingError ? 'error' : 'success'"
                size="medium"
                class="status-tag"
              >
                <template #icon>
                  <n-icon>
                    <component
                      :is="
                        settingError
                          ? CloseCircleOutline
                          : CheckmarkCircleOutline
                      "
                    />
                  </n-icon>
                </template>
                {{ settingError ? settingErrorMessage : "决策设置完毕" }}
              </n-tag>
            </template>
            {{ settingErrorMessage }}
          </n-tooltip>
          <n-tag
            v-else
            :type="settingError ? 'error' : 'success'"
            size="medium"
            class="status-tag"
          >
            <template #icon>
              <n-icon>
                <component
                  :is="
                    settingError ? CloseCircleOutline : CheckmarkCircleOutline
                  "
                />
              </n-icon>
            </template>
            {{ settingError ? settingErrorMessage : "决策设置完毕" }}
          </n-tag>
        </div>
      </div>

      <!-- 工作信息展示区域 -->
      <div class="work-info">
        <!-- 选中的房间和目标 -->
        <div class="section">
          <h3 class="section-title">选中的房间和目标</h3>
          <div class="rooms-grid">
            <n-card
              v-for="room in selectedRooms"
              :key="room"
              class="room-card"
              :class="{ 'room-card--current': robotInfo?.currentRoom === room }"
            >
              <template #header>
                <div class="room-header">
                  <div class="room-info">
                    <span class="room-name">{{
                      getRoomDisplayName(room)
                    }}</span>
                    <n-icon
                      v-if="roomTargets[room]?.isPrimary"
                      class="primary-icon"
                      :component="StarOutline"
                    />
                  </div>
                  <n-tag
                    v-if="robotInfo?.currentRoom === room"
                    type="success"
                    size="small"
                  >
                    机器人当前位置
                  </n-tag>
                </div>
              </template>

              <div class="target-tags">
                <n-tag
                  v-for="target in roomTargets[room]?.targets"
                  :key="target"
                  size="small"
                  class="target-tag"
                >
                  {{ getTargetDisplayName(room, target) }}
                </n-tag>
              </div>
            </n-card>
          </div>
        </div>

        <!-- 机器人信息 -->
        <div class="section" v-if="robotInfo">
          <h3 class="section-title">执行机器人</h3>
          <div class="work-container">
            <!-- 机器人信息 -->
            <div class="robot-info">
              <!-- 机器人图片 -->
              <div class="robot-image-section">
                <div class="robot-image">
                  <img
                    :src="getRobotImage(robotInfo.image)"
                    :alt="robotInfo.name"
                  />
                </div>
              </div>

              <!-- 机器人名称和位置 -->
              <div class="robot-identity">
                <div class="robot-name">{{ robotInfo.name }}</div>
                <div class="robot-location">
                  <n-tag type="success" size="small">
                    当前位置：{{ robotInfo.currentRoom }}
                  </n-tag>
                </div>
              </div>

              <!-- 执行指令 -->
              <div class="command-section">
                <div class="command-box">
                  <div class="command-label">执行指令：</div>
                  <div class="command-content">{{ robotInfo.command }}</div>
                </div>
              </div>
            </div>

            <!-- 修改决策操作区域 -->
            <div class="decision-actions">
              <div class="decision-content">
                <div class="upload-section">
                  <n-upload
                    accept="image/*"
                    :show-file-list="false"
                    @change="handleImageUpload"
                    :before-upload="beforeUpload"
                    :disabled="settingError"
                  >
                    <div
                      class="upload-trigger"
                      :class="{ 'has-image': imageUrl, disabled: settingError }"
                    >
                      <template v-if="!imageUrl">
                        <n-icon size="24" class="upload-icon">
                          <ImageOutline />
                        </n-icon>
                        <p class="upload-text">请上传机器人看到的图片</p>
                      </template>
                      <div class="image-preview" v-else>
                        <img :src="imageUrl" alt="预览图" />
                        <div class="image-actions">
                          <n-button
                            quaternary
                            circle
                            class="remove-image"
                            @click.stop="removeImage"
                          >
                            <template #icon>
                              <n-icon>
                                <TrashOutline />
                              </n-icon>
                            </template>
                          </n-button>
                        </div>
                      </div>
                    </div>
                  </n-upload>
                </div>
                <div class="button-container">
                  <n-button
                    type="primary"
                    :disabled="!imageUrl || loading || settingError"
                    @click="handleDecision"
                    class="decision-button"
                  >
                    {{
                      loading
                        ? "决策中..."
                        : decisionResult.hasStarted
                        ? "继续决策"
                        : "开始决策"
                    }}
                  </n-button>
                  <n-button
                    type="error"
                    @click="refreshPage"
                    class="decision-button"
                  >
                    重置决策
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 修改决策结果展示区域为模态框组件 -->
        <n-modal
          v-model:show="showDecisionResult"
          :mask-closable="false"
          :closable="false"
          :keyboard="false"
          class="decision-modal"
        >
          <n-card title="决策结果" class="modal-card">
            <div class="decision-modal-content">
              <div class="decision-info">
                <div class="decision-result-layout no-scroll">
                  <div class="preview-image">
                    <img :src="decisionResult.imageUrl" alt="决策图片" />
                  </div>

                  <n-card class="task-card">
                    <div class="task-header">
                      <div class="task-info">
                        <div class="task-id-row">
                          <span class="task-id"
                            >任务ID: {{ decisionResult.taskId }}</span
                          >
                          <n-button
                            quaternary
                            circle
                            size="small"
                            class="copy-button"
                            @click="handleCopy(decisionResult.taskId)"
                          >
                            <template #icon>
                              <n-icon>
                                <CopyOutline />
                              </n-icon>
                            </template>
                          </n-button>
                        </div>
                        <div class="trace-id-row" v-if="decisionResult.traceId">
                          <span class="trace-id"
                            >请求ID: {{ decisionResult.traceId }}</span
                          >
                          <n-button
                            quaternary
                            circle
                            size="small"
                            class="copy-button"
                            @click="handleCopy(decisionResult.traceId)"
                          >
                            <template #icon>
                              <n-icon>
                                <CopyOutline />
                              </n-icon>
                            </template>
                          </n-button>
                        </div>
                      </div>
                      <n-tag :type="getStatusType">{{
                        decisionResult.taskStatus
                      }}</n-tag>
                    </div>

                    <div
                      class="task-actions"
                      v-if="decisionResult.robotAction.length > 0"
                    >
                      <div class="action-title">机器人决策路径</div>
                      <div class="action-list">
                        <div
                          v-for="(action, index) in decisionResult.robotAction"
                          :key="index"
                          class="action-item"
                        >
                          {{ index + 1 }}. {{ action }}
                        </div>
                      </div>
                    </div>
                  </n-card>
                </div>
              </div>

              <div class="drawer-footer">
                <n-space justify="center">
                  <n-button type="primary" @click="handleAccept">接受</n-button>
                  <n-button @click="handleReject">拒绝</n-button>
                </n-space>
              </div>
            </div>
          </n-card>
        </n-modal>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { useWorkPageLogic } from "./WorkPage.js";
import {
  ArrowBack as ArrowLeftOutline,
  ImageOutline,
  TrashOutline,
  TimeOutline,
  Copy as CopyOutline,
  Star as StarOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
} from "@vicons/ionicons5";

console.log("WorkPage.vue 组件开始加载");

// 使用提取的逻辑
const {
  // 响应式数据
  scene,
  selectedRooms,
  roomTargets,
  robotInfo,
  decisionCount,
  imageUrl,
  loading,
  decisionResult,
  showDecisionResult,
  taskInfo,
  pollingTimer,
  decisionHistory,
  accessToken,
  accessTokenLoading,
  tokenExpireTime,
  settingError,
  settingErrorMessage,

  // 计算属性
  getStatusType,

  // 方法
  refreshPage,
  getAccessToken,
  checkAccessToken,
  getRobotImage,
  initializeTask,
  handleNavigateToConfig,
  handleBack,
  handleImageUpload,
  beforeUpload,
  removeImage,
  formatTime,
  handleAccept,
  handleReject,
  handleCopy,
  pollTaskStatus,
  handleDecision,
  getRoomDisplayName,
  getTargetDisplayName,
} = useWorkPageLogic();
</script>

<style scoped>
@import "./WorkPage.css";
</style>