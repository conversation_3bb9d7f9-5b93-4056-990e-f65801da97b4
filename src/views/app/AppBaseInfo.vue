<script setup>
import { ref, onMounted, computed, h, watch } from 'vue'
import {
  NCard, NSpace, NInput, NButton, NIcon,
  NTag, NPopconfirm, NModal, NForm, NFormItem,
  useMessage, NTooltip, NAutoComplete, NAvatar
} from 'naive-ui'
import {
  CopyOutline, EyeOutline, EyeOffOutline,
  ReloadOutline, CreateOutline, WarningOutline,
  SwapHorizontalOutline, TrashOutline,
  AccessibilitySharp, LogoChrome, ServerOutline,
  CloudDoneOutline, AnalyticsOutline, BuildOutline,
  BusinessOutline, CartOutline, DocumentTextOutline,
  FileTrayFullOutline, GridOutline, LogoAmplify,
  LogoApple, LogoDesignernews, LogoEdge, LogoElectron,
  HelpCircleOutline, PowerOutline
} from '@vicons/ionicons5'
import { applicationApi } from '@/api/applications'
import { useRoute, useRouter } from 'vue-router'
import AppForm from '@/components/app/AppForm.vue'
import messages from '@/utils/messages'
import SecurityVerifyModal from '@/components/system/SecurityVerifyModal.vue'
import { doPut, doGet } from '@/utils/requests'
import ContactSelectModal from '@/components/contacts/ContactSelectModal.vue'
import { inject } from 'vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const appId = route.params.id
const showSecret = ref(false)
const showEditModal = ref(false)

// 编辑表单
const formRef = ref(null)
const formModel = ref({
  iconName: '',
  iconColor: '',
  appName: '',
  docUrl: ''
})

// 复制文本
const copyText = async (text, type) => {
  try {
    await navigator.clipboard.writeText(text)

    // 如果复制的是 AppID 或 AppSecret，确保它们也保存到 sessionStorage
    if (type === 'AppID' && text) {
      sessionStorage.setItem('app_id', text)
    } else if (type === 'Appsecret' && text) {
      sessionStorage.setItem('app_secret', text)
    }

    messages.success(`${type}已复制到剪贴板`)
  } catch (err) {
    messages.error('复制失败')
  }
}

// 添加状态变量
const showSecurityVerify = ref(false)
const currentAction = ref('')

// 在script setup中添加新的状态变量
const showContactSelect = ref(false)

// 添加防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return (...args) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 修改用户搜索函数
const doUserSearch = async (query) => {
  if (!query) {
    userSearchResults.value = []
    return
  }

  searchingUsers.value = true
  try {
    const response = await doGet(`/auth-center/system/user/list?keyword=${query}`)
    userSearchResults.value = response.data.map(user => ({
      label: user.nickname,
      value: user.id,
      nickname: user.nickname
    }))
  } catch (error) {
    messages.error('搜索用户失败')
    userSearchResults.value = []
  } finally {
    searchingUsers.value = false
  }
}

// 创建防抖版本的搜索函数
const handleUserSearch = debounce(doUserSearch, 100)

// 修改转移所有权函数
const handleTransferOwnership = async () => {
  if (!transferTo.value) {
    messages.error('请选择转移对象')
    return
  }

  try {
    await doPut(`/app-center/app/info/${appId}`, {
      ownerId: transferTo.value
    })
    messages.success('应用转移成功')
    showTransferModal.value = false
    // 转移成功后跳转到应用中心
    router.push('/app/center')
  } catch (error) {
    messages.error('应用转移失败')
  }
}

// 修改安全验证处理函数
const handleSecurityVerify = async ({ verifyCode, action }) => {
  try {
    switch (action) {
      case '重置密钥':
        await handleResetSecret()
        break
      case '转移应用所有权':
        showContactSelect.value = true  // 显示成员选择弹窗
        break
      case '删除应用':
        await handleDeleteApp()
        break
    }
    showSecurityVerify.value = false
  } catch (error) {
    console.error('操作失败:', error)
    messages.error('操作失败')
  }
}

// 重置密钥
const handleResetSecret = async () => {
  try {
    const response = await doPut(`/app-center/app/info/${appId}`, {
      appSecret: 'reset'
    })

    // 如果后端直接返回了新的密钥，则更新 sessionStorage
    if (response.data && response.data.appSecret) {
      sessionStorage.setItem('app_secret', response.data.appSecret)
      console.log('密钥已重置并更新到 sessionStorage')
    }

    messages.success('密钥重置成功')
    // 触发更新事件，让父组件重新加载数据
    emit('update')
  } catch (error) {
    console.error('重置密钥失败:', error)
    messages.error('密钥重置失败')
  }
}

// 删除应用
const handleDeleteApp = async () => {
  try {
    await doPut(`/app-center/app/info/${appId}`, {
      appState: 'DROPPED'
    })

    // 清除 sessionStorage 中的应用凭证
    clearCredentialsFromSessionStorage()

    messages.success('应用删除成功')
    // 修改删除后的跳转路径为根路径
    router.push('/')
  } catch (error) {
    console.error('删除应用失败:', error)
    messages.error('删除应用失败')
  }
}

// 添加图标映射函数
const getIconComponent = (iconName) => {
  const iconMap = {
    'AccessibilitySharp': AccessibilitySharp,
    'LogoChrome': LogoChrome,
    'ServerOutline': ServerOutline,
    'CloudDoneOutline': CloudDoneOutline,
    'AnalyticsOutline': AnalyticsOutline,
    'BuildOutline': BuildOutline,
    'BusinessOutline': BusinessOutline,
    'CartOutline': CartOutline,
    'DocumentTextOutline': DocumentTextOutline,
    'FileTrayFullOutline': FileTrayFullOutline,
    'GridOutline': GridOutline,
    'LogoAmplify': LogoAmplify,
    'LogoApple': LogoApple,
    'LogoDesignernews': LogoDesignernews,
    'LogoEdge': LogoEdge,
    'LogoElectron': LogoElectron
  }
  return iconMap[iconName] || LogoElectron
}

// 处理编辑提交
const handleEditSubmit = async (formData) => {
  try {
    console.log('提交编辑表单:', formData)
    await applicationApi.updateApplication(appId, {
      appName: formData.name,
      appDesc: formData.description,
      iconName: formData.iconName,
      iconColor: formData.iconColor,
      docUrl: formData.docUrl
    })

    showEditModal.value = false
    messages.success('应用信息更新成功')

    // 触发更新事件，让父组件重新加载数据
    emit('update')

  } catch (error) {
    console.error('更新应用失败:', error)
    messages.error('应用信息更新失败')
  }
}

// 添加一个新的方法来处理确认按钮点击
const handlePositiveClick = async () => {
  console.log('点击确认按钮')
  if (formRef.value) {
    await formRef.value.handleSubmit()
  }
}

// 初始化编辑表单数据
const getInitialFormData = () => ({
  name: props.appInfo?.appName || '',
  description: props.appInfo?.description || props.appInfo?.appDesc || '',
  iconName: props.appInfo?.iconName || '',
  iconColor: props.appInfo?.iconColor || '',
  docUrl: props.appInfo?.docUrl || ''
})

// 处理危险操作的点击
const handleDangerousAction = (action) => {
  currentAction.value = action
  showSecurityVerify.value = true
}

// 添加选项渲染函数
const renderUserOption = (option, selected) => {
  return h('div', {
    class: 'user-option'
  }, [
    h(NAvatar, {
      round: true,
      size: 'small',
      src: `https://api.dicebear.com/7.x/pixel-art/svg?seed=${option.nickname}`
    }),
    h('span', {
      class: 'user-nickname'
    }, option.nickname)
  ])
}

// 修改成员选择处理函数
const handleContactSelect = async (selected) => {
  try {
    await doPut(`/app-center/app/info/${appId}`, {
      ownerId: selected[0].id,
      ownerName: selected[0].nickname
    })

    // 清除 sessionStorage 中的应用凭证，因为应用已转移给其他用户
    clearCredentialsFromSessionStorage()

    messages.success('应用转移成功')
    router.push('/')
  } catch (error) {
    console.error('转移应用失败:', error)
    messages.error('应用转移失败')
  }
}

// 修改 props 定义，允许 null 值
const props = defineProps({
  appInfo: {
    type: Object,
    required: false, // 改为 false
    default: () => ({}) // 提供默认值
  }
})

// 添加一个计算属性来判断是否在加载中
const isLoading = computed(() => !props.appInfo)

// 添加 emit 定义
const emit = defineEmits(['update'])

// 保存应用凭证到 sessionStorage
const saveCredentialsToSessionStorage = () => {
  if (props.appInfo?.appId && props.appInfo?.appSecret) {
    // 保存 appId 和 secret 到 sessionStorage
    // sessionStorage 会在用户关闭页面/标签页时自动清除，提供了安全性
    sessionStorage.setItem('app_id', props.appInfo.appId)
    sessionStorage.setItem('app_secret', props.appInfo.appSecret)
    console.log('应用凭证已保存到 sessionStorage')
  }
}

// 清除 sessionStorage 中的应用凭证
const clearCredentialsFromSessionStorage = () => {
  sessionStorage.removeItem('app_id')
  sessionStorage.removeItem('app_secret')
  console.log('应用凭证已从 sessionStorage 中清除')
}

// 监听 appInfo 变化，当获取到应用信息后保存凭证
watch(() => props.appInfo, (newVal) => {
  if (newVal?.appId && newVal?.appSecret) {
    saveCredentialsToSessionStorage()
  }
}, { immediate: true })

// 在组件挂载时保存凭证
onMounted(() => {
  if (props.appInfo?.appId && props.appInfo?.appSecret) {
    saveCredentialsToSessionStorage()
  }
})

// 修改启用/停用控制函数
const handleToggleAppState = async () => {
  try {
    const newState = props.appInfo?.appState === 'DISABLED' ? 'ENABLE' : 'DISABLED'
    await doPut(`/app-center/app/info/${appId}`, {
      appState: newState
    })

    // 根据操作类型显示不同的成功消息
    const actionText = newState === 'ENABLE' ? '启用' : '停用'
    messages.success(`应用${actionText}成功`)

    // 刷新当前页面
    window.location.reload()
  } catch (error) {
    console.error('切换应用状态失败:', error)
    messages.error('操作失败')
  }
}
</script>

<template>
  <!-- 添加加载状态判断 -->
  <div v-if="isLoading" class="loading-state">
    <n-spin size="medium" />
    <span>加载中...</span>
  </div>

  <div v-else class="app-base-info">
    <!-- 应用凭证区域 -->
    <n-card title="应用凭证" class="info-card">
      <n-grid :cols="2" :x-gap="24">
        <n-grid-item>
          <div class="credential-item">
            <span class="label">
              App ID
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-icon class="help-icon">
                    <help-circle-outline />
                  </n-icon>
                </template>
                应用的唯一标识
              </n-tooltip>
            </span>
            <div class="value-container">
              <span class="value">{{ props.appInfo?.appId }}</span>
              <n-button quaternary circle size="small" @click="copyText(props.appInfo?.appId, 'AppID')">
                <template #icon>
                  <n-icon><copy-outline /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </n-grid-item>

        <n-grid-item>
          <div class="credential-item">
            <span class="label">
              App Secret
              <n-tooltip trigger="hover" placement="right">
                <template #trigger>
                  <n-icon class="help-icon">
                    <help-circle-outline />
                  </n-icon>
                </template>
                应用密钥，用于获取app_access_token
              </n-tooltip>
            </span>
            <div class="value-container">
              <span class="value">
                {{ showSecret ? props.appInfo?.appSecret : '********************************' }}
              </span>
              <n-space :size="8">
                <n-button quaternary circle size="small" @click="showSecret = !showSecret">
                  <template #icon>
                    <n-icon>
                      <component :is="showSecret ? EyeOffOutline : EyeOutline" />
                    </n-icon>
                  </template>
                </n-button>
                <n-button quaternary circle size="small" @click="copyText(props.appInfo?.appSecret, 'Appsecret')">
                  <template #icon>
                    <n-icon><copy-outline /></n-icon>
                  </template>
                </n-button>
                <n-popconfirm @positive-click="() => handleDangerousAction('重置密钥')" positive-text="确定"
                  negative-text="取消">
                  <template #trigger>
                    <n-button quaternary circle size="small">
                      <template #icon>
                        <n-icon><reload-outline /></n-icon>
                      </template>
                    </n-button>
                  </template>
                  确定要重置应用密钥吗？重置后需要更新您的应用配置。
                </n-popconfirm>
              </n-space>
            </div>
          </div>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 综合信息区域 -->
    <n-card title="基础信息" class="info-card">
      <template #header-extra>
        <n-button quaternary circle @click="showEditModal = true">
          <template #icon>
            <n-icon><create-outline /></n-icon>
          </template>
        </n-button>
      </template>
      <n-space vertical :size="16">
        <div class="info-item">
          <span class="label">应用图标</span>
          <div class="value-container">
            <n-icon :component="getIconComponent(props.appInfo?.iconName)" :color="props.appInfo?.iconColor" size="48" />
          </div>
        </div>
        <div class="info-item">
          <span class="label">应用名称</span>
          <span class="value">{{ props.appInfo?.appName }}</span>
        </div>
        <div class="info-item">
          <span class="label">文档地址</span>
          <a v-if="props.appInfo?.docUrl" :href="props.appInfo.docUrl" target="_blank" class="doc-link">{{ props.appInfo.docUrl }}</a>
          <span v-else class="empty-value">暂无</span>
        </div>
      </n-space>
    </n-card>

    <!-- 危险操作区域 -->
    <n-card title="危险操作" class="info-card danger-zone">
      <template #header-extra>
        <n-icon color="#ff4d4f"><warning-outline /></n-icon>
      </template>
      <n-space vertical :size="16">
        <!-- 添加启用/停用控制 -->
        <div class="danger-item">
          <div class="danger-info">
            <div class="danger-header">
              <n-icon :color="props.appInfo?.appState === 'DISABLED' ? '#52c41a' : '#ff4d4f'">
                <power-outline />
              </n-icon>
              <span class="danger-title">{{ props.appInfo?.appState === 'DISABLED' ? '启用应用' : '停用应用' }}</span>
            </div>
            <span class="danger-desc">{{ props.appInfo?.appState === 'DISABLED' ? '重新启用此应用' : '暂时停用此应用的所有功能' }}</span>
          </div>
          <n-popconfirm
            @positive-click="handleToggleAppState"
            positive-text="确定"
            negative-text="取消"
          >
            <template #trigger>
              <n-button secondary :type="props.appInfo?.appState === 'DISABLED' ? 'success' : 'warning'">
                {{ props.appInfo?.appState === 'DISABLED' ? '启用' : '停用' }}
              </n-button>
            </template>
            {{ props.appInfo?.appState === 'DISABLED' ? '确定要启用此应用吗？' : '确定要停用此应用吗？停用后应用将立即禁止访问。' }}
          </n-popconfirm>
        </div>
        <div class="danger-item">
          <div class="danger-info">
            <div class="danger-header">
              <n-icon color="#faad14"><swap-horizontal-outline /></n-icon>
              <span class="danger-title">转移应用所有权</span>
            </div>
            <span class="danger-desc">将应用转移给其他用户</span>
          </div>
          <n-popconfirm @positive-click="() => handleDangerousAction('转移应用所有权')" positive-text="确定" negative-text="取消">
            <template #trigger>
              <n-button secondary type="warning">
                转移所有权
              </n-button>
            </template>
            确定要转移应用所有权吗？转移后您将失去对应用的所有权。
          </n-popconfirm>
        </div>
        <div class="danger-item">
          <div class="danger-info">
            <div class="danger-header">
              <n-icon color="#ff4d4f"><trash-outline /></n-icon>
              <span class="danger-title">删除应用</span>
            </div>
            <span class="danger-desc">删除后无法恢复，请谨慎操作</span>
          </div>
          <n-popconfirm @positive-click="() => handleDangerousAction('删除应用')" positive-text="确定" negative-text="取消">
            <template #trigger>
              <n-button secondary type="error">
                删除应用
              </n-button>
            </template>
            确定要删除应用吗？此操作不可逆。
          </n-popconfirm>
        </div>
      </n-space>
    </n-card>
  </div>

  <!-- 修改编辑弹窗 -->
  <n-modal v-model:show="showEditModal" preset="dialog" title="编辑应用信息" positive-text="确定" negative-text="取消"
    style="width: 680px" @positive-click="handlePositiveClick" @negative-click="() => showEditModal = false">
    <app-form ref="formRef" :initial-data="getInitialFormData()" @submit="handleEditSubmit"
      @cancel="() => showEditModal = false" />
  </n-modal>

  <!-- 添加安全验证弹窗 -->
  <security-verify-modal v-model:show="showSecurityVerify" :action="currentAction" @verify="handleSecurityVerify" />

  <!-- 添加成员选择弹窗 -->
  <contact-select-modal v-model:show="showContactSelect" title="转移应用给其他成员" description="转移后，你将无法查看和管理此应用" label="转移对象"
    :multiple="false" @confirm="handleContactSelect" />
</template>

<style scoped>
.app-base-info {
  padding: 24px;
}

.info-card {
  margin-bottom: 24px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.credential-item,
.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  color: #8c8c8c;
}

.value-container {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
  height: 48px;
}

.value {
  font-size: 14px;
  color: #262626;
  font-family: monospace;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.empty-value {
  color: #bfbfbf;
  font-size: 14px;
}

.doc-link {
  color: #1890ff;
  text-decoration: none;
}

.doc-link:hover {
  text-decoration: underline;
}

.danger-zone {
  border: 1px solid #ff4d4f15;
}

.danger-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.danger-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.danger-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.danger-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.danger-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.danger-desc {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 32px;
  /* 与图标对齐 */
}

.help-icon {
  font-size: 14px;
  margin-left: 4px;
  cursor: help;
  vertical-align: -0.125em;
  /* 微调图标的垂直对齐 */
}

/* 添加转移对话框相关样式 */
.transfer-content {
  padding: 16px 0;
}

.transfer-warning {
  font-size: 14px;
  color: #ff4d4f;
  margin: 0 0 24px 0;
}

.transfer-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.transfer-input .label {
  font-size: 14px;
  color: #262626;
  white-space: nowrap;
}

/* 调整自动完成组件的宽度 */
.transfer-input :deep(.n-auto-complete) {
  flex: 1;
}

/* 添加用户选项样式 */
.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.user-nickname {
  font-size: 14px;
  color: #262626;
}

/* 调整下拉选项的样式 */
:deep(.n-base-selection) {
  width: 100%;
}

:deep(.n-base-selection-input) {
  width: 100%;
}

/* 添加加载状态的样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
  color: #8c8c8c;
}
</style>