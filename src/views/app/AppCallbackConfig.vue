<script setup>
import { ref, onMounted } from 'vue'
import {
    NCard, NSpace, NInput, NButton, NIcon,
    NPopconfirm, useMessage, NForm, NFormItem,
    NSwitch, NTooltip
} from 'naive-ui'
import {
    SaveOutline, RefreshOutline,
    HelpCircleOutline, LinkOutline,
    EllipseOutline, ArrowForwardOutline, CreateOutline,
    EyeOutline, EyeOffOutline, CopyOutline
} from '@vicons/ionicons5'
import { useRoute } from 'vue-router'
import { applicationApi } from '@/api/applications'
import messages from '@/utils/messages'

const route = useRoute()
const message = useMessage()
const appId = route.params.id

// 回调配置数据
const callbackConfig = ref({
    callbackUrl: '',
    callbackEncryptKey: ''
})

// 加载回调配置
const loadCallbackConfig = async () => {
    try {
        const response = await applicationApi.getAppCallbackConfig(appId)
        console.log('API Response:', response)

        const { callbackUrl, aesKey } = response
        console.log('Parsed data:', { callbackUrl, aesKey })

        callbackConfig.value = {
            callbackUrl: callbackUrl || '',
            callbackEncryptKey: aesKey || '',
            callbackId: response.id
        }

        tempValues.value = {
            url: callbackUrl || '',
            encryptKey: aesKey || ''
        }

        console.log('Updated config:', callbackConfig.value)
    } catch (error) {
        console.error('Load callback config error:', error)
        messages.error('加载回调配置失败')
    }
}

// 保存回调配置
const saveCallbackConfig = async () => {
    try {
        const updateData = {
            callbackUrl: callbackConfig.value.callbackUrl,
            aesKey: callbackConfig.value.callbackEncryptKey
        }
        console.log('Save data:', updateData)

        await applicationApi.updateApplication(appId, updateData)
        messages.success('保存成功')
    } catch (error) {
        console.error('Save config error:', error)
        messages.error('保存失败')
    }
}

// 添加生成随机字符串的函数
const generateRandomKey = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    const length = 32
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
}

// 修改重置回调密钥函数
const resetCallbackSecret = async () => {
    const newKey = generateRandomKey()
    await applicationApi.updateAppCallbackConfig({
        id: callbackConfig.value.callbackId,
        aesKey: newKey
    })
    
    callbackConfig.value.callbackEncryptKey = newKey
    messages.success('重置成功')
}

// 测试回调连接
const testCallback = async () => {
    try {
        await applicationApi.testCallback(appId)
        messages.success('连接测试成功')
    } catch (error) {
        messages.error('连接测试失败')
    }
}

// 添加编辑状态控制
const editState = ref({
    url: false
})

// 添加临时编辑值
const tempValues = ref({
    url: ''
})

// 开始编辑
const startEdit = (field) => {
    if (field === 'url') {
        tempValues.value.url = callbackConfig.value.callbackUrl
        editState.value.url = true
    }
}

// 取消编辑
const cancelEdit = (field) => {
    if (field === 'url') {
        editState.value.url = false
    }
}

// 保存单个字段
const saveField = async (field) => {
    if (field !== 'url') return

    // URL 非空验证
    if (!tempValues.value.url?.trim()) {
        messages.error('回调地址不能为空')
        return
    }

    // 先验证回调地址
    const validateResponse = await applicationApi.validateCallback({
        aesKey: callbackConfig.value.callbackEncryptKey,
        callbackUrl: tempValues.value.url
    })

    // 检查验证结果
    if (validateResponse.code !== 0) {
        messages.error(validateResponse.message || '回调地址验证失败')
        return
    }

    // 验证通过后保存，只传入 URL 相关参数
    await applicationApi.updateAppCallbackConfig({
        id: callbackConfig.value.callbackId,
        callbackUrl: tempValues.value.url
    })

    // 更新本地数据
    callbackConfig.value.callbackUrl = tempValues.value.url
    editState.value.url = false
    messages.success('保存成功')
}

// 添加显示密钥状态控制
const showSecret = ref(false)

// 添加复制功能
const copySecret = () => {
    if (callbackConfig.value.callbackEncryptKey) {
        navigator.clipboard.writeText(callbackConfig.value.callbackEncryptKey)
        messages.success('复制成功')
    }
}

onMounted(loadCallbackConfig)
</script>

<template>
    <div class="callback-config">
        <n-card class="help-card">
            <n-space vertical :size="16">
                <div class="help-header">
                    <n-icon size="24" color="#1890ff">
                        <help-circle-outline />
                    </n-icon>
                    <span class="help-title">回调配置说明</span>
                </div>

                <div class="help-content">
                    <div class="help-item">
                        <div class="help-item-title">
                            <n-icon color="#52c41a" class="bullet-icon">
                                <ellipse-outline />
                            </n-icon>
                            什么是回调？
                        </div>
                        <p class="help-item-desc">
                            当您通过开放API下发的任务执行完毕后，我们会向您配置的回调地址发送任务执行结果。
                        </p>
                    </div>

                    <div class="help-item">
                        <div class="help-item-title">
                            <n-icon color="#52c41a" class="bullet-icon">
                                <ellipse-outline />
                            </n-icon>
                            如何接收回调？
                        </div>
                        <p class="help-item-desc">
                            1. 配置一个可以接收POST请求的URL地址（建议使用https协议，不支持IP:PORT方式）；<br>
                            2. 使用下面配置的加密密钥来解密回调数据； <br>
                            3. 当您准备妥当时，将回调地址更新在下面，点击保存；<br>
                            4. 点击保存时，会向您的回调地址发送一个测试请求，请确保按文档内容进行响应，否则可能导致回调失败。
                        </p>
                    </div>

                    <div class="help-item">
                        <div class="help-item-title">
                            <n-icon color="#52c41a" class="bullet-icon">
                                <ellipse-outline />
                            </n-icon>
                            安全建议
                        </div>
                        <p class="help-item-desc">
                            请妥善保管加密的密钥，建议定期更换。
                        </p>
                    </div>
                </div>

                <n-space justify="end">
                    <n-button text type="primary" @click="window.open('文档URL', '_blank')">
                        查看详细文档
                        <template #icon>
                            <n-icon><arrow-forward-outline /></n-icon>
                        </template>
                    </n-button>
                </n-space>
            </n-space>
        </n-card>

        <n-card title="回调配置" class="config-card">

            <n-form label-placement="left" label-width="120">
                <n-form-item label="回调地址">
                    <div class="input-with-test">
                        <div class="input-with-edit">
                            <n-input v-if="editState.url" v-model:value="tempValues.url" placeholder="请输入回调地址" clearable
                                class="callback-url-input" />
                            <n-input v-else :value="callbackConfig.callbackUrl" readonly placeholder="请输入回调地址"
                                class="callback-url-input" />
                            <div class="edit-actions" v-if="editState.url">
                                <n-button text style="color: #18a058" @click="saveField('url')">
                                    保存
                                </n-button>
                                <n-button text style="color: #d03050" @click="cancelEdit('url')">
                                    取消
                                </n-button>
                            </div>
                            <n-button v-else quaternary circle size="small" @click="startEdit('url')">
                                <template #icon>
                                    <n-icon><create-outline /></n-icon>
                                </template>
                            </n-button>
                        </div>
                    </div>
                </n-form-item>

                <n-form-item label="加密密钥">
                    <div class="input-group">
                        <span class="secret-text">
                            {{ showSecret ? callbackConfig.callbackEncryptKey : '********************************' }}
                        </span>
                        <n-button quaternary circle size="small" @click="showSecret = !showSecret">
                            <template #icon>
                                <n-icon>
                                    <component :is="showSecret ? EyeOffOutline : EyeOutline" />
                                </n-icon>
                            </template>
                        </n-button>
                        <n-button quaternary circle size="small" @click="copySecret">
                            <template #icon>
                                <n-icon><copy-outline /></n-icon>
                            </template>
                        </n-button>
                        <n-popconfirm @positive-click="resetCallbackSecret" positive-text="确定" negative-text="取消">
                            <template #trigger>
                                <n-button quaternary circle size="small">
                                    <template #icon>
                                        <n-icon><refresh-outline /></n-icon>
                                    </template>
                                </n-button>
                            </template>
                            确定要重置加密密钥吗？重置后需要更新您的回调服务配置。
                        </n-popconfirm>
                    </div>
                </n-form-item>
            </n-form>
        </n-card>
    </div>
</template>

<style scoped>
.callback-config {
    padding: 24px;
}

.config-card {
    margin-bottom: 24px;
    min-width: 800px;
}

.input-with-test {
    display: flex;
    gap: 12px;
    width: 60%;
    flex-wrap: wrap;
}

.input-with-test :deep(.n-input) {
    flex: 1;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group :deep(.n-input) {
    flex: 1;
    min-width: 300px;
}

.help-icon {
    font-size: 16px;
    color: #8c8c8c;
    cursor: help;
}

:deep(.n-form-item .n-form-item-label) {
    font-weight: 500;
}

.help-card {
    margin-bottom: 24px;
    background-color: #fafafa;
}

.help-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.help-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
}

.help-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.help-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.help-item-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.bullet-icon {
    font-size: 8px;
}

.help-item-desc {
    margin: 0;
    padding-left: 20px;
    font-size: 14px;
    color: #595959;
    line-height: 1.6;
}

.callback-url-input {
    min-width: 200px;
}

.input-with-edit {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 200px;
}

.edit-actions {
    display: flex;
    gap: 4px;
}

.secret-text {
    font-family: monospace;
    font-size: 14px;
    color: #262626;
    flex: 1;
    min-width: 300px;
    padding: 4px 0;
}

/* 调整只读输入框的样式 */
:deep(.n-input--disabled) {
    background-color: transparent;
}

:deep(.n-input--disabled .n-input__border),
:deep(.n-input--disabled .n-input__state-border) {
    border: none;
}

:deep(.n-input-wrapper) {
    background-color: transparent;
}
</style>
