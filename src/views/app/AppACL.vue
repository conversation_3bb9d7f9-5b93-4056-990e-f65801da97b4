<template>
  <div class="page-container">
    <n-card>
      <div class="header">
        <h2>API 访问控制</h2>
        <p class="description">管理应用可以访问的 API 接口及其权限</p>
      </div>

      <div class="content-layout">
        <!-- 左侧能力列表 -->
        <div class="ability-list">
          <template v-if="abilityList.length > 0">
            <div
              v-for="ability in abilityList"
              :key="ability.id"
              class="ability-item"
              :class="{ 'ability-item--active': selectedAbilityId === ability.id }"
              @click="handleAbilitySelect(ability.id)"
            >
              <span class="ability-name">{{ ability.abilityName }}</span>
            </div>
          </template>
          <n-empty v-else description="暂无可用能力" />
        </div>

        <!-- 右侧 API 列表 -->
        <div class="api-list">
          <n-data-table
            :columns="columns"
            :data="aclList"
            :loading="loading"
          />
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, h } from 'vue'
import { useDialog } from 'naive-ui'
import { doGet, doPost, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'
import { NEmpty, NIcon } from 'naive-ui'
import { OpenOutline  } from '@vicons/ionicons5'

const currentAppId = inject('currentAppId')
const dialog = useDialog()

// 能力列表相关
const abilityList = ref([])
const selectedAbilityId = ref(null)

// 数据相关
const loading = ref(false)
const aclList = ref([])

// 加载应用已获取的能力列表
const loadAbilityList = async () => {
  try {
    const res = await doGet(`/app-center/app/acl/${currentAppId.value}/abilities`)
    if (res.code === 0) {
      abilityList.value = res.data
      // 默认选中第一个能力
      if (abilityList.value.length > 0) {
        selectedAbilityId.value = abilityList.value[0].id
        loadACLList()
      }
    }
  } catch (error) {
    messages.error('获取能力列表失败')
  }
}

// 处理能力选择
const handleAbilitySelect = (abilityId) => {
  selectedAbilityId.value = abilityId
  loadACLList()
}

// 加载 ACL 列表
const loadACLList = async () => {
  if (!selectedAbilityId.value) return
  
  loading.value = true
  try {
    const res = await doGet(
      `/app-center/app/acl/${currentAppId.value}/ability/${selectedAbilityId.value}/apis`
    )
    if (res.code === 0) {
      aclList.value = res.data
    }
  } finally {
    loading.value = false
  }
}

const pendingApiId = ref(null)

const columns = [
  {
    title: '权限信息',
    key: 'api_info',
    render(row) {
      return h('div', { class: 'api-info' }, [
        h('div', { class: 'api-name', title: row.api_name }, row.api_name),
        h('div', { class: 'api-code', title: row.api_code }, row.api_code)
      ])
    }
  },
  {
    title: '权限说明',
    key: 'api_desc',
    ellipsis: {
      tooltip: true
    },
    render(row) {
      if (!row.doc_url) {
        return row.api_desc || '-'
      }
      return h('div', { class: 'desc-wrapper' }, [
        h(
          'a',
          {
            class: 'desc-link',
            onClick: (e) => {
              e.preventDefault()
              window.open(row.doc_url, '_blank')
            }
          },
          [
            h('span', { class: 'api-desc' }, row.api_desc || '-'),
            h(NIcon, { size: 18, class: 'help-icon' }, {
              default: () => h(OpenOutline)
            })
          ]
        )
      ])
    }
  },
  {
    title: '权限状态',
    key: 'usable',
    render(row) {
      return h('div', { class: 'status-wrapper' }, [
        h('span', { 
          class: ['status-dot', row.usable === 1 ? 'status-dot--active' : 'status-dot--inactive'] 
        }),
        h('span', { class: 'status-text' }, row.usable === 1 ? '已开通' : '未开通')
      ])
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(
        'div',
        { 
          class: ['action-text', row.usable === 1 ? 'action-text--cancel' : 'action-text--enable'],
          onClick: () => handleStatusChange(row)
        },
        row.usable === 1 ? '取消' : '开通'
      )
    }
  }
]

const handleStatusChange = async (row) => {
  if (row.usable === 1) {
    // 取消授权时显示确认对话框
    dialog.warning({
      title: '确认取消授权',
      content: '取消授权后，应用将无法继续调用该接口，是否确认？',
      positiveText: '确认取消',
      negativeText: '保持授权',
      onPositiveClick: async () => {
        try {
          const res = await doDelete(`/app-center/app/acl/${currentAppId.value}/api/${row.id}`)
          if (res.code === 0) {
            messages.success('取消授权成功')
            await loadACLList()
          }
        } catch (error) {
          messages.error('取消授权失败')
        }
      }
    })
  } else {
    // 开通授权时显示确认对话框
    dialog.info({
      title: '确认开通授权',
      content: '开通授权后，应用将可以调用该接口，是否确认？',
      positiveText: '确认开通',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          const res = await doPost(`/app-center/app/acl/${currentAppId.value}/api/${row.id}`)
          if (res.code === 0) {
            messages.success('开通授权成功')
            await loadACLList()
          }
        } catch (error) {
          messages.error('开通授权失败')
        }
      }
    })
  }
}

onMounted(() => {
  loadAbilityList()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.description {
  margin: 8px 0 0;
  color: #666;
  font-size: 14px;
}

.content-layout {
  display: flex;
  gap: 24px;
  margin-top: 24px;
}

.ability-list {
  width: 240px;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.ability-item {
  height: 44px;
  padding: 0 16px;
  margin: 4px 0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.ability-item:hover {
  background-color: #f3f3f3;
}

.ability-item--active {
  background-color: #e8f7f2;
  color: #18a058;
  font-weight: 500;
}

.ability-item--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #18a058;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.ability-name {
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空状态样式 */
.ability-list :deep(.n-empty) {
  padding: 24px 0;
}

.api-list {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow-x: auto;
}

/* 确保表格内容不换行 */
:deep(.n-data-table .n-data-table-table) {
  width: auto;
  min-width: 100%;
  white-space: nowrap;
}

.api-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 400px;
}

.api-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.api-code {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-link {
  color: #2080f0;
  text-decoration: none;
}

.doc-link:hover {
  text-decoration: underline;
}

/* 修改状态样式 */
:deep(.status-wrapper) {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

:deep(.status-dot) {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

:deep(.status-dot--active) {
  background-color: #18a058;
}

:deep(.status-dot--inactive) {
  background-color: #bbb;
}

:deep(.status-text) {
  font-size: 13px;
  color: #666;
}

/* 修改操作按钮式 */
:deep(.action-text) {
  font-size: 13px;
  cursor: pointer;
  transition: opacity 0.2s;
}

:deep(.action-text--cancel) {
  color: #bbb;  /* 改为灰色，与未开通状态的圆点颜色一致 */
}

:deep(.action-text--enable) {
  color: #18a058;  /* 保持绿色，与已开通状态的圆点颜色一致 */
}

:deep(.action-text:hover) {
  opacity: 0.8;
}

.desc-wrapper {
  display: flex;
  align-items: center;
}

:deep(.desc-link) {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #333;
  text-decoration: none;
  border-bottom: 1px dashed #18a058;
  padding-bottom: 1px;
  cursor: pointer;
  max-width: fit-content;
}

:deep(.api-desc) {
  font-size: 13px;
}

:deep(.help-icon) {
  color: #18a058;
  margin-left: 4px;
}

:deep(.desc-link:hover) {
  opacity: 0.8;
}
</style>
