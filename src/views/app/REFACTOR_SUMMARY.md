# PerceptionTrial.vue 重构总结

## 重构目标
将 `PerceptionTrial.vue` 文件中的 script 部分提取为独立的 JavaScript 文件，提高代码的可维护性和复用性。

## 重构内容

### 1. 创建组合式函数
- **文件位置**: `src/views/app/composables/usePerceptionTrial.js`
- **功能**: 包含了原 Vue 组件中的所有逻辑代码

### 2. 提取的内容
#### 状态管理
- 基础状态：`is_ability_added`, `image_url`, `selected_function`, `object_names`, `questions`, `function_options`, `current_request`
- 访问凭证状态：`accessToken`, `accessTokenLoading`, `tokenExpireTime`
- 图片相关状态：`active_tab`, `rgb_image_url`, `rgbd_image_url`
- 任务相关状态：`loading`, `taskResult`, `polling`, `submitting`, `taskInfo`, `pollingCount`, `actionLoading`
- 大图预览状态：`showModal`, `currentLargeImage`

#### 计算属性
- `showActionButton`: 是否显示操作按钮
- `getTaskResultTitle`: 获取任务结果标题
- `can_submit`: 是否可以提交
- `shouldDisableQuestions`: 是否禁用问题输入
- `getImagesList`: 获取图片列表
- `getStatusType`: 获取状态类型

#### 方法
- **文件处理**: `handleFileUpload`, `beforeUpload`, `handleDrop`, `handleRGBDrop`, `handleRGBDDrop`
- **图片管理**: `removeImage`, `removeRGBImage`, `removeRGBDImage`, `handleImageUpload`, `handleRGBImageUpload`, `handleRGBDImageUpload`, `uploadImage`
- **任务管理**: `handleSubmit`, `handleCancelTask`, `startPolling`, `retryPolling`
- **能力管理**: `handleAddAbility`, `handleRemoveAbility`
- **工具方法**: `copyToClipboard`, `showLargeImage`
- **API相关**: `loadFunctionOptions`, `getAccessToken`, `checkAccessToken`

#### 生命周期钩子
- `onMounted`: 组件挂载时的初始化逻辑
- `onUnmounted`: 组件卸载时的清理逻辑
- `watch`: 监听功能选择变化

### 3. Vue 组件简化
- **文件**: `src/views/app/PerceptionTrial.vue`
- **保留内容**: 
  - 模板部分（完全不变）
  - props 定义
  - emits 定义
  - 样式引用
- **新增内容**:
  - 导入组合式函数
  - 导入必要的图标组件
  - 解构组合式函数返回的所有状态和方法

## 重构优势

### 1. 代码分离
- 逻辑代码与模板代码分离
- 提高代码可读性和维护性

### 2. 复用性
- 组合式函数可以在其他组件中复用
- 便于单元测试

### 3. 模块化
- 每个功能模块职责清晰
- 便于团队协作开发

### 4. 类型安全
- 更容易添加 TypeScript 支持
- 更好的 IDE 智能提示

## 文件结构
```
src/views/app/
├── PerceptionTrial.vue          # Vue 组件（模板 + 简化的 script）
├── composables/
│   └── usePerceptionTrial.js    # 组合式函数（所有逻辑）
└── styles/
    └── perception-trial.scss    # 样式文件
```

## 使用方式
```javascript
// 在 Vue 组件中使用
import { usePerceptionTrial } from "./composables/usePerceptionTrial.js";

const {
  // 所有状态和方法
  is_ability_added,
  handleSubmit,
  // ... 其他
} = usePerceptionTrial(props);
```

## 注意事项
1. 组合式函数需要接收 `props` 参数
2. 所有原有功能保持不变
3. 模板中的引用无需修改
4. IDE 可能会显示"未使用"警告，这是正常的，因为变量在模板中使用

## 测试验证
- ✅ 构建成功
- ✅ 开发服务器启动正常
- ✅ 所有功能逻辑完整迁移
