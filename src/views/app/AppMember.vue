<script setup>
import { ref, onMounted, h, inject } from 'vue'
import { NCard, NDataTable, NSpace, NButton, NIcon, NTag, useDialog } from 'naive-ui'
import { PersonAddOutline } from '@vicons/ionicons5'
import { useRoute } from 'vue-router'
import { doGet, doPut, doPost, doDelete } from '@/utils/requests'
import messages from '@/utils/messages'
import ContactSelectModal from '@/components/contacts/ContactSelectModal.vue'
import { applicationApi } from '@/api/applications'

const route = useRoute()
const appId = route.params.id
const loading = ref(false)
const members = ref([])
const showContactSelect = ref(false)
const currentAction = ref('')

// 注入currentAppId
const currentAppId = inject('currentAppId')

// 添加批量操作相关的状态
const checkedRowKeys = ref([])

// 定义表格列
const columns = [
  {
    type: 'selection',
    disabled: (row) => row.roleId === 'OWNER',  // 所有者不能被选择
    align: 'center'
  },
  {
    title: '姓名',
    key: 'memberName',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '角色',
    key: 'roleName',
    align: 'center',
    render(row) {
      return h(NTag, getRoleTagProps(row.roleName), {
        default: () => row.roleName
      })
    }
  },
  {
    title: '加入时间',
    key: 'createTime',
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    render(row) {
      return h(
        NSpace,
        { align: 'center', justify: 'center' },
        {
          default: () => [
            row.roleId === 'OWNER' ? 
              h(
                NButton,
                {
                  size: 'small',
                  quaternary: true,
                  type: 'warning',
                  onClick: () => handleTransferOwner(row)
                },
                { default: () => '转移' }
              ) :
              h(
                NButton,
                {
                  size: 'small',
                  quaternary: true,
                  type: 'error',
                  onClick: () => handleRemoveMember(row)
                },
                { default: () => '移除' }
              )
          ]
        }
      )
    }
  }
]

// 修改角色列的渲染函数
const getRoleTagProps = (roleName) => {
  const baseProps = {
    size: 'small',
    round: true,
    bordered: false
  }

  // 根据不同角色设置不同的样式
  switch (roleName) {
    case '所有者':
      return {
        ...baseProps,
        type: 'warning',
        color: {
          color: '#faad14',
          textColor: '#fff',
          borderColor: '#faad14'
        }
      }
    case '管理':
      return {
        ...baseProps,
        type: 'error',
        color: {
          color: '#ff4d4f',
          textColor: '#fff',
          borderColor: '#ff4d4f'
        }
      }
    case '开发':
      return {
        ...baseProps,
        type: 'info',
        color: {
          color: '#2080f0',
          textColor: '#fff',
          borderColor: '#2080f0'
        }
      }
    case '测试':
      return {
        ...baseProps,
        type: 'success',
        color: {
          color: '#18a058',
          textColor: '#fff',
          borderColor: '#18a058'
        }
      }
    default:
      return baseProps
  }
}

// 加载成员列表
const loadMembers = async () => {
  loading.value = true
  try {
    const response = await doGet(`/app-center/app/member/list?appId=${appId}`)
    members.value = response.data
  } catch (error) {
    messages.error('加载成员列表失败')
  } finally {
    loading.value = false
  }
}

// 添加成员按钮点击
const handleAddMember = () => {
  currentAction.value = 'add'
  showContactSelect.value = true
}

// 修改移除成员函数
const handleRemoveMember = (member) => {
  if (member.roleId === 'OWNER') return

  // 显示确认对话框
  window.$dialog.warning({
    title: '移除成员',
    content: `确定要移除成员 ${member.memberName} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await doDelete(`/app-center/app/member?ids=${member.id}`)
        messages.success('移除成员成功')
        await loadMembers()
        // 清空选择
        checkedRowKeys.value = []
      } catch (error) {
        console.error('Failed to remove member:', error)
        messages.error('移除成员失败')
      }
    }
  })
}

// 修改批量移除函数
const handleBatchRemove = () => {
  if (checkedRowKeys.value.length === 0) {
    messages.warning('请选择要移除的成员')
    return
  }

  // 显示确认对话框
  window.$dialog.warning({
    title: '批量移除成员',
    content: `确定要移除选中的 ${checkedRowKeys.value.length} 名成员吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await doDelete(`/app-center/app/member?ids=${checkedRowKeys.value.join(',')}`)
        messages.success('批量移除成员成功')
        await loadMembers()
        // 清空选择
        checkedRowKeys.value = []
      } catch (error) {
        console.error('Failed to batch remove members:', error)
        messages.error('批量移除成员失败')
      }
    }
  })
}

// 添加转移所有者处理函数
const handleTransferOwner = (member) => {
  currentAction.value = 'transfer'
  showContactSelect.value = true
}

// 修改添加成员选择处理函数
const handleContactSelect = async (selected) => {
  if (currentAction.value === 'add') {
    try {
      console.log('Current App ID:', currentAppId.value)
      if (!currentAppId.value) {
        messages.error('应用ID未找到')
        return
      }

      const promises = selected.map(user => {
        const params = {
          memberId: user.id,
          memberName: user.nickname,
          appId: currentAppId.value,
          roleId: 'DEVELOPER',
          roleName: '开发'
        }
        console.log('Adding member with params:', params)
        return doPost('/app-center/app/member', params)
      })
      
      await Promise.all(promises)
      messages.success('添加成员成功')
      await loadMembers()
    } catch (error) {
      console.error('Failed to add members:', error)
      messages.error('添加成员失败')
    }
  } else if (currentAction.value === 'transfer') {
    try {
      await applicationApi.updateApplication(currentAppId.value, {
        ownerId: selected[0].id,
        ownerName: selected[0].nickname
      })
      messages.success('转移所有者成功')
      await loadMembers()
    } catch (error) {
      messages.error('转移所有者失败')
    }
  }
}

// 在setup中初始化dialog
const dialog = useDialog()
window.$dialog = dialog

onMounted(async () => {
  if (!currentAppId.value) {
    console.warn('currentAppId is not set')
  }
  await loadMembers()
})
</script>

<template>
  <div class="app-member">
    <n-card title="成员管理">
      <template #header-extra>
        <n-space>
          <n-button
            v-if="checkedRowKeys.length > 0"
            type="error"
            @click="handleBatchRemove"
          >
            批量移除
          </n-button>
          <n-button type="primary" @click="handleAddMember">
            <template #icon>
              <n-icon>
                <person-add-outline />
              </n-icon>
            </template>
            添加成员
          </n-button>
        </n-space>
      </template>

      <n-data-table
        :columns="columns"
        :data="members"
        :loading="loading"
        :pagination="{ pageSize: 10 }"
        :bordered="false"
        @update:checked-row-keys="checkedRowKeys = $event"
        :checked-row-keys="checkedRowKeys"
        :row-key="(row) => row.id"
      />
    </n-card>

    <!-- 添加成员选择弹窗 -->
    <contact-select-modal
      v-model:show="showContactSelect"
      :title="currentAction === 'add' ? '添加成员' : '转移所有者'"
      :description="currentAction === 'add' ? '最多可选择10名成员' : '请选择要转移给的成员'"
      :label="currentAction === 'add' ? '成员' : '转移对象'"
      :multiple="currentAction === 'add'"
      :max-selected="currentAction === 'add' ? 10 : 1"
      @confirm="handleContactSelect"
    />
  </div>
</template>

<style scoped>
.app-member {
  padding: 24px;
}
</style> 