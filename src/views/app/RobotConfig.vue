<template>
  <div class="robot-config">
    <n-card>
      <div class="header">
        <div class="left-actions">
          <n-button quaternary circle @click="handleBack">
            <template #icon>
              <n-icon>
                <ArrowLeftOutline />
              </n-icon>
            </template>
          </n-button>
          <span class="page-title">{{ scene }} - 配置机器人</span>
        </div>
      </div>

      <!-- 房间和目标信息展示 -->
      <div class="selected-info">
        <div class="room-section">
          <div class="rooms-grid">
            <n-card v-for="room in selectedRooms" :key="room" class="room-card"
              :class="{ 'room-card--selected': currentRoom === room }" @click="handleRoomChange(room)">
              <template #header>
                <div class="room-header">
                  <div class="room-info">
                    <span class="room-name">{{ room }}</span>
                    <n-icon v-if="roomTargets[room]?.isPrimary" class="primary-icon" :component="StarOutline" />
                  </div>
                  <n-tag v-if="currentRoom === room" type="success" size="small">
                    当前位置
                  </n-tag>
                </div>
              </template>

              <div class="target-tags">
                <n-tag v-for="target in roomTargets[room]?.targets" :key="target" size="small" class="target-tag">
                  {{ target }}
                </n-tag>
              </div>
            </n-card>
          </div>
        </div>
      </div>

      <!-- 机器人选择区域 -->
      <div class="robots-grid">
        <n-card v-for="robot in robots" :key="robot.name" class="robot-card" :class="{
          'robot-card--selected': selectedRobot === robot.name,
          'robot-card--disabled': robot.disabled
        }" @click="!robot.disabled && toggleRobot(robot)">
          <div class="robot-image">
            <img :src="getRobotImage(robot.image)" :alt="robot.name">
          </div>
          <div class="robot-name">{{ robot.name }}</div>
        </n-card>
      </div>

      <!-- 语言指令输入区域 -->
      <div class="command-section" v-if="selectedRobot">
        <n-divider>语言指令</n-divider>
        <div class="command-input-wrapper">
          <div class="robot-prompt">
            机器人：
          </div>

          <n-input v-model:value="commandInput" type="textarea" placeholder="请输入语言指令，描述机器人需要执行的任务，例如：请帮我打开客厅的灯"
            :autosize="{ minRows: 3, maxRows: 6 }" />
          <div class="command-actions">
            <n-button type="primary" :disabled="!commandInput.trim()" @click="handleCommandSubmit">
              开始工作

            </n-button>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowBack as ArrowLeftOutline, Star as StarOutline } from '@vicons/ionicons5'
import robotsData from '@/assets/data/robots.json'
import { useMessage } from 'naive-ui'

const router = useRouter()
const route = useRoute()

const scene = ref(route.query.scene)
const selectedRooms = ref([])
const roomTargets = ref({})
const robots = ref(robotsData)
const selectedRobot = ref('')
const commandInput = ref('')
const currentRoom = ref('')

// 创建消息实例
const message = useMessage()

// 修改图片获取方法
const getRobotImage = (imagePath) => {
  try {
    // 使用动态导入，移除开头的斜杠
    const path = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath
    return new URL(`../../assets/${path}`, import.meta.url).href
  } catch (error) {
    console.error('加载图片失败:', error)
    return ''
  }
}

// 定义缓存键名
const CACHE_KEYS = {
  SELECTED_ROBOT: 'decision_selected_robot',
  ROBOT_COMMAND: 'decision_robot_command',
  SELECTED_ROOMS: 'decision_selected_rooms',
  ROOM_TARGETS: 'decision_room_targets'
}

onMounted(() => {
  // 读取房间和目标缓存数据
  const cachedRooms = localStorage.getItem(CACHE_KEYS.SELECTED_ROOMS)
  const cachedTargets = localStorage.getItem(CACHE_KEYS.ROOM_TARGETS)

  if (cachedRooms && cachedTargets) {
    try {
      selectedRooms.value = JSON.parse(cachedRooms)
      roomTargets.value = JSON.parse(cachedTargets)

      // 确保所有选中的房间都有对应的目标数据
      selectedRooms.value.forEach(room => {
        if (!roomTargets.value[room]) {
          roomTargets.value[room] = {
            targets: [],
            isPrimary: false
          }
        }
      })

      // 优先选择主要房间作为当前房间，如果没有主要房间则选择第一个房间
      const primaryRoom = selectedRooms.value.find(room => roomTargets.value[room]?.isPrimary)
      currentRoom.value = primaryRoom || selectedRooms.value[0]
    } catch (error) {
      console.error('解析缓存数据失败:', error)
      // 如果解析失败，返回房间选择页面
      router.replace({
        name: 'decision-rooms',
        query: { scene: route.query.scene }
      })
      return
    }
  } else {
    // 如果没有缓存数据，返回房间选择页面
    router.replace({
      name: 'decision-rooms',
      query: { scene: route.query.scene }
    })
    return
  }

  // 读取机器人和指令缓存
  const cachedRobot = localStorage.getItem(CACHE_KEYS.SELECTED_ROBOT)
  const cachedCommand = localStorage.getItem(CACHE_KEYS.ROBOT_COMMAND)

  if (cachedRobot) {
    const robotInfo = JSON.parse(cachedRobot)
    selectedRobot.value = robotInfo.name
  }

  if (cachedCommand) {
    commandInput.value = cachedCommand
  }
})

// 修改选择机器人的方法
const toggleRobot = (robot) => {
  if (selectedRobot.value === robot.name) {
    // 如果点击的是已选中的机器人，取消选中
    selectedRobot.value = ''
  } else {
    // 否则选中新的机器人
    selectedRobot.value = robot.name
  }
}

const handleBack = () => {
  router.back()
}

// 修改处理指令提交的方法
const handleCommandSubmit = async () => {
  // 检查是否选择了机器人
  if (!selectedRobot.value) {
    message.error('请先选择一个机器人')
    return
  }

  const command = commandInput.value.trim()
  if (!command) {
    message.error('请输入指令内容')
    return
  }

  const selectedRobotInfo = robots.value.find(robot => robot.name === selectedRobot.value)

  if (selectedRobotInfo) {
    const robotCache = {
      id: selectedRobotInfo.id,
      name: selectedRobotInfo.name,
      image: selectedRobotInfo.image,
      command: command,
      currentRoom: currentRoom.value
    }

    try {
      // 保存缓存
      localStorage.setItem(CACHE_KEYS.SELECTED_ROBOT, JSON.stringify(robotCache))
      localStorage.setItem(CACHE_KEYS.ROBOT_COMMAND, command)

      console.log('准备跳转到工作页面...')

      // 修改路由跳转逻辑
      try {
        await router.push({
          path: '/decision/work',
          query: {
            scene: scene.value,
            timestamp: Date.now() // 添加时间戳确保路由更新
          }
        })
      } catch (routerError) {
        console.error('路由跳转错误:', routerError)
        // 尝试使用 window.location
        window.location.href = `/decision/work?scene=${encodeURIComponent(scene.value)}`
      }
    } catch (error) {
      console.error('缓存数据保存失败:', error)
      message.error('配置保存失败，请重试')
    }
  }
}

// 添加房间切换处理函数
const handleRoomChange = (room) => {
  currentRoom.value = room
}

// 注释掉或删除这部分代码
// onUnmounted(() => {
//   localStorage.removeItem(CACHE_KEYS.SELECTED_ROBOT)
//   localStorage.removeItem(CACHE_KEYS.ROBOT_COMMAND)
// })
</script>

<style scoped>
.robot-config {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.selected-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 24px;
}

.room-section {
  width: 100%;
}

.room-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.room-divider {
  height: 1px;
  background-color: #eee;
  margin-bottom: 12px;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.room-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  border: 2px solid #eee;
  cursor: pointer;
}

.room-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.room-card--selected {
  border-color: rgba(24, 160, 88, 0.8);
  background: linear-gradient(135deg, rgba(24, 160, 88, 0.1) 0%, rgba(24, 160, 88, 0.05) 100%);
}

.room-card--selected:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(24, 160, 88, 0.2);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.room-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.target-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

.target-tag {
  max-width: 100%;
}

.robots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 24px;
  padding: 16px 0;
}

.robot-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  /* 添加透明边框 */
}

.robot-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.robot-card--selected {
  border-color: rgba(24, 160, 88, 0.8);
  /* 选中时显示绿色边框 */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(24, 160, 88, 0.15);
}

.robot-card--selected:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(24, 160, 88, 0.2);
}

.robot-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.robot-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
}

.robot-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

/* 添加语言指令区域样式 */
.command-section {
  margin-top: 24px;
  padding: 0 24px;
  padding-bottom: 24px;
}

.command-input-wrapper {
  max-width: 800px;
  margin: 0 auto;
}

:deep(.n-input) {
  margin-bottom: 16px;
}

:deep(.n-input .n-input__prefix) {
  margin-right: 8px;
  color: #666;
}

.command-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 添加机器人名称提示样式 */
.robot-prompt {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  padding-left: 4px;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .robot-config {
    padding: 16px;
  }

  .robots-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }

  .robot-image {
    height: 160px;
    margin-bottom: 8px;
  }

  .robot-name {
    font-size: 14px;
  }

  .page-title {
    font-size: 14px;
  }

  .command-section {
    margin-top: 16px;
    padding: 0 16px;
    padding-bottom: 16px;
  }

  .command-input-wrapper {
    max-width: 600px;
  }

  :deep(.n-input) {
    margin-bottom: 12px;
  }

  .command-actions {
    gap: 8px;
  }

  .robot-prompt {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .room-label {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .room-divider {
    margin-bottom: 8px;
  }

  .rooms-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 12px;
  }

  .room-name {
    font-size: 14px;
  }

  :deep(.n-card-header) {
    padding: 12px 16px;
  }

  :deep(.n-card__content) {
    padding: 0 16px 16px 16px;
  }

  .target-tags {
    gap: 6px;
    padding: 6px 0;
  }
}

/* 添加分割线样式 */
:deep(.n-divider) {
  margin: 16px 0;
}

:deep(.n-divider__title) {
  font-size: 14px;
  color: #666;
}

/* 移除下一步按钮相关样式 */
.right-actions,
.next-step,
.next-step-text {
  display: none;
}

.command-actions :deep(.n-button) {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.command-actions :deep(.n-button .n-icon) {
  margin-left: 2px;
  font-size: 16px;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .command-actions :deep(.n-button .n-icon) {
    font-size: 14px;
  }
}

.robot-card--disabled {
  opacity: 0.5;
  /* 降低透明度 */
  pointer-events: none;
  /* 禁用鼠标事件 */
  cursor: not-allowed;
  /* 显示不可点击的鼠标指针 */
}

.primary-icon {
  color: #ffd700;
  font-size: 18px;
}

@media screen and (max-width: 1920px) {
  .primary-icon {
    font-size: 16px;
  }
}
</style>