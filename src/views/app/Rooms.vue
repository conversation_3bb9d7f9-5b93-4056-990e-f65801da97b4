<template>
    <div class="rooms-view">
        <!-- 顶部操作栏 -->
        <div class="header">
            <div class="left-actions">
                <n-button quaternary circle @click="handleBack">
                    <template #icon>
                        <n-icon>
                            <ArrowLeftOutline />
                        </n-icon>
                    </template>
                </n-button>
                <span class="page-title">{{ scene }} - 房间选择</span>
            </div>
            <div class="right-actions">
                <!-- 添加全选复选框 -->
                <n-checkbox :checked="isAllSelected" :indeterminate="isIndeterminate" @update:checked="handleSelectAll">
                    全选
                </n-checkbox>
                <div class="next-step" :class="{ 'next-step--active': selectedRooms.length > 0 }">
                    <span class="next-step-text">
                        {{ scene }} - 配置机器人
                    </span>
                    <n-button quaternary circle :disabled="selectedRooms.length === 0 || !hasPrimaryRoom" @click="handleNext">
                        <template #icon>
                            <n-icon>
                                <ArrowForwardOutline />
                            </n-icon>
                        </template>
                    </n-button>
                </div>
            </div>
        </div>

        <!-- 房间列表 -->
        <div class="room-container">
            <!-- 添加说明文案区域 -->
            <n-card class="guide-card">
                <div class="guide-content">
                    <div class="guide-item" data-index="1、">【操作步骤】您可以通过依次点击下方列表中的房间卡片，来选中当前场景中的房间；</div>
                    <div class="guide-item" data-index="2、">【房间配置】您可以删减或调整每个房间卡片中存在的家具；</div>
                    <div class="guide-item" data-index="3、">【重要提示】您必须指定【主人】所在的房间，点击房间卡片右上角的⭐图标进行设置；</div>
                    <div class="guide-item" data-index="4、">【主人说明】⭐表示当前房间为【主人】所在房间，在每次决策中主人只能同时存在于某一个房间中；</div>
                    <div class="guide-item" data-index="5、">【完成配置】配置完成后点击页面右上角的【下一步】按钮，保存并继续配置向导。</div>
                </div>
            </n-card>

            <div class="room-grid">
                <!-- 添加新增房间按钮 -->
                <div class="add-room-btn" @click="handleAddRoom">
                    <n-icon size="24" class="add-icon">
                        <AddOutline />
                    </n-icon>
                    <span>新增房间</span>
                </div>

                <!-- 现有的房间卡片列表 -->
                <n-card v-for="room in currentRooms" :key="room" class="room-card" :class="{
                    'room-card--selected': selectedRooms.includes(room),
                    'room-card--disabled': !roomTargets[room]?.targets?.length
                }" :title="room" @click="toggleRoomSelection(room)">
                    <template #header-extra>
                        <div class="card-actions" @click.stop>
                            <n-button quaternary circle class="primary-btn"
                                :class="{ 'primary-btn--active': roomTargets[room]?.isPrimary }"
                                @click="togglePrimaryRoom(room)">
                                <template #icon>
                                    <n-icon>
                                        <StarOutline />
                                    </n-icon>
                                </template>
                            </n-button>
                            <n-button quaternary circle class="delete-btn" 
                                :disabled="selectedRooms.includes(room) || roomTargets[room]?.isPrimary"
                                @click="removeRoom(room)">
                                <template #icon>
                                    <n-icon>
                                        <TrashOutline />
                                    </n-icon>
                                </template>
                            </n-button>
                            <n-button quaternary circle class="edit-btn" @click="showEditDialog(room)">
                                <template #icon>
                                    <n-icon>
                                        <PencilOutline />
                                    </n-icon>
                                </template>
                            </n-button>
                        </div>
                    </template>

                    <n-divider />

                    <!-- 使用标签展示识别目标 -->
                    <div class="target-tags">
                        <n-tag v-for="(target, index) in getVisibleTargets(room)" :key="index" closable
                            @close="removeTarget(room, target)" class="target-tag">
                            {{ target }}
                        </n-tag>
                        <!-- 只在目标数量大于等于6个时显示总数量 -->
                        <n-tag v-if="(roomTargets[room]?.targets?.length || 0) >= 6" class="more-tag" type="info">
                            等{{ roomTargets[room]?.targets?.length }}个
                        </n-tag>
                    </div>
                </n-card>
            </div>
        </div>

        <!-- 修改目标选择对话框 -->
        <n-modal v-model:show="showEditModal" preset="dialog" :title="currentEditRoom">
            <div class="edit-dialog-content">
                <div class="edit-dialog-container">
                    <!-- 添加房间名称编辑 -->
                    <div class="room-name-edit">
                        <n-input v-model:value="editingRoomName" type="text" placeholder="房间名称" class="room-name-input">
                            <template #prefix>
                                <n-icon :component="HomeOutline" />
                            </template>
                        </n-input>
                    </div>

                    <!-- 标签列表容器 -->
                    <div class="tags-container">
                        <div class="tags-list">
                            <n-tag v-for="tag in selectedTargets" :key="tag" closable @close="removeSelectedTag(tag)"
                                class="target-tag">
                                {{ tag }}
                            </n-tag>
                        </div>
                    </div>

                    <!-- 输入框 -->
                    <n-input v-model:value="newTag" type="text" placeholder="输入目标名称后按回车添加"
                        @keydown.enter="handleAddTag" />
                </div>
            </div>
            <template #action>
                <n-button @click="cancelEdit">取消</n-button>
                <n-button type="primary" @click="confirmEdit">确认</n-button>
            </template>
        </n-modal>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
    ArrowBack as ArrowLeftOutline,
    ArrowForward as ArrowForwardOutline,
    Close as CloseOutline,
    Trash as TrashOutline,
    Pencil as PencilOutline,
    Add as AddOutline,
    Home as HomeOutline,
    Star as StarOutline
} from '@vicons/ionicons5'
import scenesData from '@/assets/data/scenes.json'

const router = useRouter()
const route = useRoute()

// 从查询参数获取场景名称
const scene = computed(() => route.query.scene)

// 当前房间列表（可修改的）
const currentRooms = ref([])

// 定义缓存键名
const CACHE_KEYS = {
    SELECTED_ROOMS: 'decision_selected_rooms',
    ROOM_TARGETS: 'decision_room_targets'
}

// 在组件挂载时初始化数据
onMounted(() => {
    // 初始化房间数据
    initializeRooms()

    // 从缓存中读取房间配置
    const cachedRooms = localStorage.getItem(CACHE_KEYS.SELECTED_ROOMS)
    const cachedTargets = localStorage.getItem(CACHE_KEYS.ROOM_TARGETS)

    if (cachedRooms && cachedTargets) {
        try {
            const parsedRooms = JSON.parse(cachedRooms)
            const parsedTargets = JSON.parse(cachedTargets)
            selectedRooms.value = parsedRooms
            roomTargets.value = parsedTargets
        } catch (error) {
            console.error('解析缓存数据失败:', error)
        }
    }

    // 检查是否存在主要房间，如果不存在则设置第一个房间为主要房间
    const hasPrimaryRoom = Object.values(roomTargets.value).some(room => room.isPrimary)
    if (!hasPrimaryRoom && currentRooms.value.length > 0) {
        const firstRoom = currentRooms.value[0]
        roomTargets.value[firstRoom].isPrimary = true
        // 确保主要房间被选中
        if (!selectedRooms.value.includes(firstRoom)) {
            selectedRooms.value.push(firstRoom)
        }
    }
})

// 清除缓存的方法
const clearCache = () => {
    localStorage.removeItem(CACHE_KEYS.SELECTED_ROOMS)
    localStorage.removeItem(CACHE_KEYS.ROOM_TARGETS)
}

// 初始化房间列表和目标标签
const initializeRooms = () => {
    const sceneData = scenesData[route.query.scene] || {}
    // 获取房间列表（对象的键名）
    currentRooms.value = Object.keys(sceneData)

    // 初始化每个房间的目标标签和主要房间标识
    roomTargets.value = {}
    currentRooms.value.forEach(room => {
        roomTargets.value[room] = {
            targets: [...sceneData[room]], // 复制数组以避免直接引用
            isPrimary: false // 初始化主要房间标识
        }
    })
}

// 房间目标标签数据结构
const roomTargets = ref({})

const MAX_VISIBLE_TAGS = 5  // 设置最大可见标签数为5（第6个显示更多）

// 获取可见的目标标签（前5个）
const getVisibleTargets = (room) => {
    const targets = roomTargets.value[room]?.targets || []
    // 如果总数小于6个，显示全部标签；否则显示前5个
    return targets.length < 6 ? targets : targets.slice(0, MAX_VISIBLE_TAGS)
}

// 移除目标标签
const removeTarget = (room, target) => {
    const roomData = roomTargets.value[room]
    if (roomData?.targets) {
        const index = roomData.targets.indexOf(target)
        if (index > -1) {
            roomData.targets.splice(index, 1)
        }
    }
}

// 移除房间
const removeRoom = (room) => {
    const index = currentRooms.value.indexOf(room)
    if (index > -1) {
        currentRooms.value.splice(index, 1)
        // 同时删除该房间的目标标签
        delete roomTargets.value[room]
    }
}

// 处理返回
const handleBack = () => {
    router.back()
}

// 修改处理下一步的方法
const handleNext = () => {
    // 检查是否有主要房间
    const hasPrimaryRoom = Object.values(roomTargets.value).some(room => room.isPrimary)
    if (!hasPrimaryRoom) {
        window.$message.error('请至少设置一个主要房间（点击房间卡片右上角的星星图标）')
        return
    }

    if (selectedRooms.value.length > 0) {
        // 保存所有房间的目标数据到 localStorage
        localStorage.setItem(CACHE_KEYS.SELECTED_ROOMS, JSON.stringify(selectedRooms.value))
        localStorage.setItem(CACHE_KEYS.ROOM_TARGETS, JSON.stringify(roomTargets.value))

        console.log('已缓存选中的房间:', selectedRooms.value)
        console.log('已缓存房间目标:', roomTargets.value)

        // TODO: 跳转到配置机器人页面
        router.push({
            name: 'robot-config',  // 替换为实际的路由名称
            query: {
                scene: route.query.scene
            }
        })
    }
}

// 编辑相关的状态
const showEditModal = ref(false)
const currentEditRoom = ref('')
const selectedTargets = ref([])

// 添加新的响应式变量
const newTag = ref('')

// 添加房间名称编辑相关的状态
const editingRoomName = ref('')

// 修改添加标签的方法
const handleAddTag = (e) => {
    const value = newTag.value.trim()
    if (value) {
        // 如果标签已存在，先移除它
        const existingIndex = selectedTargets.value.indexOf(value)
        if (existingIndex > -1) {
            selectedTargets.value.splice(existingIndex, 1)
        }
        // 将标签添加到末尾
        selectedTargets.value.push(value)
        newTag.value = '' // 清空输入框
    }
    e.preventDefault() // 阻止回车默认行为
}

// 移除选中的标签
const removeSelectedTag = (tag) => {
    const index = selectedTargets.value.indexOf(tag)
    if (index > -1) {
        selectedTargets.value.splice(index, 1)
    }
}

// 修改显示编辑对话框方法
const showEditDialog = (room) => {
    currentEditRoom.value = room
    editingRoomName.value = room  // 初始化编辑名称
    selectedTargets.value = [...roomTargets.value[room].targets]
    newTag.value = ''
    showEditModal.value = true
}

// 修改确认编辑方法
const confirmEdit = () => {
    const newName = editingRoomName.value.trim()
    const oldName = currentEditRoom.value

    if (!newName) {
        window.$message.error('房间名称不能为空')
        return
    }

    // 如果名称发生变化且新名称已存在
    if (newName !== oldName && currentRooms.value.includes(newName)) {
        window.$message.error('房间名称已存在')
        return
    }

    // 更新房间名称和目标
    if (newName !== oldName) {
        // 更新房间列表
        const index = currentRooms.value.indexOf(oldName)
        if (index > -1) {
            currentRooms.value[index] = newName
        }
        // 更新目标数据，保持主要房间状态
        roomTargets.value[newName] = {
            targets: selectedTargets.value,
            isPrimary: roomTargets.value[oldName]?.isPrimary || false
        }
        delete roomTargets.value[oldName]
        // 更新选中状态
        if (selectedRooms.value.includes(oldName)) {
            const selectedIndex = selectedRooms.value.indexOf(oldName)
            selectedRooms.value[selectedIndex] = newName
        }
    } else {
        // 只更新目标，保持主要房间状态
        roomTargets.value[oldName].targets = selectedTargets.value
    }

    showEditModal.value = false
    currentEditRoom.value = ''
    editingRoomName.value = ''
    selectedTargets.value = []
}

// 修改取消编辑方法
const cancelEdit = () => {
    showEditModal.value = false
    currentEditRoom.value = ''
    editingRoomName.value = ''
    selectedTargets.value = []
}

// 添加选中房间的状态
const selectedRooms = ref([])

// 修改切换房间选中状态的方法
const toggleRoomSelection = (room) => {
    // 如果房间没有目标标签，不允许选中
    if (!roomTargets.value[room]?.targets?.length) {
        return
    }

    // 如果是主要房间，不允许取消选中
    if (roomTargets.value[room]?.isPrimary) {
        return
    }

    const index = selectedRooms.value.indexOf(room)
    if (index === -1) {
        selectedRooms.value.push(room)
    } else {
        selectedRooms.value.splice(index, 1)
    }
}

// 修改全选方法，只选择有目标标签的房间
const handleSelectAll = (checked) => {
    if (checked) {
        // 全选（只选择有目标标签的房间）
        selectedRooms.value = currentRooms.value.filter(room => roomTargets.value[room]?.targets?.length > 0)
    } else {
        // 取消全选
        selectedRooms.value = []
    }
}

// 修改计算属性：是否全选（只考虑有目标标签的房间）
const isAllSelected = computed(() => {
    const selectableRooms = currentRooms.value.filter(room => roomTargets.value[room]?.targets?.length > 0)
    return selectableRooms.length > 0 && selectedRooms.value.length === selectableRooms.length
})

// 添加计算属性：检查是否有主要房间
const hasPrimaryRoom = computed(() => {
    return Object.values(roomTargets.value).some(room => room.isPrimary)
})

// 修改计算属性：是否部分选中（只考虑有目标标签的房间）
const isIndeterminate = computed(() => {
    const selectableRooms = currentRooms.value.filter(room => roomTargets.value[room]?.targets?.length > 0)
    return selectedRooms.value.length > 0 && selectedRooms.value.length < selectableRooms.length
})

// 添加新增房间的方法
const handleAddRoom = () => {
    const newRoomIndex = currentRooms.value.length + 1
    const newRoomName = `新房间${newRoomIndex}`

    // 确保房间名称唯一
    let finalRoomName = newRoomName
    let counter = 1
    while (currentRooms.value.includes(finalRoomName)) {
        finalRoomName = `${newRoomName}_${counter}`
        counter++
    }

    // 添加新房间
    currentRooms.value.push(finalRoomName)
    // 初始化空的目标标签列表和主要房间状态
    roomTargets.value[finalRoomName] = {
        targets: [],
        isPrimary: false
    }

    // 自动打开编辑对话框
    showEditDialog(finalRoomName)
}

// 添加切换主要房间的方法
const togglePrimaryRoom = (room) => {
    // 如果当前房间已经是主要房间，则取消
    if (roomTargets.value[room].isPrimary) {
        roomTargets.value[room].isPrimary = false
    } else {
        // 先重置所有房间的主要状态
        Object.keys(roomTargets.value).forEach(key => {
            roomTargets.value[key].isPrimary = false
        })
        // 设置新的主要房间
        roomTargets.value[room].isPrimary = true
        // 确保主要房间被选中
        if (!selectedRooms.value.includes(room)) {
            selectedRooms.value.push(room)
        }

        // 更新缓存
        const cacheData = {
            selectedRooms: selectedRooms.value,
            roomTargets: {}
        }

        // 保存所有房间的目标数据
        Object.keys(roomTargets.value).forEach(roomKey => {
            cacheData.roomTargets[roomKey] = roomTargets.value[roomKey]
        })

        // 更新localStorage
        localStorage.setItem(CACHE_KEYS.SELECTED_ROOMS, JSON.stringify(cacheData.selectedRooms))
        localStorage.setItem(CACHE_KEYS.ROOM_TARGETS, JSON.stringify(cacheData.roomTargets))
    }
}
</script>

<style lang="scss" scoped>
@use './styles/rooms.scss';
</style>