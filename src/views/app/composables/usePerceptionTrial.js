import { ref, computed, watch, onMounted, onUnmounted, inject } from "vue";
import {
  ArrowBack as ArrowLeftOutline,
  ImageOutline,
  TrashOutline,
  PaperPlaneOutline,
  CopyOutline,
  SearchOutline,
} from "@vicons/ionicons5";
import { doPost, doPut, doGet } from "@/utils/requests";
import messages from "@/utils/messages";
import { getUploadParams } from "@/api/base";
import {
  NSpin,
  NDivider,
  NCollapseTransition,
  NCard,
  NTag,
  NModal,
} from "naive-ui";
import TypewriterText from "@/components/TypewriterText.vue";
import ResultCards from "@/components/ResultCards.vue";

export function usePerceptionTrial(props) {
  // 注入必要的数据
  const currentAppId = inject("currentAppId");

  // 基础状态
  const is_ability_added = ref(props.ability_added);
  const image_url = ref("");
  const selected_function = ref(null);
  const object_names = ref([]); // 改为数组存储标签
  const current_input = ref(""); // 当前输入的内容
  const questions = ref("");
  const object_questions = ref({}); // 存储每个标签对应的问题，格式：{ "标签名": "问题内容" }
  const function_options = ref([]);
  const current_request = ref({
    method: "",
    path: "",
  });

  // 访问凭证相关状态（对用户透明）
  const accessToken = ref("");
  const accessTokenLoading = ref(false);
  const tokenExpireTime = ref(0); // 保存令牌过期时间戳

  // 图片相关状态
  const active_tab = ref("2d");
  const rgb_image_url = ref("");
  const rgbd_image_url = ref("");

  // 任务相关状态
  const loading = ref(false);
  const taskResult = ref(null);
  const polling = ref(null);

  // 添加提交状态
  const submitting = ref(false);

  // 在 script setup 中添加新的响应状态变量
  const taskInfo = ref(null);
  const pollingCount = ref(0);
  const MAX_POLLING_COUNT = 30;

  // 添加操作按钮加载状态
  const actionLoading = ref(false);

  // 添加大图预览相关的响应式变量
  const showModal = ref(false);
  const currentLargeImage = ref("");

  // 添加文件大小限制常量（10MB）
  const MAX_FILE_SIZE = 10 * 1024 * 1024;

  // 添加多行文本占位符
  const multi_line_placeholder = `（可用时必填）可在此针对每个物体询问其状态，例如：
苹果: 是什么形状的？
香蕉: 是什么颜色的？
`;

  // 修改 API 配置映射
  const API_CONFIG = {
    // 全功能
    "app:perception:full": {
      title: "识别出",
      imageField: "croppedImagesListBbox",
      questions: true, // 允许输入问题
    },
    // 检测
    "app:perception:check": {
      title: "检测到",
      imageField: "croppedImagesListBbox",
      questions: false,
    },
    // 分割
    "app:perception:split": {
      title: "分割出",
      imageField: "croppedImagesListSegment",
      questions: false,
    },
    // 属性描述
    "app:perception:props-describe": {
      title: "识别出",
      imageField: "croppedImagesListBbox",
      questions: true, // 允许输入问题
    },
    // 角度预测
    "app:perception:angle-prediction": {
      title: "识别出角度",
      imageField: "croppedImagesListAngle",
      questions: false,
    },
    // 抓取点预测
    "app:perception:grab-point-prediction": {
      title: "识别出抓取点",
      imageField: "croppedImagesListGrasp",
      questions: false,
    },
    // 关键点预测
    "app:perception:key-point-prediction": {
      title: "识别出关键点",
      imageField: "croppedImagesListPoint",
      questions: false,
    },
  };

  // 添加是否显示操作按钮的计算属性
  const showActionButton = computed(() => {
    return (
      taskInfo.value?.taskStatus === "RUNNING" ||
      taskInfo.value?.taskStatus === "PROCESSING" ||
      taskInfo.value?.taskStatus === "TIMEOUT"
    );
  });

  // 修改获取任务结果标题的计算属性
  const getTaskResultTitle = computed(() => {
    // 如果任务完成但没有识别结果，显示相应提示
    if (!taskResult.value?.labels?.length) {
      if (taskInfo.value?.taskStatus === 'DONE') {
        // 从当前选择的功能中获取 apiCode
        const selectedOption = function_options.value.find(
          (opt) => opt.value === selected_function.value
        );
        const apiCode = selectedOption?.apiCode;

        // 根据不同功能类型给出不同的提示
        const emptyResultMessages = {
          "app:perception:full": "未识别到指定的物体",
          "app:perception:check": "未检测到指定的物体",
          "app:perception:split": "未找到可分割的物体",
          "app:perception:props-describe": "未识别到指定的物体",
          "app:perception:angle-prediction": "未识别到可预测角度的物体",
          "app:perception:grab-point-prediction": "未识别到可抓取的物体",
          "app:perception:key-point-prediction": "未识别到关键点"
        };

        return emptyResultMessages[apiCode] || "未识别到指定的物体";
      }
      return "";
    }

    // 从当前选择的功能中获取 apiCode
    const selectedOption = function_options.value.find(
      (opt) => opt.value === selected_function.value
    );
    const apiCode = selectedOption?.apiCode;

    // 获取对应的配置
    const config = API_CONFIG[apiCode] || { title: "识别出" };

    // 对标签进行分组计数
    const labelCounts = taskResult.value.labels.reduce((acc, label) => {
      acc[label] = (acc[label] || 0) + 1;
      return acc;
    }, {});

    // 将分组结果转换为描述文本
    const labelDescription = Object.entries(labelCounts)
      .map(([name, count]) => `${count}个${name}`)
      .join("，");

    return `为您${config.title} ${labelDescription}`;
  });

  // 修改是否可以提交的计算属性
  const can_submit = computed(() => {
    // 检查基本条件：必须有功能选择
    const has_basic_requirements = selected_function.value;

    if (!has_basic_requirements) return false;

    // 根模式检查
    if (active_tab.value === "2d") {
      // 2D模式：必须有普通图片
      return Boolean(image_url.value);
    } else {
      // 3D模式：必须同时有RGB和RGBD图片
      return Boolean(rgb_image_url.value && rgbd_image_url.value);
    }
  });

  // 修改 shouldDisableQuestions 计算属
  const shouldDisableQuestions = computed(() => {
    const selectedOption = function_options.value.find(
      (opt) => opt.value === selected_function.value
    );
    const apiCode = selectedOption?.apiCode;
    const config = API_CONFIG[apiCode];

    // 如果没有配置或 questions 不为 true，则禁用输入框
    return !config?.questions;
  });

  // 添加计算属性：当前功能是否支持问题输入
  const supportsQuestions = computed(() => {
    const selectedOption = function_options.value.find(
      (opt) => opt.value === selected_function.value
    );
    const apiCode = selectedOption?.apiCode;
    const config = API_CONFIG[apiCode];
    return config?.questions === true;
  });

  // 修改获取图片列表的计算属性
  const getImagesList = computed(() => {
    if (!taskResult.value) return [];

    // 从当前选择的功能中获取 apiCode
    const selectedOption = function_options.value.find(
      (opt) => opt.value === selected_function.value
    );
    const apiCode = selectedOption?.apiCode;

    // 获取对应的配置
    const config = API_CONFIG[apiCode];
    if (!config) {
      // 如果没有配置，尝试返回默认的图片字段
      return taskResult.value.croppedImagesListBbox || [];
    }

    // 返回对应字段的图片列表
    const imagesList = taskResult.value[config.imageField] || [];

    // 添加调试日志
    console.log('getImagesList 计算:', {
      apiCode,
      imageField: config.imageField,
      imagesList,
      taskResult: taskResult.value
    });

    return imagesList;
  });

  // 同时修改状态样式计算属性，添加失败状态
  const getStatusType = computed(() => {
    if (!taskInfo.value) return "default";

    switch (taskInfo.value.taskStatus) {
      case "DONE":
        return "success";
      case "TIMEOUT":
        return "超时,请重试";
      case "失败":
      case "提交失败": // 添加提交失败状态
        return "error";
      case "处理中":
        return "info";
      default:
        return "default";
    }
  });

  // 监听 props 变化
  watch(
    () => props.ability_added,
    (newValue) => {
      is_ability_added.value = newValue;
    }
  );

  // 修改加载功能列表的方法，确保保存 apiCode
  const loadFunctionOptions = async () => {
    const res = await doGet("/app-center/app/ability/api/list", {
      ability_id: props.ability_id,
    });
    if (res.code === 0) {
      function_options.value = res.data.map((item) => ({
        label: item.apiName,
        value: item.apiName,
        requestMethod: item.requestMethod,
        requestPath: item.requestPath,
        apiCode: item.apiCode, // 保存 apiCode
      }));

      // 如果有选项，默认选中第一项
      if (function_options.value.length > 0) {
        selected_function.value = function_options.value[0].value;
      }
    }
  };

  // 获取应用访问凭证（对用户完全透明）
  const getAccessToken = async () => {
    try {
      accessTokenLoading.value = true;

      // 从 sessionStorage 获取 app_id 和 app_secret
      const appId = sessionStorage.getItem("app_id");
      const appSecret = sessionStorage.getItem("app_secret");

      if (!appId || !appSecret) {
        console.error("未找到应用凭证，请先访问应用基础信息页面");
        return false;
      }

      // 调用获取访问凭证的 API
      const response = await doGet(
        `/open-apis/base/auth/access_token?app_id=${appId}&app_secret=${appSecret}`
      );

      // 根据新的返回结构进行适配
      if (response.code === 0 && response.data && response.data.accessToken) {
        // 使用正确的字段名 accessToken 而不是 access_token
        accessToken.value = response.data.accessToken;

        // 保存到 sessionStorage 以便其他页面使用
        sessionStorage.setItem("api_access_token", response.data.accessToken);

        // 保存过期时间
        if (response.data.expire) {
          // 保存过期秒数
          sessionStorage.setItem(
            "api_access_token_expire",
            String(response.data.expire)
          );

          // 计算过期时间戳并保存
          const expireTimestamp = Date.now() + response.data.expire * 1000;
          sessionStorage.setItem(
            "api_access_token_expire_time",
            String(expireTimestamp)
          );

          // 更新组件内的过期时间
          tokenExpireTime.value = expireTimestamp;
        }

        // 成功获取凭证，但不显示消息
        return true;
      } else {
        console.error("获取访问凭证失败：", response.message || "未知错误");
        return false;
      }
    } catch (error) {
      console.error("获取访问凭证出错:", error);
      return false;
    } finally {
      accessTokenLoading.value = false;
    }
  };

  // 检查访问凭证是否有效
  const checkAccessToken = async () => {
    // 如果没有访问凭证，直接获取新的
    if (!accessToken.value) {
      // 尝试从 sessionStorage 获取
      const storedToken = sessionStorage.getItem("access_token");
      const storedExpireTime = sessionStorage.getItem("access_token_expire_time");

      if (storedToken && storedExpireTime) {
        // 检查是否过期
        const expireTime = parseInt(storedExpireTime, 10);
        const now = Date.now();

        // 如果未过期，直接使用存储的令牌
        if (expireTime > now) {
          accessToken.value = storedToken;
          tokenExpireTime.value = expireTime;
          return true;
        }
      }

      // 如果没有令牌或已过期，获取新的
      return await getAccessToken();
    }

    // 如果有访问凭证，检查是否过期
    if (tokenExpireTime.value > 0) {
      const now = Date.now();

      // 如果过期或即将过期（小于5分钟），刷新令牌
      if (tokenExpireTime.value - now < 5 * 60 * 1000) {
        return await getAccessToken();
      }

      // 令牌有效
      return true;
    }

    // 如果有令牌但没有过期时间，保险起见获取新的
    return await getAccessToken();
  };

  // 修改文件处理方法
  const handleFileUpload = (file, type) => {
    if (!file || !(file instanceof File)) return;

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      messages.error("图片大小不能超过10MB");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      switch (type) {
        case "image":
          image_url.value = e.target.result;
          break;
        case "rgb":
          rgb_image_url.value = e.target.result;
          break;
        case "rgbd":
          rgbd_image_url.value = e.target.result;
          break;
      }
    };
    reader.readAsDataURL(file);
  };

  // 改上传组件的 before-upload 属性
  const beforeUpload = (file) => {
    if (file.size > MAX_FILE_SIZE) {
      messages.error("图片大小不能超过10MB");
      return false;
    }
    return true;
  };

  // 修改拖拽处理方法
  const handleDrop = (e) => {
    const file = e.file?.file;
    if (file) handleFileUpload(file, "image");
  };

  const handleRGBDrop = (e) => {
    const file = e.file?.file;
    if (file) handleFileUpload(file, "rgb");
  };

  const handleRGBDDrop = (e) => {
    const file = e.file?.file;
    if (file) handleFileUpload(file, "rgbd");
  };

  // 删除图片方法
  const removeImage = () => {
    image_url.value = "";
  };

  const removeRGBImage = () => {
    rgb_image_url.value = "";
  };

  const removeRGBDImage = () => {
    rgbd_image_url.value = "";
  };

  // 添加图片上传处理方法
  const handleImageUpload = ({ file }) => {
    handleFileUpload(file.file, "image");
  };

  const handleRGBImageUpload = ({ file }) => {
    handleFileUpload(file.file, "rgb");
  };

  const handleRGBDImageUpload = ({ file }) => {
    handleFileUpload(file.file, "rgbd");
  };

  // 处理图片上传
  const uploadImage = async (base64Data) => {
    if (!base64Data) return null;

    try {
      // 获取上传参数
      const uploadParams = await getUploadParams();
      if (uploadParams.code !== 0) {
        console.error("获取上传参数失败:", uploadParams);
        throw new Error("获取上传参数失败");
      }

      // 将base64转换为文件
      const binaryData = atob(base64Data.split(",")[1]);
      const array = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        array[i] = binaryData.charCodeAt(i);
      }
      const blob = new Blob([array], { type: "image/png" });

      console.log("上传参数:", {
        url: uploadParams.data.url,
        key: uploadParams.data.key,
      });

      // 直接发送PUT请求到返回的URL，请求体为文件内容
      const response = await fetch(uploadParams.data.url, {
        method: "PUT",
        headers: {
          "Content-Type": "image/png",
        },
        body: blob,
      });

      console.log("上传响应状态:", response.status);

      // 检查响应状态码，200表示上传成功
      if (response.status !== 200) {
        const responseText = await response.text();
        console.error("上传失败响应内容:", responseText);
        throw new Error(`上传失败: ${response.status} ${responseText}`);
      }

      // 返回key作为后续请求的参数
      return uploadParams.data.key;
    } catch (error) {
      console.error("上传出错:", error);
      throw error;
    }
  };

  // 添加复制方法
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      messages.success("复制成功");
    } catch (err) {
      messages.error("复制失败");
    }
  };

  // 添加显示大图的方法
  const showLargeImage = (imageUrl) => {
    currentLargeImage.value = imageUrl;
    showModal.value = true;
  };

  // 添加标签管理方法
  const addObjectName = (name) => {
    if (!name || typeof name !== 'string') return;

    // 检查标签数量限制
    if (object_names.value.length >= 10) {
      messages.error("最多只能添加10个物品标签");
      return;
    }

    const trimmedName = name.trim();
    if (trimmedName && !object_names.value.includes(trimmedName)) {
      object_names.value.push(trimmedName);

      // 如果当前功能支持问题输入，为新标签创建默认问题
      if (supportsQuestions.value) {
        object_questions.value[trimmedName] = '属性'; // 默认填充"属性"
      }
    }
    current_input.value = '';
  };

  const removeObjectName = (index) => {
    if (index >= 0 && index < object_names.value.length) {
      const removedName = object_names.value[index];
      object_names.value.splice(index, 1);

      // 同时删除对应的问题
      if (object_questions.value[removedName]) {
        delete object_questions.value[removedName];
      }
    }
  };

  const handleInputKeydown = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      addObjectName(current_input.value);
    }
  };

  // 添加能力方法
  const handleAddAbility = async () => {
    try {
      const res = await doPost("/app-center/app/acl", {
        appId: currentAppId.value,
        abilityId: props.ability_id,
      });
      if (res.code === 0) {
        messages.success("添加成功");
        is_ability_added.value = true;
      } else {
        messages.error(res.msg || "添加失败");
      }
    } catch (error) {
      messages.error("添加失败");
    }
  };

  // 删除能力方法
  const handleRemoveAbility = async () => {
    try {
      const res = await doPut("/app-center/app/acl", {
        appId: currentAppId.value,
        abilityId: props.ability_id,
      });
      if (res.code === 0) {
        messages.success("删除成功");
        is_ability_added.value = false;
      } else {
        messages.error(res.msg || "删除失败");
      }
    } catch (error) {
      messages.error("删除失败");
    }
  };

  // 添加轮询相关函数
  const startPolling = async (taskId) => {
    // 清除可能存在的之前的轮询
    if (polling.value) {
      clearInterval(polling.value);
    }

    // 重置轮询计数
    pollingCount.value = 0;

    // 创建新的轮询
    polling.value = setInterval(async () => {
      try {
        pollingCount.value++;

        // 检查是否超过最大轮询次数
        if (pollingCount.value > MAX_POLLING_COUNT) {
          clearInterval(polling.value);
          taskInfo.value.taskStatus = "TIMEOUT";
          loading.value = false;
          submitting.value = false;
          return;
        }

        // 确保系统准备就绪（对用户透明）
        const tokenSuccess = await checkAccessToken();
        if (!tokenSuccess) {
          clearInterval(polling.value);
          messages.error("系统连接中断，请刷新页面重试");
          loading.value = false;
          submitting.value = false;
          return;
        }

        // 获取任务状态
        const res = await doGet(
          "/open-apis/app/perception/result?task_id=" + taskId
        );

        if (res.code === 0) {
          // 更新任务状态
          taskInfo.value = {
            ...taskInfo.value,
            taskStatus: res.data.taskStatus,
          };

          // 根据任务状态处理
          switch (res.data.taskStatus) {
            case "DONE":
              // 任务完成
              clearInterval(polling.value);
              taskResult.value = res.data.taskResult;
              loading.value = false;
              submitting.value = false;
              break;

            case "FAILED":
              // 任务失败
              clearInterval(polling.value);
              messages.error("任务执行失败");
              loading.value = false;
              submitting.value = false;
              break;

            case "CANCELED":
              // 任务被取消
              clearInterval(polling.value);
              loading.value = false;
              submitting.value = false;
              break;
          }
        } else {
          // 请求出错
          clearInterval(polling.value);
          messages.error("获取任务状态失败");
          loading.value = false;
          submitting.value = false;
        }
      } catch (error) {
        console.error("轮询出错:", error);
        clearInterval(polling.value);
        loading.value = false;
        submitting.value = false;
      }
    }, 2000); // 每2秒轮询一次
  };

  // 修改提交处理方法
  const handleSubmit = async () => {
    if (!selected_function.value || !current_request.value.path) return;

    // 新增：检查物品问题描述的表单验证
    if (supportsQuestions.value && object_names.value.length > 0) {
      // 检查是否所有物品都有对应的问题描述，不允许空值
      for (const name of object_names.value) {
        const question = object_questions.value[name];
        if (!question || question.trim() === '') {
          messages.error(`请填写关于"${name}"的问题描述`);
          return;
        }
      }
    }

    if (active_tab.value === "3d" && !rgbd_image_url.value) {
      messages.error("3D模式下需要同时上传RGB图片和深度图片");
      return;
    }

    try {
      submitting.value = true; // 开始提交，禁用按钮
      loading.value = true;
      taskResult.value = null;

      // 初始化任务信息，立即显示处理中状态
      taskInfo.value = {
        taskId: "等待响应...",
        taskStatus: "正在提交...",
        traceId: "等响应...",
      };

      // 在提交任务前检查访问凭证（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        messages.error("系统准备失败，请稍后重试");
        loading.value = false;
        submitting.value = false;
        return;
      }

      // 构建基础请求数据
      const request_data = {
        object_names: object_names.value.length > 0 ? object_names.value.join(',') : '', // 将标签数组用逗号拼接，如果没有标签则为空字符串
        image_type: active_tab.value.toUpperCase(),
      };

      // 处理问题字段：如果支持问题输入，拼接标签对应的问题
      if (supportsQuestions.value && Object.keys(object_questions.value).length > 0) {
        const questionsList = object_names.value.map(name => {
          const question = object_questions.value[name] || '';
          // 按照要求的格式拼接：标签:问题内容
          return `${name}:${question.trim()}`;
        }).join('\n');
        request_data.questions = questionsList;
      } else if (questions.value) {
        // 兼容旧的问题输入方式
        request_data.questions = questions.value;
      }

      // 根据当前模式上传图片
      if (active_tab.value === "2d") {
        if (image_url.value) {
          const imageUrl = await uploadImage(image_url.value);
          request_data.imageUrl = imageUrl; // 直接使用完整的URL
        } else {
          messages.error("请上传图片");
          loading.value = false;
          submitting.value = false;
          return;
        }
      } else {
        if (rgb_image_url.value && rgbd_image_url.value) {
          const [imageUrl, depthUrl] = await Promise.all([
            uploadImage(rgb_image_url.value),
            uploadImage(rgbd_image_url.value),
          ]);
          request_data.imageUrl = imageUrl; // 直接使用完整的URL
          request_data.depthUrl = depthUrl; // 直接使用完整的URL
        } else {
          messages.error("请同时上传RGB图片和深度图");
          loading.value = false;
          submitting.value = false;
          return;
        }
      }

      console.log("发送给后端的数据:", request_data); // 添加日志

      // 发送请求并处理响应
      const api_path = `/open-apis${current_request.value.path}`;

      // 创建请求头，添加 Bearer 认证
      const headers = {
        Authorization: `Bearer ${accessToken.value}`,
      };

      // 使用自定义请求头发送请求
      const res = await doPost(api_path, request_data, headers);

      if (res.code === 0) {
        if (res.data.taskStatus === "SUBMIT_FAILED") {
          // 如果提交就失败了，直接显示提交失败状态
          taskInfo.value = {
            taskId: res.data.taskId,
            taskStatus: "提交失败",
            traceId: res.headers?.["x-trace-id"] || "未获取",
          };
          loading.value = false;
          submitting.value = false;
        } else if (res.data.taskId) {
          // 正常提交成功，开始轮询
          taskInfo.value = {
            taskId: res.data.taskId,
            taskStatus: "处理中",
            traceId: res.headers?.["x-trace-id"] || "未获取",
          };
          startPolling(res.data.taskId);
        }
      } else {
        messages.error("提交失败");
        loading.value = false;
        submitting.value = false;
        taskInfo.value = null;
      }
    } catch (error) {
      loading.value = false;
      submitting.value = false;
      taskInfo.value = null;
    }
  };

  // 修改取消任务的方法
  const handleCancelTask = async () => {
    if (!taskInfo.value?.taskId) return;

    try {
      actionLoading.value = true;

      // 确保系统准备就绪（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        messages.error("系统准备失败，请稍后重试");
        actionLoading.value = false;
        return;
      }

      // 创建请求头，添加 Bearer 认证
      const headers = {
        Authorization: `Bearer ${accessToken.value}`,
      };

      // 直接更新任务状态为 CANCELED
      await doPut(
        "/app-center/app/task/record",
        {
          taskId: taskInfo.value.taskId,
          taskStatus: "CANCELED",
        },
        headers
      );

      messages.success("任务已取消");
      // 终止轮询
      clearInterval(polling.value);
      // 重置状态
      loading.value = false;
      submitting.value = false;
      if (taskInfo.value) {
        taskInfo.value.taskStatus = "CANCELED";
      }

      // 重置表单状态（可选）
      // 如果需要重置表单，可以在这里添加重置逻辑
      // 例如：image_url.value = ''
    } catch (error) {
      console.error("取消任务失败:", error);
      messages.error("取消任务失败: " + (error.message || "未知错误"));
    } finally {
      actionLoading.value = false;
    }
  };

  // 添加重试轮询的方法
  const retryPolling = async () => {
    if (!taskInfo.value?.taskId) return;

    try {
      actionLoading.value = true;

      // 确保系统准备就绪（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        messages.error("系统准备失败，请稍后重试");
        return;
      }

      // 重置任务状态
      taskInfo.value.taskStatus = "处理中";
      loading.value = true;
      // 重新开始轮询
      startPolling(taskInfo.value.taskId);
    } catch (error) {
      console.error("重试失败:", error);
      messages.error("重试失败: " + (error.message || "未知错误"));
      loading.value = false;
    } finally {
      actionLoading.value = false;
    }
  };

  // 监听功能选择变化
  watch(selected_function, (newValue) => {
    if (newValue) {
      // 找到选中的功能选项
      const selectedOption = function_options.value.find(
        (opt) => opt.value === newValue
      );
      if (selectedOption) {
        // 更新请求信息
        current_request.value = {
          method: selectedOption.requestMethod,
          path: selectedOption.requestPath,
        };

        // 如果是检测任务，清空问题
        if (selectedOption.value === "提交检测任务") {
          questions.value = "";
        }
      }

      // 重置标签和输入框
      object_names.value = [];
      current_input.value = "";
      object_questions.value = {};
    } else {
      // 清空请求信息
      current_request.value = {
        method: "",
        path: "",
      };

      // 重置标签和输入框
      object_names.value = [];
      current_input.value = "";
      object_questions.value = {};
    }
  });

  // 在组件挂载时加载功能列表和获取访问凭证
  onMounted(async () => {
    loadFunctionOptions();

    // 检查并获取访问凭证
    await checkAccessToken();

    // 清除决策相关的缓存
    localStorage.removeItem("decision_selected_robot");
    localStorage.removeItem("decision_robot_command");
    localStorage.removeItem("decision_selected_rooms");
    localStorage.removeItem("decision_room_targets");
  });

  // 在组件卸载时清除轮询
  onUnmounted(() => {
    if (polling.value) {
      clearInterval(polling.value);
    }
  });

  // 添加计算属性：检查是否有任何有效的结果数据
  const hasAnyResults = computed(() => {
    if (!taskResult.value) return false;

    // 检查各种类型的结果数据
    const hasLabels = taskResult.value.labels && taskResult.value.labels.length > 0;
    const hasGraspImage = taskResult.value.graspImage && taskResult.value.graspImage !== '';
    const hasAngleImage = taskResult.value.angleImage && taskResult.value.angleImage !== '';
    const hasMaskImages = taskResult.value.maskImage && taskResult.value.maskImage.length > 0;

    // 检查问答结果
    const hasAnswers = taskResult.value.answers && taskResult.value.answers.length > 0;

    // 检查边界框
    const hasBoxes = taskResult.value.boxes && taskResult.value.boxes.length > 0;

    // 检查裁剪图片
    const hasCroppedImages = taskResult.value.croppedImagesListBbox && taskResult.value.croppedImagesListBbox.length > 0;

    // 检查置信度分数
    const hasScores = taskResult.value.scores && taskResult.value.scores.length > 0;

    // 添加调试日志
    console.log('hasAnyResults 检查:', {
      hasLabels,
      hasGraspImage,
      hasAngleImage,
      hasMaskImages,
      hasAnswers,
      hasBoxes,
      hasCroppedImages,
      hasScores,
      taskResult: taskResult.value
    });

    return hasLabels || hasGraspImage || hasAngleImage || hasMaskImages || hasAnswers || hasBoxes || hasCroppedImages || hasScores;
  });

  // 添加计算属性：根据功能类型获取建议
  const getEmptySuggestions = computed(() => {
    const selectedOption = function_options.value.find(
      (opt) => opt.value === selected_function.value
    );
    const apiCode = selectedOption?.apiCode;

    const suggestionMap = {
      "app:perception:full": [
        "确保图片中包含您指定的物体",
        "检查物体是否被遮挡或模糊",
        "尝试调整图片角度或光线条件",
        "确认物体名称是否准确"
      ],
      "app:perception:check": [
        "确保图片中包含要检测的物体",
        "检查物体尺寸是否合适（不要过小或过大）",
        "确保图片清晰度足够",
        "尝试不同的拍摄角度"
      ],
      "app:perception:split": [
        "确保物体边界清晰可见",
        "检查物体与背景的对比度",
        "避免复杂背景干扰",
        "确保物体完整出现在图片中"
      ],
      "app:perception:props-describe": [
        "确保物体特征清晰可见",
        "检查图片光线是否充足",
        "确认物体名称和问题描述准确",
        "尝试更清晰的图片"
      ],
      "app:perception:angle-prediction": [
        "确保物体轮廓清晰",
        "检查物体是否完整显示",
        "避免物体重叠或遮挡",
        "确保图片角度合适"
      ],
      "app:perception:grab-point-prediction": [
        "确保物体适合抓取",
        "检查物体表面是否清晰可见",
        "避免物体过于复杂的形状",
        "确保抓取面可见"
      ],
      "app:perception:key-point-prediction": [
        "确保关键点区域清晰",
        "检查物体特征是否明显",
        "避免关键点被遮挡",
        "尝试更好的拍摄角度"
      ]
    };

    return suggestionMap[apiCode] || [
      "图片中没有您指定的物体",
      "物体被遮挡或不够清晰",
      "物体尺寸过小或过大",
      "图片光线条件不佳"
    ];
  });

  // 返回所有需要的状态和方法
  return {
    // 基础状态
    is_ability_added,
    image_url,
    selected_function,
    object_names,
    current_input,
    questions,
    object_questions,
    function_options,
    current_request,

    // 图片相关状态
    active_tab,
    rgb_image_url,
    rgbd_image_url,

    // 任务相关状态
    loading,
    taskResult,
    submitting,
    taskInfo,
    actionLoading,

    // 计算属性
    showActionButton,
    getTaskResultTitle,
    can_submit,
    shouldDisableQuestions,
    supportsQuestions,
    getImagesList,
    getStatusType,
    multi_line_placeholder,
    hasAnyResults,
    getEmptySuggestions,

    // 大图预览
    showModal,
    currentLargeImage,

    // 方法
    handleFileUpload,
    beforeUpload,
    handleDrop,
    handleRGBDrop,
    handleRGBDDrop,
    removeImage,
    removeRGBImage,
    removeRGBDImage,
    handleImageUpload,
    handleRGBImageUpload,
    handleRGBDImageUpload,
    handleSubmit,
    handleCancelTask,
    handleAddAbility,
    handleRemoveAbility,
    copyToClipboard,
    showLargeImage,
    retryPolling,

    // 标签管理方法
    addObjectName,
    removeObjectName,
    handleInputKeydown,
  };
}