<template>
  <div class="decision-trial">
    <n-card>
      <!-- 顶部操作栏 -->
      <div class="trial-header">
        <div class="left-actions">
          <n-button quaternary circle @click="$emit('close')">
            <template #icon>
              <n-icon>
                <ArrowLeftOutline />
              </n-icon>
            </template>
          </n-button>
          <span class="page-title">千诀·决策模型</span>
        </div>
      </div>

      <!-- 场景选择区域 -->
      <div class="scene-grid">
        <!-- 预设场景按钮 -->
        <n-button
          v-for="(rooms, scene) in scenes"
          :key="scene"
          class="scene-btn"
          :quaternary="false"
          @click="handleSceneSelect(scene)"
        >
          <div class="scene-content">
            <n-icon size="32">
              <component :is="getSceneIcon(scene)" />
            </n-icon>
            <span>{{ scene }}</span>
          </div>
        </n-button>

        <!-- 自定义场景按钮 -->
        <n-button
          class="scene-btn custom-scene-btn"
          :quaternary="false"
          disabled
          @click="handleCustomScene"
        >
          <div class="custom-scene-content">
            <div class="scene-title">自定义场景</div>
            <div class="coming-soon">敬请期待</div>
          </div>
        </n-button>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowBack as ArrowLeftOutline, Home, Bed, Book, Briefcase, GameController } from '@vicons/ionicons5'
import scenesData from '@/assets/data/scenes.json'

const router = useRouter()
const scenes = ref(scenesData)

// 定义缓存键名
const CACHE_KEYS = {
  SELECTED_ROBOT: 'decision_selected_robot',
  ROBOT_COMMAND: 'decision_robot_command',
  SELECTED_ROOMS: 'decision_selected_rooms',
  ROOM_TARGETS: 'decision_room_targets'
}

// 处理场景选择
const handleSceneSelect = (scene) => {
  // 清除所有相关缓存
  Object.values(CACHE_KEYS).forEach(key => {
    localStorage.removeItem(key)
  })

  router.push({
    name: 'decision-rooms',
    query: {
      scene
    }
  })
}

// 处理自定义场景
const handleCustomScene = () => {
  console.log('创建自定义场景')
}

// 定义发出的事件
defineEmits(['close'])
// 获取场景对应的图标
const getSceneIcon = (scene) => {
  const iconMap = {
    '家庭': Home,
    '酒店': Bed,
    '学校': Book,
    '办公': Briefcase,
    '娱乐': GameController
  }
  return iconMap[scene] || null
}
</script>

<style scoped>
.decision-trial {
  width: 100%;
}

.trial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  padding-top: 16px;
  border-bottom: 1px solid #eee;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.scene-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 16px 16px;
}

.scene-btn {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.scene-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-scene-btn {
  border: 2px dashed var(--n-primary-color);
  background-color: transparent;
  color: var(--n-primary-color);
  padding: 16px;
}

.custom-scene-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.scene-title {
  font-size: 16px;
  font-weight: 500;
}

.coming-soon {
  display: block;
  font-size: 12px;
  color: var(--n-primary-color);
  margin-top: 8px;
  opacity: 0.8;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  line-height: 1.5;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .scene-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .scene-btn,
  .custom-scene-btn {
    height: 140px;
    font-size: 14px;
  }

  .custom-scene-btn {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .coming-soon {
    font-size: 12px;
    color: var(--n-primary-color);
    margin-top: 8px;
    opacity: 0.8;
    width: 100%;
    text-align: center;
  }

  .custom-scene-btn .n-icon {
    font-size: 20px;
  }
}

/* 添加全屏模态框样式 */
:deep(.fullscreen-modal) {
  --n-padding-top: 0 !important;
  --n-padding-bottom: 0 !important;
  --n-padding-left: 0 !important;
  --n-padding-right: 0 !important;
}

:deep(.n-modal) {
  position: fixed;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  transform: none !important;
  margin: 0 !important;
  border-radius: 0 !important;
}

:deep(.n-card) {
  height: 100vh;
  max-height: 100vh;
  border-radius: 0;
}

:deep(.n-card-header) {
  padding: 0 !important;
}

:deep(.n-card__content) {
  padding: 0 !important;
}

.scene-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}
</style>