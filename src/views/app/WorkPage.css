.work-page {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.right-actions {
  display: flex;
  align-items: center;
}

.status-tag {
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .work-page {
    padding: 16px;
  }

  .page-title {
    font-size: 14px;
  }
}

/* 添加新样式 */
.work-info {
  padding: 0 16px;
}

.section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.room-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  border: 2px solid #eee;
}

.room-card--current {
  border-color: rgba(24, 160, 88, 0.8);
  background: linear-gradient(135deg,
      rgba(24, 160, 88, 0.1) 0%,
      rgba(24, 160, 88, 0.05) 100%);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.room-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.target-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

.target-tag {
  max-width: 100%;
}

.work-container {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.robot-info {
  flex: 2;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* 机器人图片区域 */
.robot-image-section {
  flex: 0 0 auto;
  width: 120px;
}

.robot-image {
  width: 120px;
  height: 120px;
  overflow: hidden;
  border-radius: 8px;
  background-color: #fff;
}

.robot-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 机器人身份信息区域 */
.robot-identity {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 160px;
}

.robot-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.robot-location {
  margin-top: 4px;
}

/* 指令区域 */
.command-section {
  flex: 1;
  min-width: 200px;
}

.command-box {
  background-color: #fff;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #eee;
  height: 100%;
}

.command-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.command-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .robot-info {
    padding: 16px;
    gap: 16px;
  }

  .robot-image-section {
    width: 100px;
  }

  .robot-image {
    width: 100px;
    height: 100px;
  }

  .robot-identity {
    min-width: 140px;
    gap: 6px;
  }

  .robot-name {
    font-size: 16px;
  }

  .command-section {
    min-width: 180px;
  }

  .command-box {
    padding: 10px;
  }

  .command-label,
  .command-content {
    font-size: 12px;
  }
}

/* 修改决策操作区域样式 */
.decision-actions {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 8px;
  min-width: 320px;
  display: flex;
  align-items: center;
  padding: 20px;
}

.decision-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
}

.upload-section {
  width: 100%;
  min-width: 250px;
  min-height: 250px;
  display: flex;
  justify-content: center;
}

.upload-trigger {
  width: 100%;
  height: 100%;
  min-height: 350px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

.upload-trigger:hover {
  border-color: #18a058;
}

.upload-trigger.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.upload-trigger.disabled:hover {
  border-color: #e5e7eb;
}

.upload-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 16px;
}

.upload-text {
  color: #909399;
  font-size: 14px;
  margin: 0;
  text-align: center;
  padding: 0 16px;
}

.image-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
  display: flex;
  gap: 8px;
}

.remove-image {
  width: 32px;
  height: 32px;
  padding: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remove-image:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.remove-image .n-icon {
  font-size: 18px;
  color: #d03050;
}

.decision-button {
  width: 160px;
  height: 40px;
  font-size: 16px;
}

/* 修改上传组件的样式 */
:deep(.n-upload) {
  width: 100%;
  height: 100%;
}

:deep(.n-upload-trigger) {
  height: 100%;
  width: 100%;
}

:deep(.n-upload-trigger-area) {
  height: 100%;
  width: 100%;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .decision-actions {
    padding: 16px;
  }

  .decision-content {
    gap: 16px;
  }

  .upload-section {
    min-width: 220px;
    min-height: 220px;
  }

  .upload-trigger {
    min-height: 220px;
  }

  .upload-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .upload-text {
    font-size: 13px;
  }

  .remove-image {
    width: 28px;
    height: 28px;
  }

  .remove-image .n-icon {
    font-size: 16px;
  }

  .decision-button {
    width: 140px;
    height: 36px;
    font-size: 14px;
  }
}

/* 添加决策抽屉相关样式 */
.decision-drawer {
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.decision-drawer :deep(.n-drawer-content) {
  background-color: #fff;
}

.decision-drawer-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  overflow: hidden;
}

.decision-info {
  flex: 1;
  overflow: hidden;
}

.no-scroll {
  overflow: hidden;
}

.drawer-footer {
  padding: 16px 0;
  border-top: 1px solid #eee;
  margin-top: auto;
}

/* 新增水平布局样式 */
.decision-result-layout {
  display: flex;
  gap: 24px;
  height: 100%;
  overflow: hidden;
}

.task-card {
  flex: 1;
  overflow-y: auto;
  max-height: 100%;
}

.preview-image {
  flex-shrink: 0;
  width: 45%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.task-card {
  flex: 1;
  min-width: 0;
  /* 防止内容溢出 */
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-id-row,
.trace-id-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id,
.trace-id {
  font-size: 14px;
  color: #666;
}

.copy-button {
  width: 24px;
  height: 24px;
  padding: 0;
}

.copy-button:hover {
  background-color: #f5f7fa;
}

.copy-button .n-icon {
  font-size: 14px;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {

  .task-id,
  .trace-id {
    font-size: 13px;
  }

  .copy-button {
    width: 22px;
    height: 22px;
  }

  .copy-button .n-icon {
    font-size: 13px;
  }
}

.task-actions {
  margin-top: 16px;
}

.action-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-item {
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

.decision-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

/* 响应式调整 */
@media screen and (max-width: 1920px) {
  .decision-result-layout {
    gap: 16px;
  }

  .preview-image {
    flex: 0 0 250px;
    height: 180px;
  }

  .task-id {
    font-size: 13px;
  }

  .action-title {
    font-size: 13px;
  }

  .action-item {
    font-size: 13px;
    padding: 6px 10px;
  }
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.task-actions {
  margin: 20px 0;
}

.action-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.action-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  /* 每列最小宽度 150px，自动填充 */
  gap: 10px;
  /* 宫格之间的间距 */
}

.action-item {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f9f9f9;
  text-align: center;
}

.primary-icon {
  color: #ffd700;
  font-size: 18px;
}

@media screen and (max-width: 1920px) {
  .primary-icon {
    font-size: 16px;
  }
}