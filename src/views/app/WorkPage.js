import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getUploadParams } from "@/api/base";
import { doPost, doGet } from "@/utils/requests";
import message from "@/utils/messages";

export function useWorkPageLogic() {
  console.log("useWorkPageLogic 函数开始执行");

  const router = useRouter();
  const route = useRoute();

  const scene = ref(route.query.scene);
  const selectedRooms = ref([]);
  const roomTargets = ref({});
  const robotInfo = ref(null);
  const decisionCount = ref(0);

  // 添加新的响应式变量
  const imageUrl = ref("");
  const loading = ref(false);
  const decisionResult = ref({
    imageUrl: "",
    taskId: "",
    taskStatus: "",
    robotAction: [],
    traceId: "",
    hasStarted: false,
    ackStatus: "", // 添加用户反馈状态字段
  });
  const showDecisionResult = ref(false);

  // 添加新的响应式变量
  const taskInfo = ref(null);
  const pollingTimer = ref(null);

  // 添加新的响应式变量来存储历史记录
  const decisionHistory = ref({
    historyActions: [],
    historyImages: [],
  });

  // 添加访问凭证相关的响应式变量
  const accessToken = ref("");
  const accessTokenLoading = ref(false);
  const tokenExpireTime = ref(0);

  // 添加 setting 接口错误状态
  const settingError = ref(false);
  const settingErrorMessage = ref("");

  // 定义缓存键名
  const CACHE_KEYS = {
    SELECTED_ROBOT: "decision_selected_robot",
    ROBOT_COMMAND: "decision_robot_command",
    SELECTED_ROOMS: "decision_selected_rooms",
    ROOM_TARGETS: "decision_room_targets",
    TASK_ID: "decision_task_id",
    TASK_STEP: "decision_task_step", // 新增 task_step 的缓存键
  };

  // 添加文件大小限制（10MB）
  const MAX_FILE_SIZE = 10 * 1024 * 1024;

  // 组件挂载逻辑
  onMounted(async () => {
    console.log("WorkPage onMounted 开始执行");

    // 清除之前的缓存
    localStorage.removeItem(CACHE_KEYS.TASK_ID);
    localStorage.removeItem(CACHE_KEYS.TASK_STEP); // 新增清除 task_step 缓存

    // 读取其他缓存数据
    const cachedRooms = localStorage.getItem(CACHE_KEYS.SELECTED_ROOMS);
    const cachedTargets = localStorage.getItem(CACHE_KEYS.ROOM_TARGETS);
    const cachedRobot = localStorage.getItem(CACHE_KEYS.SELECTED_ROBOT);

    console.log("缓存数据检查:", {
      cachedRooms: cachedRooms ? "存在" : "不存在",
      cachedTargets: cachedTargets ? "存在" : "不存在",
      cachedRobot: cachedRobot ? "存在" : "不存在"
    });

    if (cachedRooms && cachedTargets && cachedRobot) {
      try {
        const parsedRooms = JSON.parse(cachedRooms);
        const parsedTargets = JSON.parse(cachedTargets);
        robotInfo.value = JSON.parse(cachedRobot);

        // 用于记录每个家具出现的次数
        const furnitureCount = {};

        // 统计所有家具出现的次数
        Object.entries(parsedTargets).forEach(([room, data]) => {
          if (data.targets) {
            data.targets.forEach((target) => {
              furnitureCount[target] = (furnitureCount[target] || 0) + 1;
            });
          }
        });

        // 用于记录每个重复家具当前的索引
        const currentIndex = {};

        // 更新目标数据，为重复家具添加下标
        const finalTargets = {};
        Object.entries(parsedTargets).forEach(([room, data]) => {
          finalTargets[room] = {
            targets:
              data.targets?.map((target) => {
                if (furnitureCount[target] > 1) {
                  currentIndex[target] = (currentIndex[target] || 0) + 1;
                  return `${target}_${currentIndex[target]}`;
                }
                return target;
              }) || [],
            isPrimary: data.isPrimary,
          };
        });

        selectedRooms.value = parsedRooms;
        roomTargets.value = finalTargets;

        console.log("数据设置完成:", {
          selectedRooms: selectedRooms.value,
          roomTargets: roomTargets.value,
          robotInfo: robotInfo.value
        });

        // 调用 setting 接口获取初始 task_id
        await initializeTask();
      } catch (error) {
        console.error("解析缓存数据失败:", error);
        handleNavigateToConfig();
      }
    } else {
      console.log("缺少必要的缓存数据");
      handleNavigateToConfig();
    }
  });

  // 组件卸载时清理轮询定时器
  onUnmounted(() => {
    if (pollingTimer.value) {
      clearInterval(pollingTimer.value);
      pollingTimer.value = null;
      console.log("task timeout1");
    }
  });

  // 添加对 showDecisionResult 的监听
  watch(showDecisionResult, (newValue) => {
    // 当抽屉关闭时
    if (!newValue && pollingTimer.value) {
      // 清除轮询定时器
      clearInterval(pollingTimer.value);
      pollingTimer.value = null;
    }
  });

  const refreshPage = () => {
    window.location.reload();
  };

  // 获取应用访问凭证（对用户完全透明）
  const getAccessToken = async () => {
    try {
      accessTokenLoading.value = true;

      // 从 sessionStorage 获取 app_id 和 app_secret
      const appId = sessionStorage.getItem("app_id");
      const appSecret = sessionStorage.getItem("app_secret");

      if (!appId || !appSecret) {
        console.error("未找到应用凭证，请先访问应用基础信息页面");
        return false;
      }

      // 调用获取访问凭证的 API
      const response = await doGet(
        `/open-apis/base/auth/access_token?app_id=${appId}&app_secret=${appSecret}`
      );

      // 根据新的返回结构进行适配
      if (response.code === 0 && response.data && response.data.accessToken) {
        // 使用正确的字段名 accessToken 而不是 access_token
        accessToken.value = response.data.accessToken;

        // 保存到 sessionStorage 以便其他页面使用
        sessionStorage.setItem("api_access_token", response.data.accessToken);

        // 保存过期时间
        if (response.data.expire) {
          // 保存过期秒数
          sessionStorage.setItem(
            "api_access_token_expire",
            String(response.data.expire)
          );

          // 计算过期时间戳并保存
          const expireTimestamp = Date.now() + response.data.expire * 1000;
          sessionStorage.setItem(
            "api_access_token_expire_time",
            String(expireTimestamp)
          );

          // 更新组件内的过期时间
          tokenExpireTime.value = expireTimestamp;
        }

        // 成功获取凭证，但不显示消息
        return true;
      } else {
        console.error("获取访问凭证失败：", response.message || "未知错误");
        return false;
      }
    } catch (error) {
      console.error("获取访问凭证出错:", error);
      return false;
    } finally {
      accessTokenLoading.value = false;
    }
  };

  // 检查访问凭证是否有效
  const checkAccessToken = async () => {
    // 如果没有访问凭证，直接获取新的
    if (!accessToken.value) {
      // 尝试从 sessionStorage 获取
      const storedToken = sessionStorage.getItem("api_access_token");
      const storedExpireTime = sessionStorage.getItem(
        "api_access_token_expire_time"
      );

      if (storedToken && storedExpireTime) {
        // 检查是否过期
        const expireTime = parseInt(storedExpireTime, 10);
        const now = Date.now();

        // 如果未过期，直接使用存储的令牌
        if (expireTime > now) {
          accessToken.value = storedToken;
          tokenExpireTime.value = expireTime;
          return true;
        }
      }

      // 如果没有令牌或已过期，获取新的
      return await getAccessToken();
    }

    // 如果有访问凭证，检查是否即将过期（提前5分钟刷新）
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    if (tokenExpireTime.value && tokenExpireTime.value - now < fiveMinutes) {
      // 令牌即将过期，获取新的
      return await getAccessToken();
    }

    return true;
  };

  // 获取机器人图片
  const getRobotImage = (imagePath) => {
    try {
      const path = imagePath.startsWith("/") ? imagePath.slice(1) : imagePath;
      return new URL(`../../assets/${path}`, import.meta.url).href;
    } catch (error) {
      console.error("加载图片失败:", error);
      return "";
    }
  };

  // 添加初始化任务的函数
  const initializeTask = async () => {
    try {
      loading.value = true;
      settingError.value = false; // 重置错误状态
      settingErrorMessage.value = ""; // 重置错误信息

      // 在初始化任务前检查访问凭证（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        const errorMsg = "系统准备失败，请稍后重试";
        message.error(errorMsg);
        settingError.value = true; // 设置错误状态
        settingErrorMessage.value = errorMsg; // 设置错误信息
        loading.value = false;
        return;
      }

      // 构建基础请求数据
      const envSettings = {};
      // 用于记录每个家具出现的次数
      const furnitureCount = {};

      // 第一次遍历：统计所有家具出现的次数
      selectedRooms.value.forEach((room) => {
        if (roomTargets.value[room]?.targets) {
          roomTargets.value[room].targets.forEach((target) => {
            furnitureCount[target] = (furnitureCount[target] || 0) + 1;
          });
        }
      });

      // 用于记录每个重复家具当前的索引
      const currentIndex = {};

      // 第二次遍历：构建envSettings，为重复家具添加下标
      selectedRooms.value.forEach((room) => {
        envSettings[room] = {};
        if (roomTargets.value[room]?.targets) {
          roomTargets.value[room].targets.forEach((target) => {
            if (furnitureCount[target] > 1) {
              // 如果是重复家具，添加下标
              currentIndex[target] = (currentIndex[target] || 0) + 1;
              const targetWithIndex = `${target}_${currentIndex[target]}`;
              envSettings[room][targetWithIndex] = [];
            } else {
              // 如果不是重复家具，直接使用原名
              envSettings[room][target] = [];
            }
          });
          // 如果是主要房间（主人所在房间），添加主人作为家具
          if (roomTargets.value[room].isPrimary) {
            envSettings[room]["主人"] = [];
          }
        }
      });

      const requestData = {
        robot_id: robotInfo.value?.id || 0,
        task_command: robotInfo.value?.command || "",
        robot_location: robotInfo.value?.currentRoom || "",
        env_settings: envSettings,
      };

      // 创建请求头，添加 Bearer 认证
      const headers = {
        Authorization: `Bearer ${accessToken.value}`,
      };

      // 调用 setting 接口
      const settingRes = await doPost(
        "/open-apis/app/decision/setting",
        requestData,
        headers
      );

      if (settingRes.code !== 0) {
        settingError.value = true; // 设置错误状态
        settingErrorMessage.value = settingRes.msg || "初始化任务失败"; // 设置具体错误信息
        throw new Error(settingRes.msg || "初始化任务失败");
      }

      // 缓存 task_id 和 task_step
      localStorage.setItem(CACHE_KEYS.TASK_ID, settingRes.data.taskId);
      localStorage.setItem(CACHE_KEYS.TASK_STEP, settingRes.data.taskStep); // 新增缓存 task_step

      // 更新决策结果
      decisionResult.value = {
        taskId: settingRes.data.taskId,
        taskStatus: settingRes.data.taskStatus,
        robotAction: settingRes.data.robotAction || [],
        traceId: "",
      };

      // 设置成功状态
      settingError.value = false;
      settingErrorMessage.value = ""; // 清空错误信息
    } catch (error) {
      console.error("初始化任务错误:", error);
      settingError.value = true; // 设置错误状态

      // 尝试从不同的错误响应结构中提取错误信息
      let errorMsg = "初始化任务失败，请刷新页面重试";

      if (error.response && error.response.data) {
        // HTTP 错误响应，从 response.data 中获取错误信息
        if (error.response.data.msg) {
          errorMsg = error.response.data.msg;
        } else if (error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (typeof error.response.data === "string") {
          errorMsg = error.response.data;
        }
      } else if (error.message) {
        // 其他类型的错误
        errorMsg = error.message;
      }

      settingErrorMessage.value = errorMsg; // 设置具体错误信息
      message.error(errorMsg);
    } finally {
      loading.value = false;
    }
  };

  // 抽取导航逻辑为单独的方法
  const handleNavigateToConfig = () => {
    message.error("配置信息不完整，请重新配置");
    router.replace({
      name: "robot-config",
      query: { scene: route.query.scene },
    });
  };

  const handleBack = () => {
    router.back();
  };

  // 处理图片上传
  const handleImageUpload = ({ file }) => {
    if (!file || !(file.file instanceof File)) return;
    if (settingError.value) {
      message.error(
        settingErrorMessage.value || "系统初始化失败，请刷新页面重试"
      );
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      imageUrl.value = e.target.result;

      // 更新决策结果，保留 taskId
      decisionResult.value = {
        ...decisionResult.value, // 保留现有数据，包括 taskId
        imageUrl: e.target.result,
        robotAction: [],
        taskStatus: "",
      };
    };
    reader.readAsDataURL(file.file);
  };

  // 上传前验证
  const beforeUpload = (file) => {
    if (settingError.value) {
      message.error(
        settingErrorMessage.value || "系统初始化失败，请刷新页面重试"
      );
      return false;
    }
    if (file.size > MAX_FILE_SIZE) {
      message.error("图片大小不能超过10MB");
      return false;
    }
    return true;
  };

  // 删除图片
  const removeImage = (e) => {
    e.stopPropagation();
    imageUrl.value = "";
    decisionCount.value = 0;

    // 只清空图片相关的数据，保留 taskId
    decisionResult.value = {
      ...decisionResult.value, // 保留现有数据
      imageUrl: "", // 只清空图片URL
      robotAction: [], // 清空动作列表
      taskStatus: "", // 清空状态
    };
  };

  // 格式化时间的方法
  const formatTime = (date) => {
    return new Intl.DateTimeFormat("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    }).format(date);
  };

  // 处理接受按钮点击
  const handleAccept = () => {
    decisionResult.value.ackStatus = "ACCEPT";
    message.success("已接受决策结果");
    showDecisionResult.value = false;
  };

  // 处理拒绝按钮点击
  const handleReject = () => {
    decisionResult.value.ackStatus = "REJECT";
    message.warning("已拒绝决策结果");
    showDecisionResult.value = false;
  };

  const handleCopy = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success("复制成功");
    } catch (err) {
      message.error("复制失败");
      console.error("复制失败:", err);
    }
  };



  // 修改 pollTaskStatus 函数
  const pollTaskStatus = async (taskId) => {
    try {
      console.log("polling count = " + decisionCount.value);

      decisionCount.value++;

      // 确保系统准备就绪（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        clearInterval(pollingTimer.value);
        message.error("系统连接中断，请刷新页面重试");
        loading.value = false;
        return;
      }

      // 获取最新的 task_step 缓存值
      const taskStep = localStorage.getItem(CACHE_KEYS.TASK_STEP);

      // 判断当前环境
      const isDevelopment = process.env.NODE_ENV === "development";
      // 构建查询参数
      const queryParams = new URLSearchParams({
        task_id: taskId,
        task_step: taskStep,
      });
      // 在开发环境下添加调试参数
      if (isDevelopment) {
        queryParams.append("is_debug", "true");
      }

      // 使用构建好的查询参数发起请求
      const res = await doGet(
        `/open-apis/app/decision/result?${queryParams.toString()}`
      );

      if (res.code === 0) {
        // 从响应头中获取 X-TRACE-ID
        const traceId = res.headers?.get("X-TRACE-ID") || "";

        // 更新任务状态
        decisionResult.value = {
          ...decisionResult.value,
          taskId: res.data.taskId,
          taskStatus: res.data.taskStatus,
          robotAction: res.data.robotAction || [],
          traceId,
        };

        if (res.data.robotAction && res.data.robotAction.length === 1) {
          loading.value = false;
          showDecisionResult.value = true;
          clearInterval(pollingTimer.value);
          pollingTimer.value = null;

          message.success("决策已全部完成");
        }
        // 当任务状态为 DONE 时，显示抽屉并停止轮询
        if (res.data.taskStatus === "DONE") {
          loading.value = false;
          showDecisionResult.value = true;
          clearInterval(pollingTimer.value);
          pollingTimer.value = null;
          message.success("当前步骤决策完毕");
        }
      }
    } catch (error) {
      console.error("查询任务状态失败:", error);
      clearInterval(pollingTimer.value);
      pollingTimer.value = null;
      message.error("查询任务状态失败");
    }
  };

  // 修改处理决策的方法
  const handleDecision = async () => {
    if (settingError.value) {
      message.error(
        settingErrorMessage.value || "系统初始化失败，请刷新页面重试"
      );
      return;
    }
    if (!imageUrl.value) {
      message.error("请先上传图片");
      return;
    }

    console.log(
      "current robot action length = " + decisionResult.value.robotAction.length
    );
    const hasDone = decisionResult.value.robotAction.some(
      (action) => action === "Done"
    );

    if (hasDone) {
      message.error("当前决策任务已完成，请重置");
      return;
    }
    try {
      loading.value = true;

      // 获取上传参数并上传图片
      const uploadParams = await getUploadParams();
      if (uploadParams.code !== 0) {
        throw new Error("获取上传参数失败");
      }

      // 将base64转换为文件并上传
      const binaryData = atob(imageUrl.value.split(",")[1]);
      const array = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        array[i] = binaryData.charCodeAt(i);
      }
      const blob = new Blob([array], { type: "image/png" });

      const response = await fetch(uploadParams.data.url, {
        method: "PUT",
        body: blob,
      });

      if (response.status !== 200) {
        const responseText = await response.text();
        console.error("上传失败响应内容:", responseText);
        throw new Error(`上传失败: ${response.status} ${responseText}`);
      }

      // 获取缓存的 task_id 和 task_step
      const taskId = localStorage.getItem(CACHE_KEYS.TASK_ID);
      const taskStep = localStorage.getItem(CACHE_KEYS.TASK_STEP);
      if (!taskId) {
        throw new Error("未找到任务ID，请刷新页面重试");
      }

      // 构建请求数据
      const envSettings = {};
      selectedRooms.value.forEach((room) => {
        envSettings[room] = {};
        if (roomTargets.value[room]?.targets) {
          roomTargets.value[room].targets.forEach((target) => {
            envSettings[room][target] = [];
          });
        }
      });

      // 在调用 working 接口前检查访问凭证（对用户透明）
      const tokenSuccess = await checkAccessToken();
      if (!tokenSuccess) {
        message.error("系统准备失败，请稍后重试");
        loading.value = false;
        return;
      }

      const requestData = {
        robot_id: robotInfo.value?.id || 0,
        task_command: robotInfo.value?.command || "",
        robot_location: robotInfo.value?.currentRoom || "",
        image: uploadParams.data.key,
        env_settings: envSettings,
        task_id: taskId,
        task_step: taskStep,
        ack_status: decisionResult.value.ackStatus || "", // 添加用户反馈状态
      };

      // 创建请求头，添加 Bearer 认证
      const headers = {
        Authorization: `Bearer ${accessToken.value}`,
      };

      // 调用 working 接口
      const workingRes = await doPost(
        "/open-apis/app/decision/working",
        requestData,
        headers
      );

      if (workingRes.code !== 0) {
        throw new Error("决策失败");
      }

      // 更新缓存的 task_step
      localStorage.setItem(CACHE_KEYS.TASK_STEP, workingRes.data.taskStep);

      // 更新决策结果
      decisionResult.value = {
        imageUrl: imageUrl.value,
        taskId: workingRes.data.taskId,
        taskStatus: workingRes.data.taskStatus,
        robotAction: workingRes.data.robotAction || [],
        traceId: "",
        hasStarted: true, // 设置标志位
      };

      // 开始轮询任务状态
      if (pollingTimer.value) {
        clearInterval(pollingTimer.value);
      }

      pollingTimer.value = setInterval(() => {
        if (decisionCount.value > 180) {
          clearInterval(pollingTimer.value);
          decisionCount.value = 0;
          loading.value = false;
          message.error("决策超时，请重试");
        }
        pollTaskStatus(workingRes.data.taskId);
      }, 1000);
    } catch (error) {
      message.error(error.message || "决策失败，请刷新页面后重试");
      console.error("决策错误:", error);
      loading.value = false;
    }
  };

  // 添加状态样式计算属性
  const getStatusType = computed(() => {
    switch (decisionResult.value.taskStatus) {
      case "SUBMIT_SUCCESS":
        return "warn";
      case "DONE":
        return "success";
      case "FAILED":
        return "error";
      default:
        return "info";
    }
  });

  // 添加获取房间显示名称的方法
  const getRoomDisplayName = (room) => {
    // 如果房间名称包含下标（包含下划线），直接返回原始名称
    if (room.includes("_")) {
      return room;
    }
    return room;
  };

  // 添加获取目标显示名称的方法
  const getTargetDisplayName = (room, target) => {
    // 如果目标名称包含下标（包含下划线），将下划线替换为序号显示
    if (target.includes("_")) {
      const [name, index] = target.split("_");
      return `${name}_${index}`;
    }
    return target;
  };





  // 更新返回对象，包含所有方法
  const result = {
    // 响应式数据
    scene,
    selectedRooms,
    roomTargets,
    robotInfo,
    decisionCount,
    imageUrl,
    loading,
    decisionResult,
    showDecisionResult,
    taskInfo,
    pollingTimer,
    decisionHistory,
    accessToken,
    accessTokenLoading,
    tokenExpireTime,
    settingError,
    settingErrorMessage,
    CACHE_KEYS,
    MAX_FILE_SIZE,

    // 计算属性
    getStatusType,

    // 方法
    refreshPage,
    getAccessToken,
    checkAccessToken,
    getRobotImage,
    initializeTask,
    handleNavigateToConfig,
    handleBack,
    handleImageUpload,
    beforeUpload,
    removeImage,
    formatTime,
    handleAccept,
    handleReject,
    handleCopy,
    pollTaskStatus,
    handleDecision,
    getRoomDisplayName,
    getTargetDisplayName,
  };

  return result;
}
