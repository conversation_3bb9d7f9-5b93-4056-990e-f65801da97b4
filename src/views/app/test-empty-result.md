# 空结果展示逻辑优化测试

## 优化内容总结

### 1. 结果标题优化
- **原逻辑**: 当 `labels` 为空时，标题显示为空字符串
- **新逻辑**: 根据不同功能类型显示相应的"未识别"提示
  - 全功能: "未识别到指定的物体"
  - 检测: "未检测到指定的物体"
  - 分割: "未找到可分割的物体"
  - 等等...

### 2. 结果展示区域优化
- **原逻辑**: 只检查 `labels` 是否有内容
- **新逻辑**: 检查所有可能的结果数据（`hasAnyResults` 计算属性）
  - labels
  - graspImage
  - angleImage
  - maskImage

### 3. 空状态提示优化
- **原逻辑**: 没有针对空结果的特殊处理
- **新逻辑**: 
  - 显示友好的空状态界面
  - 根据功能类型提供针对性建议
  - 提供重试按钮

### 4. 建议系统
根据不同的感知功能提供不同的优化建议：

#### 全功能识别
- 确保图片中包含您指定的物体
- 检查物体是否被遮挡或模糊
- 尝试调整图片角度或光线条件
- 确认物体名称是否准确

#### 检测功能
- 确保图片中包含要检测的物体
- 检查物体尺寸是否合适（不要过小或过大）
- 确保图片清晰度足够
- 尝试不同的拍摄角度

#### 分割功能
- 确保物体边界清晰可见
- 检查物体与背景的对比度
- 避免复杂背景干扰
- 确保物体完整出现在图片中

#### 属性描述
- 确保物体特征清晰可见
- 检查图片光线是否充足
- 确认物体名称和问题描述准确
- 尝试更清晰的图片

#### 角度预测
- 确保物体轮廓清晰
- 检查物体是否完整显示
- 避免物体重叠或遮挡
- 确保图片角度合适

#### 抓取点预测
- 确保物体适合抓取
- 检查物体表面是否清晰可见
- 避免物体过于复杂的形状
- 确保抓取面可见

#### 关键点预测
- 确保关键点区域清晰
- 检查物体特征是否明显
- 避免关键点被遮挡
- 尝试更好的拍摄角度

## 测试场景

### 场景1: 完全空结果
```json
{
  "code": 0,
  "data": {
    "taskId": "PT_46VVLECYATX9Z6EXRB3CEJVST3AXW",
    "taskStatus": "DONE",
    "taskResult": {
      "angeleImage": null,
      "angles": [],
      "angles3D": [],
      "answers": [],
      "boxes": [],
      "croppedImagesListAngle": [],
      "croppedImagesListBbox": [],
      "croppedImagesListGrasp": [],
      "croppedImagesListPoint": [],
      "croppedImagesListSegment": [],
      "graspImage": null,
      "grasps": [],
      "labels": [],
      "maskData": [],
      "maskImage": [],
      "points": [],
      "questions": [],
      "scores": []
    }
  },
  "message": "OK"
}
```

**预期展示效果**:
1. 任务状态显示为 "DONE"（绿色）
2. 结果标题显示对应功能的"未识别"提示
3. 显示空状态界面，包含：
   - 搜索图标
   - 针对性的优化建议列表
   - "重新获取结果"按钮

### 场景2: 有图片但无标签
```json
{
  "taskResult": {
    "labels": [],
    "graspImage": "https://example.com/grasp.jpg",
    "angleImage": null,
    // ... 其他字段
  }
}
```

**预期展示效果**:
1. 显示抓取点预测图片
2. 不显示结果卡片（因为没有标签）
3. 整体被 `hasAnyResults` 判定为有结果

## 用户体验改进

1. **明确的状态反馈**: 用户明确知道任务已完成，只是没有识别到目标
2. **针对性建议**: 根据不同功能提供具体的改进建议
3. **操作引导**: 提供重试按钮，方便用户重新尝试
4. **视觉友好**: 使用图标和结构化布局，避免空白页面
