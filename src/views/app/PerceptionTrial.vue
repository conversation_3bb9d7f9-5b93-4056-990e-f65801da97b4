<template>
  <div class="perception-trial">
    <n-card>
      <!-- 添加顶部操作栏 -->
      <div class="trial-header">
        <div class="left-actions">
          <n-button quaternary circle @click="$emit('close')">
            <template #icon>
              <n-icon>
                <ArrowLeftOutline />
              </n-icon>
            </template>
          </n-button>
          <span class="page-title">千诀·感知模型全家桶</span>
        </div>
        <div class="right-actions">
          <n-button
            type="success"
            v-if="!is_ability_added"
            @click="handleAddAbility"
          >
            添加能力
          </n-button>
          <n-button type="error" v-else @click="handleRemoveAbility">
            删除能力
          </n-button>
        </div>
      </div>

      <!-- 重构后的左右布局 -->
      <div class="main-content">
        <!-- 左侧输入区域 -->
        <div class="left-panel">
          <!-- 图片上传区域 -->
          <div class="upload-section">
            <div class="section-title">图片上传</div>
            <!-- 添加 tab 切换 -->
            <n-tabs v-model:value="active_tab" type="segment" size="large">
              <n-tab-pane name="2d" tab="2D">
                <div class="upload-box">
                  <n-upload
                    accept="image/*"
                    :show-file-list="false"
                    @change="handleImageUpload"
                    @drop.prevent="handleDrop"
                    :before-upload="beforeUpload"
                  >
                    <div class="upload-trigger">
                      <n-icon size="48" class="upload-icon">
                        <image-outline />
                      </n-icon>
                      <p class="upload-text">点击上传RGB图片或拖拽到此处</p>
                      <div class="image-preview" v-if="image_url">
                        <img :src="image_url" alt="预览图" />
                        <div class="image-actions">
                          <n-button
                            quaternary
                            circle
                            class="remove-image"
                            @click.stop="removeImage"
                          >
                            <template #icon>
                              <n-icon>
                                <TrashOutline />
                              </n-icon>
                            </template>
                          </n-button>
                          <n-button
                            quaternary
                            circle
                            class="zoom-image"
                            @click.stop="showLargeImage(image_url)"
                          >
                            <template #icon>
                              <n-icon>
                                <SearchOutline />
                              </n-icon>
                            </template>
                          </n-button>
                        </div>
                      </div>
                    </div>
                  </n-upload>
                </div>
              </n-tab-pane>

              <n-tab-pane name="3d" tab="3D">
                <div class="upload-box-container upload-box-container-3d">
                  <div class="upload-box">
                    <n-upload
                      accept="image/*"
                      :show-file-list="false"
                      @change="handleRGBImageUpload"
                      @drop.prevent="handleRGBDrop"
                      :before-upload="beforeUpload"
                    >
                      <div class="upload-trigger">
                        <n-icon size="48" class="upload-icon">
                          <image-outline />
                        </n-icon>
                        <p class="upload-text">点击上传RGB图片或拖拽到此处</p>
                        <div class="image-preview" v-if="rgb_image_url">
                          <img :src="rgb_image_url" alt="RGB预览图" />
                          <div class="image-actions">
                            <n-button
                              quaternary
                              circle
                              class="remove-image"
                              @click.stop="removeRGBImage"
                            >
                              <template #icon>
                                <n-icon>
                                  <TrashOutline />
                                </n-icon>
                              </template>
                            </n-button>
                            <n-button
                              quaternary
                              circle
                              class="zoom-image"
                              @click.stop="showLargeImage(rgb_image_url)"
                            >
                              <template #icon>
                                <n-icon>
                                  <SearchOutline />
                                </n-icon>
                              </template>
                            </n-button>
                          </div>
                        </div>
                      </div>
                    </n-upload>
                  </div>

                  <div class="upload-box">
                    <n-upload
                      accept="image/*"
                      :show-file-list="false"
                      @change="handleRGBDImageUpload"
                      @drop.prevent="handleRGBDDrop"
                      :before-upload="beforeUpload"
                    >
                      <div class="upload-trigger">
                        <n-icon size="48" class="upload-icon">
                          <image-outline />
                        </n-icon>
                        <p class="upload-text">点击上传深度图片或拖拽到此处</p>
                        <div class="image-preview" v-if="rgbd_image_url">
                          <img :src="rgbd_image_url" alt="RGBD预览图" />
                          <div class="image-actions">
                            <n-button
                              quaternary
                              circle
                              class="remove-image"
                              @click.stop="removeRGBDImage"
                            >
                              <template #icon>
                                <n-icon>
                                  <TrashOutline />
                                </n-icon>
                              </template>
                            </n-button>
                            <n-button
                              quaternary
                              circle
                              class="zoom-image"
                              @click.stop="showLargeImage(rgbd_image_url)"
                            >
                              <template #icon>
                                <n-icon>
                                  <SearchOutline />
                                </n-icon>
                              </template>
                            </n-button>
                          </div>
                        </div>
                      </div>
                    </n-upload>
                  </div>
                </div>
              </n-tab-pane>
            </n-tabs>
          </div>

          <!-- 功能选择和文本输入区 -->
          <div class="input-section">
            <div class="section-title">功能配置</div>
            <!-- 功能选择 -->
            <n-select
              v-model:value="selected_function"
              :options="function_options"
              placeholder="请选择功能"
              class="function-select"
            />

            <!-- 标签输入 -->
            <div class="object-names-input">
              <n-input
                v-model:value="current_input"
                type="text"
                :placeholder="
                  object_names.length >= 10
                    ? '已达到最大标签数量（10个）'
                    : '（选填）请输入要识别的物品名称，按回车键添加标签'
                "
                class="single-line-input"
                @keydown="handleInputKeydown"
                :disabled="object_names.length >= 10"
              />
              <div class="tags-container" v-if="object_names.length > 0">
                <div class="tags-header">
                  <span class="tags-count"
                    >已添加标签 ({{ object_names.length }}/10)</span
                  >
                </div>
                <div class="tags-list">
                  <n-tag
                    v-for="(name, index) in object_names"
                    :key="index"
                    closable
                    @close="removeObjectName(index)"
                    class="object-tag"
                  >
                    {{ name }}
                  </n-tag>
                </div>
              </div>
            </div>

            <!-- 动态问题输入框 -->
            <div
              v-if="supportsQuestions && object_names.length > 0"
              class="questions-section"
            >
              <div class="section-subtitle">物品问题描述</div>
              <div
                v-for="name in object_names"
                :key="name"
                class="question-item"
              >
                <label class="question-label">{{ name }}</label>
                <n-input
                  v-model:value="object_questions[name]"
                  type="text"
                  :placeholder="`请描述关于${name}的问题`"
                  class="question-input"
                />
              </div>
            </div>
          </div>

          <!-- 发送按钮和任务信息区 -->
          <div class="action-section">
            <div class="section-title">任务控制</div>
            <n-button
              type="primary"
              :disabled="!can_submit || submitting"
              @click="handleSubmit"
              class="send-button"
            >
              <template #icon>
                <n-icon>
                  <PaperPlaneOutline />
                </n-icon>
              </template>
              {{ submitting ? "处理中" : "发送" }}
            </n-button>

            <!-- 任务信息 -->
            <div class="task-info" v-if="taskInfo">
              <div class="task-info-item">
                <span class="label">任务ID</span>
                <span class="value">
                  {{ taskInfo.taskId }}
                  <n-button
                    text
                    size="tiny"
                    @click="() => copyToClipboard(taskInfo.taskId)"
                  >
                    <template #icon>
                      <n-icon>
                        <CopyOutline />
                      </n-icon>
                    </template>
                  </n-button>
                </span>
              </div>
              <div class="task-info-item">
                <span class="label">请求ID</span>
                <span class="value">
                  {{ taskInfo.traceId }}
                  <n-button
                    text
                    size="tiny"
                    @click="() => copyToClipboard(taskInfo.traceId)"
                  >
                    <template #icon>
                      <n-icon>
                        <CopyOutline />
                      </n-icon>
                    </template>
                  </n-button>
                </span>
              </div>
              <div class="task-info-item status-item">
                <span class="label">任务状态</span>
                <div class="value">
                  <n-tag :type="getStatusType">{{ taskInfo.taskStatus }}</n-tag>
                </div>
              </div>
              <!-- 新增操作区域 -->
              <div class="task-info-item" v-if="showActionButton">
                <span class="label">可执行操作</span>
                <div class="action-buttons">
                  <n-button
                    v-if="
                      taskInfo.taskStatus === 'RUNNING' ||
                      taskInfo.taskStatus === 'PROCESSING'
                    "
                    type="warning"
                    size="small"
                    @click="handleCancelTask"
                    class="cancel-button"
                  >
                    取消任务
                  </n-button>
                  <n-button
                    v-if="taskInfo.taskStatus === 'TIMEOUT'"
                    type="primary"
                    size="small"
                    @click="retryPolling"
                    :loading="actionLoading"
                  >
                    重试任务
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧结果展示区域 -->
        <div class="right-panel">
          <div class="section-title">执行结果</div>
          <div class="result-container">
            <n-collapse-transition :show="loading">
              <div class="result-area loading">
                <n-spin size="medium">
                  <template #description>拼命处理中...</template>
                </n-spin>
              </div>
            </n-collapse-transition>

            <n-collapse-transition :show="!!taskResult">
              <div class="result-area" v-if="taskResult">
                <div class="result-summary">
                  {{ getTaskResultTitle }}
                </div>

                <!-- 当有任何识别结果时显示结果内容 -->
                <div v-if="hasAnyResults">
                  <ResultCards
                    :result="taskResult"
                    :images-list="getImagesList"
                    @mounted="() => console.log('TaskResult:', taskResult)"
                    @retry="retryPolling"
                  />

                  <!-- 修改抓取点图片显示的条件判断 -->
                  <div
                    v-if="taskResult.graspImage && taskResult.graspImage !== ''"
                    class="additional-image-container"
                  >
                    <div class="image-title">抓取点预测结果</div>
                    <div class="image-wrapper">
                      <img :src="taskResult.graspImage" alt="抓取点预测图" />
                    </div>
                  </div>
                  <!-- 修改角度图片显示的条件判断 -->
                  <div
                    v-if="taskResult.angleImage && taskResult.angleImage !== ''"
                    class="additional-image-container"
                  >
                    <div class="image-title">角度预测结果</div>
                    <div class="image-wrapper">
                      <img :src="taskResult.angleImage" alt="角度预测图" />
                    </div>
                  </div>
                </div>

                <!-- 当没有任何识别结果时显示空状态提示 -->
                <div v-else class="empty-result-with-suggestions">
                  <n-empty description="">
                    <template #icon>
                      <n-icon size="48" color="#d0d0d0">
                        <SearchOutline />
                      </n-icon>
                    </template>
                    <template #extra>
                      <div class="empty-suggestions">
                        <div class="suggestion-title">优化建议：</div>
                        <ul class="suggestion-list">
                          <li
                            v-for="suggestion in getEmptySuggestions"
                            :key="suggestion"
                          >
                            {{ suggestion }}
                          </li>
                        </ul>
                        <div class="suggestion-actions">
                          <n-button
                            type="primary"
                            @click="retryPolling"
                            size="small"
                          >
                            重新获取结果
                          </n-button>
                        </div>
                      </div>
                    </template>
                  </n-empty>
                </div>
              </div>
            </n-collapse-transition>

            <!-- 当没有结果时显示占位内容 -->
            <div v-if="!loading && !taskResult" class="empty-result">
              <n-empty description="请先提交一个感知任务">
                <template #icon>
                  <n-icon>
                    <SearchOutline />
                  </n-icon>
                </template>
              </n-empty>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <n-modal
      v-model:show="showModal"
      preset="card"
      style="width: 80%; max-width: 1200px"
    >
      <div class="large-image-container">
        <img :src="currentLargeImage" alt="大图预览" />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { usePerceptionTrial } from "./composables/usePerceptionTrial.js";
import {
  ArrowBack as ArrowLeftOutline,
  ImageOutline,
  TrashOutline,
  PaperPlaneOutline,
  CopyOutline,
  SearchOutline,
} from "@vicons/ionicons5";
import ResultCards from "@/components/ResultCards.vue";

// props 定义要放在最前面
const props = defineProps({
  ability_added: {
    type: Boolean,
    required: true,
  },
  ability_id: {
    type: [String, Number],
    required: true,
  },
  taskType: {
    type: String,
    required: true,
  },
});

// 发射关闭事件，替代原来的closeTrial方法
defineEmits(["close"]);

// 使用组合式函数
const {
  // 基础状态
  is_ability_added,
  image_url,
  selected_function,
  object_names,
  current_input,
  questions,
  object_questions,
  function_options,
  current_request,

  // 图片相关状态
  active_tab,
  rgb_image_url,
  rgbd_image_url,

  // 任务相关状态
  loading,
  taskResult,
  submitting,
  taskInfo,
  actionLoading,

  // 计算属性
  showActionButton,
  getTaskResultTitle,
  can_submit,
  shouldDisableQuestions,
  supportsQuestions,
  getImagesList,
  getStatusType,
  multi_line_placeholder,
  hasAnyResults,
  getEmptySuggestions,

  // 大图预览
  showModal,
  currentLargeImage,

  // 方法
  handleFileUpload,
  beforeUpload,
  handleDrop,
  handleRGBDrop,
  handleRGBDDrop,
  removeImage,
  removeRGBImage,
  removeRGBDImage,
  handleImageUpload,
  handleRGBImageUpload,
  handleRGBDImageUpload,
  handleSubmit,
  handleCancelTask,
  handleAddAbility,
  handleRemoveAbility,
  copyToClipboard,
  showLargeImage,
  retryPolling,

  // 标签管理方法
  addObjectName,
  removeObjectName,
  handleInputKeydown,
} = usePerceptionTrial(props);
</script>

<style lang="scss" scoped>
@use "./styles/perception-trial";
</style>