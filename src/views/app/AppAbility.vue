<template>
  <div class="page-container">
    <!-- 如果显示试用页面则隐藏能力列表 -->
    <div v-if="!showTrial">
      <n-card>
        <!-- 原有的能力列表内容 -->
        <div class="header">
          <h2>应用能力</h2>
        </div>
        <div class="ability-list">
          <div v-for="item in abilityList" :key="item.id" class="ability-item">
            <img :src="item.abilityCover" :alt="item.abilityName">
            <h3>{{ item.abilityName }}</h3>
            <p>{{ item.abilityDesc }}</p>
            <div class="api-count">为您提供了 {{ item.apiCount }} 个API</div>
            <div class="button-group">
              <n-button 
                type="info"
                @click="handleTryAbility(item.trialPage, item)"
                class="action-button"
                :disabled="!item.trialPage"
              >
                {{ item.trialPage ? '立即体验' : '敬请期待' }}
              </n-button>
              <n-button 
                :type="item.exists ? 'error' : 'success'"
                @click="handleAbilityClick(item.id, item.exists)"
                class="action-button"
              >
                {{ item.exists ? '删除能力' : '添加能力' }}
              </n-button>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 添加试用页面区域 -->
    <div v-else class="trial-container">
      <component 
        :is="currentTrialComponent" 
        v-if="currentTrialComponent"
        :ability_added="Boolean(currentAbilityStatus)"
        :ability_id="currentAbilityId"
        :task-type="ability_type"
        @close="closeTrial"
      />
    </div>

    <!-- 安全验证弹窗 -->
    <security-verify-modal
      v-model:show="showSecurityVerify"
      :action="currentAction"
      @verify="handleSecurityVerify"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { useDialog } from 'naive-ui'
import { doGet, doPost, doPut } from '@/utils/requests'
import SecurityVerifyModal from '@/components/system/SecurityVerifyModal.vue'
import messages from '@/utils/messages'
import { markRaw } from 'vue'

const currentAppId = inject('currentAppId')
const dialog = useDialog()
const abilityList = ref([])

// 安全验证相关
const showSecurityVerify = ref(false)
const currentAction = ref('')
const pendingAbilityId = ref(null)
const pendingExists = ref(false)

// 新增状态
const showTrial = ref(false)
const currentTrialComponent = ref(null)

// 添加当前能力状态
const currentAbilityStatus = ref(false)
const currentAbilityId = ref(null)

// 添加 ability_type ref
const ability_type = ref('detection') // 默认为检测任务

// 添加组件映射对象
const trialComponents = {
  '/app/PerceptionTrial': () => import('./PerceptionTrial.vue'),
  '/app/DecisionTrial': () => import('./DecisionTrial.vue')
  // 可以根据需要添加更多组件映射
}

const getAbilityList = async () => {
  const res = await doGet('/app-center/app/ability/list?appId=' + currentAppId.value)
  if (res.code === 0) {
    abilityList.value = res.data
  }
}

const handleAbilityClick = (abilityId, exists) => {
  if (!exists) {
    // 添加能力直接处理
    handleAbility(abilityId, exists)
  } else {
    // 删除能力需要确认
    dialog.warning({
      title: '确认删除',
      content: '确定要删除该能力吗？删除后将无法使用能力提供的API。',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        pendingAbilityId.value = abilityId
        pendingExists.value = exists
        currentAction.value = '删除能力'
        showSecurityVerify.value = true
      }
    })
  }
}

const handleSecurityVerify = async () => {
  // 验证通过后执行删除操作
  await handleAbility(pendingAbilityId.value, pendingExists.value)
  // 重置状态
  pendingAbilityId.value = null
  pendingExists.value = false
  showSecurityVerify.value = false
}

const handleAbility = async (abilityId, exists) => {
  if (exists) {
    // 删除能力
    const res = await doPut('/app-center/app/acl', {
      appId: currentAppId.value,
      abilityId
    })
    if (res.code === 0) {
      messages.success('删除成功')
      if (abilityId === currentAbilityId.value) {
        currentAbilityStatus.value = false
      }
      await getAbilityList()
    }
  } else {
    // 添加能力
    const res = await doPost('/app-center/app/acl', {
      appId: currentAppId.value,
      abilityId
    })
    if (res.code === 0) {
      messages.success('添加成功')
      if (abilityId === currentAbilityId.value) {
        currentAbilityStatus.value = true
      }
      await getAbilityList()
    }
  }
}

const handleTryAbility = async (trialPage, item) => {
  if (trialPage) {
    try {
      // 检查组件是否在映射表中
      if (!trialComponents[trialPage]) {
        throw new Error(`找不到对应的试用组件: ${trialPage}`)
      }
      
      // 使用映射表加载组件
      const component = await trialComponents[trialPage]()
      currentTrialComponent.value = markRaw(component.default)
      currentAbilityStatus.value = Boolean(item.exists)
      currentAbilityId.value = item.id
      showTrial.value = true
    } catch (error) {
      messages.error('加载试用页面失败')
      console.error('Failed to load trial component:', error)
    }
  }
}

// 添加关闭试用页面的方法
const closeTrial = () => {
  showTrial.value = false
  currentTrialComponent.value = null
}

// 修改打开试用的方法，接收能力类型
const openTrial = (id, type) => {
  ability_id.value = id
  ability_type.value = type // 设置能力类型
  showTrial.value = true
}

onMounted(() => {
  getAbilityList()
})
</script>

<style scoped>
.page-container {
  /* padding: 24px; */
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.ability-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.ability-item {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  position: relative;
  min-height: 380px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  padding-bottom: 70px;
}

.ability-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.ability-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
}

.ability-item h3 {
  margin: 16px 0 8px;
  font-size: 16px;
  color: #333;
}

.ability-item p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.api-count {
  font-size: 12px;
  color: #666;
  margin: 12px 0;
  display: flex;
  align-items: center;
}

.button-group {
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-button {
  min-width: 90px;  /* 设置最小宽度确保按钮大小一致 */
  height: 34px;     /* 统一按钮高度 */
  padding: 0 16px;  /* 统一内边距 */
  font-size: 14px;  /* 统一字体大小 */
}

.trial-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
}

.trial-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}
</style>
