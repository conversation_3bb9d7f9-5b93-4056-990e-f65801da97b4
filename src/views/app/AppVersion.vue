<template>
  <div class="page-container">
    <n-card>
      <div class="header">
        <div class="header-left">
          <h2>版本管理</h2>
          <p class="description">管理应用的版本信息</p>
        </div>
        <div class="header-right">
          <n-button type="primary" @click="handleCreateVersion">
            创建版本
          </n-button>
        </div>
      </div>

      <n-data-table
        :columns="columns"
        :data="versionList"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>

    <!-- 创建版本弹窗 -->
    <n-modal
      v-model:show="showCreateModal"
      preset="dialog"
      title="创建版本"
      positive-text="确认"
      negative-text="取消"
      @positive-click="handleConfirmCreate"
      @negative-click="handleCancelCreate"
    >
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="left"
        label-width="80"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="版本号" path="version">
          <n-input v-model:value="formModel.version" placeholder="请输入版本号，如：1.0.0" />
        </n-form-item>
        <n-form-item label="版本说明" path="description">
          <n-input
            v-model:value="formModel.description"
            type="textarea"
            placeholder="请输入版本说明"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </n-form-item>
      </n-form>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, h } from 'vue'
import { NTag } from 'naive-ui'
import { doGet, doPost } from '@/utils/requests'
import messages from '@/utils/messages'

const currentAppId = inject('currentAppId')

// 列表相关
const loading = ref(false)
const versionList = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40]
})

// 表格列定义
const columns = [
  {
    title: '版本号',
    key: 'versionCode',
    width: 120
  },
  {
    title: '版本说明',
    key: 'versionDesc',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '版本类型',
    key: 'versionType',
    width: 100,
    render(row) {
      return h(
        NTag,
        {
          type: row.versionType === 'DEVELOP' ? 'info' : 'success',
          size: 'small'
        },
        { default: () => row.versionType === 'DEVELOP' ? '开发版' : '正式版' }
      )
    }
  },
  {
    title: '提交状态',
    key: 'submitState',
    width: 100,
    render(row) {
      const stateMap = {
        'DONE': { type: 'success', text: '已完成' },
        'PENDING': { type: 'warning', text: '待审核' },
        'REJECTED': { type: 'error', text: '已驳回' }
      }
      const state = stateMap[row.submitState] || { type: 'default', text: row.submitState }
      return h(
        NTag,
        {
          type: state.type,
          size: 'small'
        },
        { default: () => state.text }
      )
    }
  },
  {
    title: '提交人',
    key: 'submitUserName',
    width: 120
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  }
]

// 加载版本列表
const loadVersionList = async () => {
  loading.value = true
  try {
    const res = await doGet(
      `/app-center/app/version/page/${currentAppId.value}`,
      {
        page: pagination.value.page,
        size: pagination.value.pageSize
      }
    )
    if (res.code === 0) {
      versionList.value = res.data.records
      pagination.value.itemCount = res.data.totalRow
      pagination.value.page = res.data.pageNumber
      pagination.value.pageSize = res.data.pageSize
    }
  } catch (error) {
    messages.error('加载版本列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handlePageChange = (page) => {
  pagination.value.page = page
  loadVersionList()
}

const handlePageSizeChange = (pageSize) => {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  loadVersionList()
}

// 创建版本相关
const showCreateModal = ref(false)
const formRef = ref(null)
const formModel = ref({
  version: '',
  description: ''
})

const rules = {
  version: {
    required: true,
    message: '请输入版本号',
    trigger: 'blur'
  },
  description: {
    required: true,
    message: '请输入版本说明',
    trigger: 'blur'
  }
}

const handleCreateVersion = () => {
  showCreateModal.value = true
  formModel.value = {
    version: '',
    description: ''
  }
}

const handleConfirmCreate = async (e) => {
  e.preventDefault()
  try {
    await formRef.value?.validate()
    const res = await doPost(`/app-center/app/${currentAppId.value}/version`, formModel.value)
    if (res.code === 0) {
      messages.success('创建成功')
      showCreateModal.value = false
      loadVersionList()
    }
  } catch (error) {
    // 表单验证失败或请求失败
  }
}

const handleCancelCreate = () => {
  showCreateModal.value = false
}

onMounted(() => {
  loadVersionList()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.description {
  margin: 8px 0 0;
  color: #666;
  font-size: 14px;
}
</style>
