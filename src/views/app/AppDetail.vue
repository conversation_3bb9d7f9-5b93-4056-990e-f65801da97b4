<script setup>
import { ref, onMounted, computed, markRaw, provide } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NLayout, NLayoutSider, NMenu, NIcon,
  NSpace, NTag, NButton, NBreadcrumb, NBreadcrumbItem
} from 'naive-ui'
import {
  HomeOutline, KeyOutline, PeopleOutline,
  ExtensionPuzzleOutline, ShieldOutline,
  NotificationsOutline, LockClosedOutline,
  RocketOutline, DocumentTextOutline,
  AccessibilitySharp, LogoChrome, ServerOutline,
  CloudDoneOutline, AnalyticsOutline, BuildOutline,
  BusinessOutline, CartOutline,
  FileTrayFullOutline, GridOutline, LogoAmplify,
  LogoApple, LogoDesignernews, LogoEdge, LogoElectron
} from '@vicons/ionicons5'
import { h } from 'vue'
import { applicationApi } from '@/api/applications'
import AppBaseInfo from './AppBaseInfo.vue'  // 导入基础信息组件
import AppMember from './AppMember.vue'  // 添加这行
import AppAbility from './AppAbility.vue'
import AppACL from './AppACL.vue'
import AppVersion from './AppVersion.vue'
import AppCallbackConfig from './AppCallbackConfig.vue'
import AppAwl from './AppAWL.vue'

const route = useRoute()
const router = useRouter()
const appId = route.params.id
const activeKey = ref('basic-info')
const appInfo = ref(null)

// 添加缓存ID的ref
const currentAppId = ref('')

// 添加默认展开的菜单键值数组
const defaultExpandedKeys = ['basic', 'capabilities', 'development', 'versions', 'monitoring']

// 菜单配置
const menuOptions = [
  {
    label: '基础信息',
    key: 'basic',
    children: [
      {
        label: '凭证与基础信息',
        key: 'basic-info',
        icon: () => h(NIcon, null, { default: () => h(KeyOutline) })
      },
      {
        label: '成员管理',
        key: 'members',
        icon: () => h(NIcon, null, { default: () => h(PeopleOutline) })
      }
    ]
  },
  {
    label: '应用能力',
    key: 'capabilities',
    children: [
      {
        label: '应用能力管理',
        key: 'capability-manage',
        icon: () => h(NIcon, null, { default: () => h(ExtensionPuzzleOutline) })
      }
    ]
  },
  {
    label: '开发配置',
    key: 'development',
    children: [
      {
        label: '权限管理',
        key: 'permissions',
        icon: () => h(NIcon, null, { default: () => h(ShieldOutline) })
      },
      {
        label: '回调配置',
        key: 'events',
        icon: () => h(NIcon, null, { default: () => h(NotificationsOutline) })
      },
      {
        label: '安全设置',
        key: 'security',
        icon: () => h(NIcon, null, { default: () => h(LockClosedOutline) })
      }
    ]
  },
  {
    label: '应用发布',
    key: 'versions',
    children: [
      {
        label: '版本管理',
        key: 'publish',
        icon: () => h(NIcon, null, { default: () => h(RocketOutline) })
      }
    ]
  },
  {
    label: '运营监控',
    key: 'monitoring',
    children: [
      {
        label: '日志检索',
        key: 'logs',
        icon: () => h(NIcon, null, { default: () => h(DocumentTextOutline) })
      }
    ]
  }
]

// 返回应用中心
const handleBack = () => {
  router.push('/')
}

// 添加面包屑点击处理
const handleBreadcrumbClick = (path) => {
  if (path === 'app-center') {
    router.push('/')
  }
}

// 处理菜单选择
const handleMenuSelect = (key) => {
  activeKey.value = key
  // 如果是基础信息页面，更新路由但保持在当前页面
  if (key === 'basic-info') {
    router.push(`/app/${appId}`)
  }
  // TODO: 其他菜单项的路由处理
}

// 修改加载应用详情函数
const loadAppDetail = async () => {
  try {
    const response = await applicationApi.getApplicationDetail(appId)
    appInfo.value = response
    currentAppId.value = response.id
    console.log('Current App ID set to:', currentAppId.value)
  } catch (error) {
    console.error('Failed to load app detail:', error)
  }
}

onMounted(async () => {
  await loadAppDetail()  // 先加载应用详情

  // 确认currentAppId已经设置
  if (!currentAppId.value) {
    console.error('Failed to set currentAppId')
    return
  }

  // 根据当前路由设置初始选中的菜单项
  const path = route.path
  if (path === `/app/${appId}`) {
    activeKey.value = 'basic-info'
  }
})

// 获取图标组件
const getIconComponent = (iconName) => {
  // 这里可以复用 AppForm 中的图标映射逻辑
  const iconMap = {
    'LogoElectron': LogoElectron,
    // ... 其他图标映射
  }
  return iconMap[iconName] || LogoElectron
}

// 菜单项对应的组件映射
const menuComponents = {
  'basic-info': markRaw(AppBaseInfo),
  'members': markRaw(AppMember),
  'capability-manage': markRaw(AppAbility),
  'permissions': markRaw(AppACL),
  'events': markRaw(AppCallbackConfig),
  'security': markRaw(AppAwl),
  'publish': markRaw(AppVersion),
  'logs': null
}

// 当前显示的组件
const currentComponent = computed(() => menuComponents[activeKey.value])

// 将currentAppId通过provide提供给子组件
provide('currentAppId', currentAppId)
</script>

<template>
  <n-layout has-sider>
    <!-- 顶部信息栏 -->
    <div class="app-header">
      <n-space align="center" :size="16">
        <n-button quaternary circle class="home-button" @click="handleBack">
          <n-icon size="24">
            <home-outline />
          </n-icon>
        </n-button>
        <n-breadcrumb>
          <n-breadcrumb-item @click="handleBreadcrumbClick('app-center')" class="clickable-breadcrumb">
            应用中心
          </n-breadcrumb-item>
          <n-breadcrumb-item>
            <n-space align="center" :size="8">
              <n-icon v-if="appInfo" :component="getIconComponent(appInfo.iconName)" :color="appInfo?.iconColor" />
              <span>{{ appInfo?.appName || '加载中...' }}</span>
            </n-space>
          </n-breadcrumb-item>
        </n-breadcrumb>
      </n-space>
      <n-space align="center">
        <n-tag :type="appInfo?.appState === 'DISABLED' ? 'error' : 'success'">
          {{ appInfo?.appState === 'DISABLED' ? '停止运行' : '正常运行' }}
        </n-tag>
        <!-- 信息提示区域 -->
      </n-space>
    </div>

    <!-- 左侧菜单 -->
    <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240" :native-scrollbar="false"
      class="app-sider">
      <n-menu :value="activeKey" :options="menuOptions" :collapsed-width="64" :collapsed-icon-size="22"
        :default-expanded-keys="defaultExpandedKeys" @update:value="handleMenuSelect" />
    </n-layout-sider>

    <!-- 右侧内容区 -->
    <n-layout class="app-content">
      <!-- 根据activeKey显示对应的组件 -->
      <component 
        v-if="currentComponent" 
        :is="currentComponent" 
        :key="activeKey"
        :app-info="appInfo"
        @update="loadAppDetail"
      />
      <div v-else class="empty-content">
        该功能正在开发中...
      </div>
    </n-layout>
  </n-layout>
</template>

<style scoped>
.app-header {
  height: 64px;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.app-sider {
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  background-color: #fff;
}

.app-content {
  margin-left: 240px;
  margin-top: 64px;
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.home-button {
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clickable-breadcrumb {
  cursor: pointer;
  color: #1890ff;
}

.clickable-breadcrumb:hover {
  color: #40a9ff;
}

/* 添加图标样式 */
.app-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}
</style>