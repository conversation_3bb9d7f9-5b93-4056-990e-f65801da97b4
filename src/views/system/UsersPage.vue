<script setup>
import { ref, onMounted, h } from 'vue'
import { NSpace, NCard, NGrid, NGridItem, NDataTable, NButton, NModal, NForm, NFormItem, NInput, NTreeSelect, NSelect, NSwitch, NIcon, NTree, NDropdown } from 'naive-ui'
import { getDepartments, getDepartmentMembers, getRoles, getUserDetails, createUser, updateUser } from '@/api/users'
import { AddCircleOutline,PencilOutline,TrashOutline} from '@vicons/ionicons5'
import { Edit, TrashCan } from '@vicons/carbon'
import {PlusSquareOutlined,MinusSquareOutlined} from '@vicons/antd'
import {MoreVertical16Filled} from '@vicons/fluent'

const departmentTree = ref([])
const selectedDepartmentKeys = ref([])
const users = ref([])
const pagination = ref({
  page: 1,
  pageSize: 50,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})
const selectedRowKeys = ref([])
const showUserModal = ref(false)
const editingUser = ref({})
const roleOptions = ref([])
const userForm = ref(null)
const expandedKeys = ref([])

// 新增的状态变量
const showDepartmentModal = ref(false)
const editingDepartment = ref({})
const departmentForm = ref(null)

// 新增的部门表单规则
const departmentRules = {
  name: { required: true, message: '请输入部门名称', trigger: 'blur' },
}

const userRules = {
  username: { required: true, message: '请输入用名', trigger: 'blur' },
  nickname: { required: true, message: '请输入昵称', trigger: 'blur' },
  passwd: { required: true, message: '请输入密码', trigger: 'blur' },
  workMobile: { required: true, message: '请输入手机号', trigger: 'blur' },
  departmentId: { required: true, message: '请选择所属部门', trigger: 'change' },
  roles: { type: 'array', required: true, message: '请选择角色', trigger: 'change' }
}

const columns = [
  { title: 'ID', key: 'id', width: 80 },  // 添加回 ID 列
  { title: '昵称', key: 'nickname' },
  { title: '手机号', key: 'workMobile' },
  { 
    title: '角色', 
    key: 'roles', 
    render: (row) => {
      if (!roleOptions.value || roleOptions.value.length === 0) return ''
      return row.roles
        .map(roleId => roleOptions.value.find(role => role.value === roleId)?.label)
        .filter(Boolean)  // 过滤掉可能的 undefined 值
        .join(', ')
    }
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => {
      return h(NSpace, { align: 'center' }, {
        default: () => [
          h(NSwitch, {
            value: !row.disabled,
            onUpdateValue: (value) => toggleUserStatus(row, value)
          }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => editUser(row)
          }, { default: () => h(NIcon, { component: Edit }) }),
          h(NButton, {
            quaternary: true,
            circle: true,
            size: 'small',
            onClick: () => deleteUser(row)
          }, { default: () => h(NIcon, { component: TrashCan }) })
        ]
      })
    }
  }
]

onMounted(async () => {
  await fetchRoles()
  await fetchDepartments()
  if (departmentTree.value.length > 0) {
    selectedDepartmentKeys.value = [departmentTree.value[0].key]
    await fetchUsers(selectedDepartmentKeys.value[0])
  }
})

function buildTreeData(departments) {
  const options = []
  const map = {}

  departments.forEach(dept => {
    map[dept.id] = {
      key: dept.id.toString(),
      label: dept.name,
      children: [],
      isLeaf: true // 初始假设所有节点都是叶子节点
    }
  })

  departments.forEach(dept => {
    if (dept.parentId === 0) {
      options.push(map[dept.id])
    } else {
      const parent = map[dept.parentId]
      if (parent) {
        parent.children.push(map[dept.id])
        parent.isLeaf = false // 如果有子节点，则不是叶子节点
      }
    }
  })

  return options
}

async function fetchDepartments() {
  try {
    const response = await getDepartments()
    departmentTree.value = buildTreeData(response.data)
    if (departmentTree.value.length > 0) {
      selectedDepartmentKeys.value = [departmentTree.value[0].key]
      // 初始化 expandedKeys 为所有顶级部门的 key
      expandedKeys.value = departmentTree.value.map(dept => dept.key)
      await fetchUsers(selectedDepartmentKeys.value[0])
    }
  } catch (error) {
    console.error('Failed to fetch departments:', error)
  }
}

async function fetchUsers(departmentId) {
  try {
    const response = await getDepartmentMembers(departmentId, pagination.value.page, pagination.value.pageSize)
    users.value = response.data.map(user => ({
      ...user,
      roles: user.roles || []  // 确保每个用户都有 roles 属性，即使它可能为空
    }))
    pagination.value.itemCount = response.total
  } catch (error) {
    console.error('Failed to fetch users:', error)
    users.value = []
  }
}

async function fetchRoles() {
  try {
    const response = await getRoles()
    roleOptions.value = response.data.map(role => ({
      label: role.roleName,
      value: role.id
    }))
  } catch (error) {
    console.error('Failed to fetch roles:', error)
    roleOptions.value = []  // 如果获取失败，设置为空数组
  }
}

function handlePageChange(page) {
  pagination.value.page = page
  fetchUsers(selectedDepartmentKeys.value[0])
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  fetchUsers(selectedDepartmentKeys.value[0])
}

function handleCheck(rowKeys) {
  selectedRowKeys.value = rowKeys
}

function handleAddUser() {
  editingUser.value = {
    username: '',
    nickname: '',
    passwd: '',
    workMobile: '',
    departmentId: null,
    roles: [],
    disabled: false
  }
  showUserModal.value = true
}

async function editUser(user) {
  try {
    const response = await getUserDetails(user.id)
    editingUser.value = response.data
    showUserModal.value = true
  } catch (error) {
    console.error('Failed to get user details:', error)
  }
}

function closeUserModal() {
  showUserModal.value = false
  editingUser.value = {}
}

async function saveUser() {
  try {
    await userForm.value.validate()
    if (editingUser.value.id) {
      await updateUser(editingUser.value)
    } else {
      await createUser(editingUser.value)
    }
    closeUserModal()
    await fetchUsers(selectedDepartmentKeys.value[0])
  } catch (error) {
    console.error('Failed to save user:', error)
  }
}

async function toggleUserStatus(user, value) {
  try {
    await updateUser({ id: user.id, disabled: !value })
    await fetchUsers(selectedDepartmentKeys.value[0])
  } catch (error) {
    console.error('Failed to toggle user status:', error)
  }
}

async function deleteUser(user) {
  // Implement delete user logic
}

async function handleBatchDelete() {
  // Implement batch delete logic
}

function renderSwitcherIcon({ expanded, isLeaf }) {
  if (isLeaf) {
    return null
  }
  return expanded ? h(MinusSquareOutlined) : h(PlusSquareOutlined)
}

// 修改 overrideNodeClickBehavior 函数
const overrideNodeClickBehavior = ({ option }) => {
  console.log('overrideNodeClickBehavior', option)
    // 阻止事件冒泡，以防止触发两次
    event.stopPropagation()
    console.log('overrideNodeClickBehavior', event)
    // 更新选中状态
    selectedDepartmentKeys.value = [option.key]
    
    // 加载用户列表
    fetchUsers(option.key)
    
    // 处理展开/收起
    if (option.children && option.children.length > 0) {
      if (expandedKeys.value.includes(option.key)) {
        expandedKeys.value = expandedKeys.value.filter(key => key !== option.key)
      } else {
        expandedKeys.value = [...expandedKeys.value, option.key]
      }
    }

    // 返回 'prevent-default' 来阻止默认行为
    return 'prevent-default'
}

function handleTreeExpand(keys) {
  expandedKeys.value = keys
}

// 添加一个辅助函数来查找点
function findNodeByKey(tree, key) {
  for (const node of tree) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const found = findNodeByKey(node.children, key)
      if (found) return found
    }
  }
  return null
}

// 新增、编辑和删除部门的函数
async function addDepartment(parentKey) {
  editingDepartment.value = { parentId: parentKey, name: '' }
  showDepartmentModal.value = true
}

async function renameDepartment(key) {
  const department = findNodeByKey(departmentTree.value, key)
  if (department) {
    editingDepartment.value = { ...department }
    showDepartmentModal.value = true
  }
}

async function deleteDepartment(key) {
  // 这里应该调用后端 API 来删除部门
  // 删除成功后重新获取部门树
  await fetchDepartments()
}

async function saveDepartment() {
  try {
    await departmentForm.value.validate()
    // 这里应该调用后端 API 来保存或更新部门
    // 保存成功后重新获取部门树
    await fetchDepartments()
    showDepartmentModal.value = false
  } catch (error) {
    console.error('Failed to save department:', error)
  }
}

// 处理拖动排序
function handleDrop({ node, dragNode, dropPosition }) {
  // 这里应该调用后端 API 来更新部门顺序
  // 更新成功后重新获取部门树
  fetchDepartments()
}

// 修改 renderSuffix 函数
function renderSuffix({ option }) {
  return () => h(
    'div',
    { class: 'tree-node-action' },
    h(
      NDropdown,
      {
        trigger: 'click',
        options: [
          {
            label: '新增子部门',
            key: 'add',
            icon: renderIcon(AddCircleOutline)
          },
          {
            label: '重命名',
            key: 'rename',
            icon: renderIcon(PencilOutline)
          },
          {
            label: '删除',
            key: 'delete',
            icon: renderIcon(TrashOutline)
          }
        ],
        onSelect: (key) => {
          switch (key) {
            case 'add':
              addDepartment(option.key)
              break
            case 'rename':
              renameDepartment(option.key)
              break
            case 'delete':
              deleteDepartment(option.key)
              break
          }
        }
      },
      {
        default: () => h(NIcon, { component: MoreVertical16Filled })
      }
    )
  )
}

function renderIcon(icon) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

</script>

<template>
  <n-space vertical :size="24">
    <n-card title="">
      <n-grid :cols="12" :x-gap="24">
        <!-- 左侧组织机构树 -->
        <n-grid-item span="4">
          <n-card title="组织机构">
            <n-tree
              block-line
              :data="departmentTree"
              :expanded-keys="expandedKeys"
              :selected-keys="selectedDepartmentKeys"
              :render-suffix="renderSuffix"
              :render-switcher-icon="renderSwitcherIcon"
              :override-default-node-click-behavior="overrideNodeClickBehavior"
              selectable
              draggable
              @update:expanded-keys="handleTreeExpand"
              @drop="handleDrop"
            />
          </n-card>
        </n-grid-item>
        
        <!-- 右侧用户列表 -->
        <n-grid-item span="8">
          <n-card title="用户列表">
            <template #header-extra>
              <n-space>
                <n-button @click="handleAddUser">新增用户</n-button>
                <n-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">批量删除</n-button>
              </n-space>
            </template>
            <n-data-table
              :columns="columns"
              :data="users"
              :pagination="pagination"
              @update:page="handlePageChange"
              @update:page-size="handlePageSizeChange"
              :row-key="row => row.id"
              @update:checked-row-keys="handleCheck"
            />
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 用户编辑对话框 -->
    <n-modal v-model:show="showUserModal" :mask-closable="false">
      <n-card
        style="width: 600px"
        :title="editingUser.id ? '编辑用户' : '新增用户'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingUser"
          :rules="userRules"
          ref="userForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :style="{
            maxWidth: '640px'
          }"
        >
          <n-form-item label="用户名" path="username">
            <n-input v-model:value="editingUser.username" placeholder="请输入用户名" />
          </n-form-item>
          <n-form-item label="昵称" path="nickname">
            <n-input v-model:value="editingUser.nickname" placeholder="请输入昵称" />
          </n-form-item>
          <n-form-item label="密码" path="passwd">
            <n-input v-model:value="editingUser.passwd" type="password" placeholder="请输入密码" />
          </n-form-item>
          <n-form-item label="手机号" path="workMobile">
            <n-input v-model:value="editingUser.workMobile" placeholder="请输入手机号" />
          </n-form-item>
          <n-form-item label="所属部门" path="departmentId">
            <n-tree-select
              v-model:value="editingUser.departmentId"
              :options="departmentTree"
              placeholder="请选择所属部门"
            />
          </n-form-item>
          <n-form-item label="角色" path="roles">
            <n-select
              v-model:value="editingUser.roles"
              multiple
              :options="roleOptions"
              placeholder="请选择角色"
            />
          </n-form-item>
          <n-form-item label="状态" path="disabled">
            <n-switch v-model:value="editingUser.disabled" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="closeUserModal">取消</n-button>
            <n-button type="primary" @click="saveUser">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <!-- 部门编辑对话框 -->
    <n-modal v-model:show="showDepartmentModal" :mask-closable="false">
      <n-card
        style="width: 400px"
        :title="editingDepartment.key ? '编辑部门' : '新增部门'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          :model="editingDepartment"
          :rules="departmentRules"
          ref="departmentForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-form-item label="部门名称" path="name">
            <n-input v-model:value="editingDepartment.name" placeholder="请输入部门名称" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showDepartmentModal = false">取消</n-button>
            <n-button type="primary" @click="saveDepartment">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </n-space>
</template>

<style scoped>
.n-tree {
  --n-item-height: 40px;
}

/* 添加以下样式以调整操作列的布局 */
.n-data-table .n-button.n-button--quaternary {
  padding: 0;
  margin: 0 4px;
}

/* 可以添加以下样式来调整展开/收起图标的大小 */
.n-tree .n-tree-node-switcher {
  transform: none !important;
}

/* 调整图标大小和对齐方式 */
.n-tree .n-tree-node-switcher svg {
  font-size: 18px;
  width: 1em;
  height: 1em;
}

/* 确保叶子节点没有左边 */
.n-tree .n-tree-node-content__prefix {
  width: auto;
}

/* 隐藏叶子节点的切换器 */
.n-tree .n-tree-node--leaf .n-tree-node-switcher {
  visibility: hidden;
  width: 0;
}

/* 修改节点操作按钮的样式 */
.n-tree .n-tree-node-content {
  position: relative;
}

.tree-node-action {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.n-tree-node:hover .tree-node-action,
.n-tree-node--selected .tree-node-action {
  opacity: 1;
}

.tree-node-action .n-icon {
  font-size: 16px;
}

/* 确保下拉菜单在树节点之上 */
.n-dropdown-menu {
  z-index: 1000;
}
</style>
