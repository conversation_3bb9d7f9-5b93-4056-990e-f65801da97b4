<template>
    <div class="login-container">
        <div class="login-box">
            <div class="login-content">
                <div class="login-form-section">
                    <div class="login-logo">
                        <img src="@/assets/images/qj-logo-Dgc-aGJV.png" alt="Logo" style="width: 450px; height: auto;">
                    </div>
                    <div class="login-header">
                    </div>
                    <div class="login-form">
                        <div v-if="!showVerificationInput && !showPasswordInput && !showOrganizationInput && !showSuccessView"
                            class="input-item">
                            <div class="input-wrapper">
                                <n-input
                                    ref="emailInput"
                                    v-model:value="email"
                                    placeholder="请输入您的邮箱"
                                    autocomplete="off"
                                    :input-props="{ style: 'text-align: left; font-size: 18px;' }"
                                    @blur="validateEmail"
                                    @keyup.enter="handleEnter"
                                    :status="emailError ? 'error' : undefined"
                                >
                                    <template #prefix>
                                        <n-icon><mail-outline /></n-icon>
                                    </template>
                                </n-input>
                                <span v-if="emailError" class="error-message">{{ emailError }}</span>
                            </div>
                            <div class="button-wrapper">
                                <n-button type="primary" class="send-code-button" @click="sendVerificationCode"
                                    :disabled="!isEmailValid || isSending">
                                    {{ isSending ? `重新发送 (${countdown})` : '发送验证码' }}
                                </n-button>
                            </div>
                        </div>
                        <transition name="slide-fade">
                            <div v-if="showVerificationInput && !showPasswordInput && !showOrganizationInput && !showSuccessView" 
                                class="verification-step">
                                <p class="verification-prompt">请输入 {{ email }} 收到的验证码</p>
                                <div class="verification-code-input">
                                    <div class="code-input-wrapper">
                                        <input v-for="(digit, index) in 6" :key="index"
                                            v-model="verificationCode[index]" type="text" maxlength="1"
                                            @input="onCodeInput(index)" @keydown="onKeyDown($event, index)"
                                            @paste.prevent="onPaste" ref="codeInputs">
                                    </div>
                                </div>
                                <div class="resend-wrapper">
                                    <n-button text @click="resendCode" :disabled="isSending">
                                        {{ isSending ? `重新发送 (${countdown})` : '重新发送验证码' }}
                                    </n-button>
                                </div>
                            </div>
                        </transition>
                        <transition name="slide-fade">
                            <div v-if="showPasswordInput && !showOrganizationInput && !showSuccessView" 
                                class="password-step">
                                <div class="password-input-wrapper">
                                    <n-input
                                        ref="passwordInput"
                                        v-model:value="password"
                                        :type="showPassword ? 'text' : 'password'"
                                        placeholder="请输入密码"
                                        @keyup.enter="confirmPassword"
                                        @input="filterPassword"
                                    >
                                        <template #suffix>
                                            <n-icon @click="togglePasswordVisibility" class="password-toggle">
                                                <eye-outline v-if="showPassword" />
                                                <eye-off-outline v-else />
                                            </n-icon>
                                        </template>
                                    </n-input>
                                </div>
                                <div class="password-input-wrapper">
                                    <n-input v-model:value="confirmPasswordValue"
                                        :type="showPassword ? 'text' : 'password'" placeholder="请再次输入密码"
                                        @keyup.enter="confirmPassword" @input="filterConfirmPassword" />
                                </div>
                                <div class="button-wrapper password-button-wrapper">
                                    <n-button type="primary" @click="confirmPassword" :disabled="!isPasswordValid">
                                        下一步
                                    </n-button>
                                </div>
                                <p class="password-hint">密码至少9位，需包含大小写字母、数字、符号中的任意3种</p>
                            </div>
                        </transition>
                        <transition name="slide-fade">
                            <div v-if="showOrganizationInput && !showSuccessView" 
                                class="organization-step">
                                <div class="organization-input-wrapper">
                                    <n-input
                                        ref="organizationInput"
                                        v-model:value="organizationName"
                                        placeholder="请输入个人或组织名称"
                                        @blur="validateOrganizationName"
                                        maxlength="32"
                                    />
                                    <span v-if="organizationNameError" class="error-message">{{ organizationNameError }}</span>
                                </div>
                                <div class="input-wrapper">
                                    <n-input
                                        v-model:value="userName"
                                        placeholder="请输入您的称呼"
                                        @blur="validateUserName"
                                        maxlength="32"
                                    />
                                    <span v-if="userNameError" class="error-message">{{ userNameError }}</span>
                                </div>
                                <div class="input-wrapper">
                                    <n-input
                                        v-model:value="phoneNumber"
                                        placeholder="请输入您的手机号码"
                                        @blur="validatePhoneNumber"
                                        @input="validatePhoneNumber"
                                        @keyup.enter="handlePhoneNumberEnter"
                                        maxlength="11"
                                    />
                                    <span v-if="phoneNumberError" class="error-message">{{ phoneNumberError }}</span>
                                </div>
                                <div class="button-wrapper organization-button-wrapper">
                                    <n-button
                                        type="primary"
                                        @click="completeRegistration"
                                        :disabled="!isOrganizationInfoValid"
                                    >
                                        完成注册
                                    </n-button>
                                </div>
                            </div>
                        </transition>
                        <transition name="slide-fade">
                            <div v-if="showSuccessView" class="success-step">
                                <div class="success-icon">
                                    <n-icon size="48" color="#52c41a">
                                        <checkmark-circle-outline />
                                    </n-icon>
                                </div>
                                <h2 class="success-title">注册成功</h2>
                                <p class="success-description">您的账号已经创建完成</p>
                                <div class="success-actions">
                                    <n-button type="primary" size="large" @click="goToLogin">
                                        立即登录
                                    </n-button>
                                </div>
                            </div>
                        </transition>
                        <div v-if="!showSuccessView" class="register-link">
                            <n-button text @click="goToLogin">已有账号？立即登录</n-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import { NInput, NButton, NIcon } from 'naive-ui'
import { MailOutline, EyeOutline, EyeOffOutline, CheckmarkCircleOutline } from '@vicons/ionicons5'
import { useRouter } from 'vue-router'
import { doPost, doPut, doGet } from '@/utils/requests'
import message from '@/utils/messages'

const router = useRouter()

// 将所有 ref 声明集中到一起
const email = ref('')
const emailError = ref('')
const isSending = ref(false)
const countdown = ref(60)
const showVerificationInput = ref(false)
const verificationCode = ref(['', '', '', '', '', ''])
const showPasswordInput = ref(false)
const password = ref('')
const confirmPasswordValue = ref('')
const showPassword = ref(false)
const showOrganizationInput = ref(false)
const organizationName = ref('')
const organizationNameError = ref('')
const userName = ref('')
const userNameError = ref('')
const phoneNumber = ref('')
const phoneNumberError = ref('')
const showSuccessView = ref(false)
const uid = ref(null)

// 所有输入框的 ref
const emailInput = ref(null)
const passwordInput = ref(null)
const organizationInput = ref(null)
const codeInputs = ref([])

const isEmailValid = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email.value)
})

const validateEmail = () => {
    if (!email.value) {
        emailError.value = '邮箱不能为空'
    } else if (!isEmailValid.value) {
        emailError.value = '请输入有效的邮地址'
    } else {
        emailError.value = ''
    }
}

const resendCode = () => {
    if (!isSending.value) {
        sendVerificationCode()
    }
}

const sendVerificationCode = async () => {
    if (!isEmailValid.value) {
        return
    }

    isSending.value = true
    try {
        const encodedEmail = encodeURIComponent(email.value)
        const response = await doPost(`/auth-center/system/user/register/validation?email=${encodedEmail}`, {})
        if (response.code === 0) {
            message.success('验证码已发送，请查收邮件')
            startCountdown()
            showVerificationInput.value = true
            nextTick(() => {
                codeInputs.value[0]?.focus() // 验证码输入框自动获取焦点
            })
        } else {
            throw new Error(response.message || '发送失败')
        }
    } catch (error) {
        console.error('发送验证码失败:', error)
    } finally {
        isSending.value = false
    }
}

const startCountdown = () => {
    const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
            clearInterval(timer)
            countdown.value = 60
            isSending.value = false
        }
    }, 1000)
}

const handleEnter = () => {
    if (isEmailValid.value && !isSending.value) {
        sendVerificationCode()
    }
}

const onCodeInput = (index) => {
    if (verificationCode.value[index].length === 1) {
        if (index < 5) {
            codeInputs.value[index + 1]?.focus()
        } else {
            // 最后一个输入框填写完毕，检查是否所有输入框都已填写
            if (verificationCode.value.every(digit => digit !== '')) {
                register()
            }
        }
    }
}

const onKeyDown = (event, index) => {
    if (event.key === 'Backspace' && index > 0 && verificationCode.value[index] === '') {
        codeInputs.value[index - 1]?.focus()
    }
}

const onPaste = (event) => {
    event.preventDefault()
    const pastedText = event.clipboardData.getData('text')
    const chars = pastedText.slice(0, 6).split('')

    verificationCode.value = chars.concat(Array(6 - chars.length).fill(''))

    nextTick(() => {
        const nextEmptyIndex = verificationCode.value.findIndex(v => v === '')
        if (nextEmptyIndex !== -1) {
            codeInputs.value[nextEmptyIndex]?.focus()
        } else {
            codeInputs.value[5]?.focus()
            // 如有输入框都已填写，触发注册操作
            if (verificationCode.value.every(digit => digit !== '')) {
                register()
            }
        }
    })
}

const goToLogin = () => {
    // 如果是从成功界面点击，则带上邮箱参数
    if (showSuccessView.value) {
        router.push({ path: '/login', query: { email: email.value } })
    } else {
        router.push('/login')
    }
}

const isPasswordValid = computed(() => {
    const hasUpperCase = /[A-Z]/.test(password.value)
    const hasLowerCase = /[a-z]/.test(password.value)
    const hasNumber = /\d/.test(password.value)
    const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password.value)
    const validTypes = [hasUpperCase, hasLowerCase, hasNumber, hasSymbol].filter(Boolean).length
    return password.value.length >= 9 && validTypes >= 3 && password.value === confirmPasswordValue.value
})

// 添加手机号码格式验证的计算属性
const isPhoneNumberValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phoneNumber.value)
})

// 修改组织信息验证的计算属性
const isOrganizationInfoValid = computed(() => {
  return organizationName.value && 
         !organizationNameError.value && 
         userName.value && 
         !userNameError.value && 
         phoneNumber.value && 
         !phoneNumberError.value && 
         isPhoneNumberValid.value // 添加手机号码格式验证
})

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
}
const tid = ref(null)
const register = async () => {
    const code = verificationCode.value.join('')
    try {
        const response = await doPost(`/auth-center/system/user/register?email=${encodeURIComponent(email.value)}&verifyCode=${code}`, {})
        if (response.code === 0) {
            uid.value = response.data.uid
            tid.value = response.data.tid
            showVerificationInput.value = false
            showPasswordInput.value = true
            nextTick(() => {
                passwordInput.value?.focus()
            })
        } else {
            throw new Error(response.message || '验证失败')
        }
    } catch (error) {
        console.error('验证失败:', error)
    }
}

const confirmPassword = async () => {
    if (!isPasswordValid.value || !uid.value) return

    try {
        const response = await doPut(`/auth-center/system/user/${uid.value}`, {
            passwd: password.value,
            tenantId: tid.value
        })
        if (response.code === 0) {
            showPasswordInput.value = false
            showOrganizationInput.value = true // 显示组织名称输入界面
            nextTick(() => {
                organizationInput.value?.focus() // 组织名称输入框自动获取焦点
            })
        } else {
            throw new Error(response.message || '设置密码失败')
        }
    } catch (error) {
        console.error('设置密码失败:', error)
    }
}

const validateOrganizationName = async () => {
  if (!organizationName.value) {
    organizationNameError.value = '组织名称不能为空'
    return
  } else if (organizationName.value.length > 32) {
    organizationNameError.value = '组织名称不能超过32个字符'
    return
  }

  try {
    const response = await doGet(`/auth-center/system/department/exists?name=${organizationName.value}&parentId=0`)
    
    if (response.code === 0 && response.data.exists) {
      organizationNameError.value = '该组织名称已存在，请更换其他名称'
    } else {
      organizationNameError.value = ''
    }
  } catch (error) {
    console.error('检查组织名称失败:', error)
    organizationNameError.value = '该名称已被占用'
  }
}

const validateUserName = () => {
  if (!userName.value) {
    userNameError.value = '称呼不能为空'
  } else if (userName.value.length > 32) {
    userNameError.value = '称呼不能超过32个字符'
  } else {
    userNameError.value = ''
  }
}

// 修改手机号码验证函数，在输入时实时验证
const validatePhoneNumber = () => {
  if (!phoneNumber.value) {
    phoneNumberError.value = '手机号码不能为空'
    return false
  } else if (!isPhoneNumberValid.value) {
    phoneNumberError.value = '请输入有效的11位手机号码'
    return false
  } else {
    phoneNumberError.value = ''
    return true
  }
}

const completeRegistration = async () => {
  if (!isOrganizationInfoValid.value) return

  try {
    // 先创建构
    const departmentResponse = await doPost('/auth-center/system/department', {
      name: organizationName.value,
      parentId: 0,
      tenantId: tid.value
    })
    if (departmentResponse.code !== 0) {
      throw new Error(departmentResponse.message || '创建机构失败')
    }
    
    const departmentId = departmentResponse.data.id

    // 然后更新用户信息
    const userResponse = await doPut(`/auth-center/system/user/${uid.value}`, {
      nickname: userName.value,
      workMobile: phoneNumber.value,
      disabled: false,
      departmentId: departmentId,
      tenantId: tid.value
    })
    if (userResponse.code !== 0) {
      throw new Error(userResponse.message || '更新用户信息失败')
    }

    // 显示成功界面
    showOrganizationInput.value = false
    showSuccessView.value = true

    // 移除自动跳转的代码
    // setTimeout(() => {
    //   router.push({ path: '/login', query: { email: email.value } })
    // }, 3000)
  } catch (error) {
    console.error('完成注册失败:', error)
    message.error(error.message || '注册失败，请稍后重试')
  }
}


const filterPassword = (value) => {
    password.value = value.replace(/[^\x00-\xff]/g, '')
}

const filterConfirmPassword = (value) => {
    confirmPasswordValue.value = value.replace(/[^\x00-\xff]/g, '')
}

// 在组件挂载时让邮箱输入框获取焦点
onMounted(() => {
    // 使用 setTimeout 确保在 DOM 完全渲染后设置焦点
    setTimeout(() => {
        emailInput.value?.input?.focus()
    }, 100)
})

// 添加手机号码回车事件处理函数
const handlePhoneNumberEnter = () => {
  if (isOrganizationInfoValid.value) {
    completeRegistration()
  }
}
</script>

<style scoped>
/* 复制 LoginPage.vue 中的所有样式到这里 */
/* 注意：可能需要调整一些类名和样式以适应注册页面的布局 */

.login-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    min-height: 100vh;
    height: 100vh;
    background-color: #f0f4f9;
    overflow: hidden;
    box-sizing: border-box;
    padding: 20px 0;
    overflow-x: hidden;
}

.login-box {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
    border-radius: 8px;
    padding: 40px;
    width: 90%;
    max-width: 100%;
    height: auto;
    max-height: calc(100vh - 120px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: auto;
    overflow-y: auto;
    box-sizing: border-box;
    overflow-x: hidden;
}

/* 其他样式保持不变，直接从 LoginPage.vue 复制过来 */
/* ... */

.input-wrapper, .password-input-wrapper, .organization-input-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 20px; /* 减少底部边距 */
}

.error-message {
    position: absolute;
    top: -8px; /* 将错误消息移到输入框上方 */
    left: 12px; /* 左侧偏移 */
    color: #ff4d4f;
    font-size: 16px; /* 稍微减小字体大小 */
    line-height: 1;
    padding: 0 4px;
    background-color: white; /* 添加白色背景，使其看起来像切割了边框 */
    z-index: 1;
}

:deep(.n-input) {
    max-width: 100%;
}

:deep(.n-input__input-el) {
    height: 56px;
    line-height: 56px;
    font-size: 18px;
}

:deep(.n-input__prefix) {
    height: 56px;
    display: flex;
    align-items: center;
}

:deep(.n-input.n-input--error) {
    border-color: #ff4d4f; /* 确保错误状态下输入框边框为红色 */
}

:deep(.n-input.n-input--error:hover),
:deep(.n-input.n-input--error:focus) {
    border-color: #ff7875;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.button-wrapper {
    position: relative;
    width: 100%;
    margin-top: 20px;
    /* 调整按钮与输入框的间距 */
}

.send-code-button {
    position: absolute;
    right: 0;
    top: 0; /* 调整按钮位置，使其与输入框顶部对齐 */
    height: 56px;
    font-size: 16px;
    padding: 0 20px;
}

/* 其他样式保持不变 */

.login-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    /* 增加这一行 */
}

.login-header h2 {
    font-size: 28px;
    color: #333;
    margin: 0;
    /* 添加这一行以移除默认下边距 */
}

/* 他样式保持不变 */

.verification-step {
    margin-top: 20px;
}

.verification-prompt {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.resend-wrapper {
    text-align: center;
    margin-top: 15px;
}

/* 其他现有的样式保持不变 */

.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}

.verification-code-input {
    margin-top: 20px;
}

.code-input-wrapper {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    /* 添加间距 */
}

.code-input-wrapper input {
    width: 50px;
    /* 调整为正方形 */
    height: 50px;
    /* 调整为正方形 */
    font-size: 18px;
    text-align: center;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    outline: none;
    padding: 0;
    /* 移除内边距 */
}

.code-input-wrapper input:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.password-step {
    margin-top: 20px;
    position: relative;
    /* 添加这一行 */
}

.password-prompt {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.password-input-wrapper {
    margin-bottom: 15px;
}

.password-toggle {
    cursor: pointer;
}

.password-button-wrapper {
    position: absolute;
    /* 修改为绝对定位 */
    right: 0;
    /* 靠右齐 */
    top: 100%;
    /* 放置在密码输入框下方 */
    width: auto;
    /* 允许按钮宽度适应 */
    margin-top: 10px;
    /* 与输入框保持一定距离 */
}

.password-button-wrapper .n-button {
    height: 56px;
    /* 保持与其他按钮一致的高度 */
    font-size: 16px;
    /* 保持与其他按钮一致的字体大小 */
    padding: 0 20px;
    /* 保持与其他按钮一致的内距 */
}

.password-hint {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

.organization-step {
    margin-top: 20px;
    position: relative;
}

.organization-input-wrapper {
    margin-bottom: 15px;
    position: relative;
}

.organization-button-wrapper {
    position: absolute;
    right: 0;
    top: 100%;
    width: auto;
    margin-top: 10px;
}

.organization-button-wrapper .n-button {
    height: 56px;
    font-size: 16px;
    padding: 0 20px;
}

.input-wrapper {
    margin-bottom: 15px;
    position: relative;
}

.success-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;
  animation: zoomIn 0.3s ease-in-out;
}

.success-title {
  font-size: 24px;
  color: #52c41a;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-in-out 0.1s both;
}

.success-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  animation: fadeIn 0.3s ease-in-out 0.2s both;
}

.success-actions {
  animation: fadeIn 0.3s ease-in-out 0.3s both;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


.success-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  text-align: center;
}

.success-icon {
  margin-bottom: 24px;
  animation: zoomIn 0.3s ease-in-out;
}

.success-title {
  font-size: 24px;
  color: #52c41a;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-in-out 0.1s both;
}

.success-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  animation: fadeIn 0.3s ease-in-out 0.2s both;
}

.success-actions {
  animation: fadeIn 0.3s ease-in-out 0.3s both;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>













