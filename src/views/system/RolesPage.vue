<template>
  <div class="role-management">
    
    <!-- 工具栏 -->
    <n-space class="toolbar">
      <n-button type="primary" @click="refreshRoles" round>
        <template #icon>
          <n-icon><RefreshOutline /></n-icon>
        </template>
        刷新数据
      </n-button>
      <n-button type="info" @click="showAddDialog" round> <!-- 修改按钮颜色为蓝色 -->
        <template #icon>
          <n-icon><AddOutline /></n-icon>
        </template>
        新增角色
      </n-button>
      <n-button type="error" @click="batchDelete" :disabled="!selectedRoles.length" round>
        <template #icon>
          <n-icon><TrashOutline /></n-icon>
        </template>
        批量删除
      </n-button>
    </n-space>
    
    <!-- 角色列表 -->
    <n-data-table
      :columns="columns"
      :data="roles"
      :row-key="row => row.id"
      @update:checked-row-keys="handleSelectionChange"
    />

    <!-- 编辑角色对话框 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      :style="{ width: dialogWidth }"
      preset="card"
      :mask-closable="false"
      :auto-focus="false"
      :transformOrigin="'center'"
      :class="{ 'fullscreen-modal': isFullscreen }"
    >
      <template #header-extra>
        <n-button v-if="showMaximizeButton" quaternary circle @click="toggleFullscreen">
          <template #icon>
            <n-icon>
              <component :is="isFullscreen ? ContractOutline : ExpandOutline" />
            </n-icon>
          </template>
        </n-button>
      </template>
      
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        :style="{
          maxWidth: '640px'
        }"
      >
        <n-form-item label="角色名称" path="roleName">
          <n-input v-model:value="form.roleName" placeholder="请输入角色名称" />
        </n-form-item>
        <n-form-item label="角色代码" path="roleCode">
          <n-input v-model:value="form.roleCode" placeholder="请输入角色代码" />
        </n-form-item>
        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="菜单权限" path="menus">
                <n-tree
                ref="menuTreeRef"
                :data="menuTree"
                checkable
                cascade
                :key-field="'id'"
                :label-field="'menuLabel'"
                :children-field="'subMenus'"
                :checked-keys="form.menus"
                @update:checked-keys="handleMenuCheck"
                :default-expand-all="true"
                :selectable="false"
                :show-irrelevant-nodes="false"
                />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="数据权限" path="dataScope">
              <n-tooltip placement="top">
                <template #trigger>
                  <n-icon><HelpCircleOutline /></n-icon>
                </template>
                不选择数据权限时仅能查询自己名下的数据
              </n-tooltip>
              <n-tree
                ref="dataScopeTreeRef"
                :data="departmentTree"
                checkable
                cascade
                :key-field="'id'"
                :label-field="'name'"
                :children-field="'children'"
                @update:checked-keys="handleDataScopeCheck"
                :default-expand-all="true"
                :selectable="false"
                :show-irrelevant-nodes="false"
                />      
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, h, nextTick, onUnmounted, watch } from 'vue';
import { useDialog } from 'naive-ui';
import { RefreshOutline, AddOutline, TrashOutline, PencilOutline, HelpCircleOutline, ExpandOutline, ContractOutline } from '@vicons/ionicons5';
import { getRoles, getMenus, saveRole, updateRole, deleteRole, getRoleDetail } from '@/api/roles';
import { getDepartments } from '@/api/users';
import messages from '@/utils/messages';
import { NButton, NSpace, NIcon, NModal } from 'naive-ui';

const roles = ref([]);
const menuTree = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const menuTreeRef = ref(null);
const selectedRoles = ref([]);
const departmentTree = ref([]);
const dataScopeTreeRef = ref(null);

const form = ref({
  roleName: '',
  roleCode: '',
  menus: [],
  dataScope: ''
});

const rules = {
  roleName: [
    { 
      required: true, 
      message: '请输入角色名称（2-20个汉字）', 
      trigger: ['blur', 'input'],
      validator: (rule, value) => {
        if (!value) {
          return new Error('角色名称不能为空');
        } else if (value.length < 2 || value.length > 20) {
          return new Error('角色名称长度应在2-20个字符之间');
        }
        return true;
      }
    }
  ],
  roleCode: [
    { 
      required: true, 
      message: '请输入角色代码（2-20个大写字母或下划线）', 
      trigger: ['blur', 'input'],
      validator: (rule, value) => {
        if (!value) {
          return new Error('角色代码不能为空');
        } else if (!/^[A-Z_]{2,20}$/.test(value)) {
          return new Error('角色代码应为2-20个大写字母');
        }
        return true;
      }
    }
  ],
  menus: [
    { 
      type: 'array', 
      required: true, 
      message: '请至少选择一个菜单权限', 
      trigger: 'change',
      validator: (rule, value) => {
        if (!value || value.length === 0) {
          return new Error('请至少选择一个菜单权限');
        }
        return true;
      }
    }
  ],
  dataScope: [
    {
      trigger: 'change',
      validator: (rule, value) => {
        return true;
      }
    }
  ]
};

const dialog = useDialog();

const columns = [
  { type: 'selection',align: 'center'},
  { title: '角色ID', key: 'id' ,align: 'center'},
  { title: '角色代码', key: 'roleCode',align: 'center' },
  { title: '角色名称', key: 'roleName' ,align: 'center'},
  {
    title: '操作',
    key: 'actions',
    width: 120, // 减小列宽
    align: 'center',

    render: (row) => {
      return h(NSpace, { justify: 'center', size: 'small' }, () => [
        h(NIcon, {
          component: PencilOutline,
          size: 20,
          color: '#2080f0',
          style: {
            cursor: 'pointer'
          },
          onClick: () => handleEdit(row)
        }),
        h(NIcon, {
          component: TrashOutline,
          size: 20,
          color: '#d03050',
          style: {
            cursor: 'pointer'
          },
          onClick: () => handleDelete(row)
        })
      ]);
    }
  }
];

// 获取角色列表
const fetchRoles = async () => {
  try {
    const response = await getRoles();
    roles.value = response.data.map(role => ({
      ...role,
      menus: role.menus || []
    }));
  } catch (error) {
    messages.error('获角色列失败');
  }
};

// 获取菜单树
const fetchMenuTree = async () => {
  try {
    const response = await getMenus();
    menuTree.value = buildMenuTree(response.data);
  } catch (error) {
    messages.error('获取菜单列表失败');
  }
};

// 构建菜单树
const buildMenuTree = (menus) => {
  const menuMap = {};
  menus.forEach(menu => menuMap[menu.id] = { ...menu, subMenus: [] });
  
  const rootMenus = [];
  menus.forEach(menu => {
    if (menu.parentId === 1) {
      rootMenus.push(menuMap[menu.id]);
    } else {
      menuMap[menu.parentId].subMenus.push(menuMap[menu.id]);
    }
  });
  
  // 移除空的 subMenus 数组
  const removeEmptySubMenus = (menu) => {
    if (menu.subMenus.length === 0) {
      delete menu.subMenus;
    } else {
      menu.subMenus.forEach(removeEmptySubMenus);
    }
  };
  rootMenus.forEach(removeEmptySubMenus);
  
  return rootMenus;
};

// 获取部门树
const fetchDepartmentTree = async () => {
  try {
    const response = await getDepartments();
    departmentTree.value = buildDepartmentTree(response.data);
  } catch (error) {
    messages.error('获取组织机构树失败');
  }
};

// 构建部门树
const buildDepartmentTree = (departments) => {
  const departmentMap = {};
  departments.forEach(dept => departmentMap[dept.id] = { ...dept, children: [] });
  
  const rootDepartments = [];
  departments.forEach(dept => {
    if (!dept.parentId) {
      rootDepartments.push(departmentMap[dept.id]);
    } else {
      departmentMap[dept.parentId].children.push(departmentMap[dept.id]);
    }
  });
  
  // 移除空的 children 数组
  const removeEmptyChildren = (dept) => {
    if (dept.children.length === 0) {
      delete dept.children;
    } else {
      dept.children.forEach(removeEmptyChildren);
    }
  };
  rootDepartments.forEach(removeEmptyChildren);
  
  return rootDepartments;
};

// 刷新角色列表
const refreshRoles = () => {
  fetchRoles();
};

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false;
  form.value = { roleName: '', roleCode: '', menus: [], dataScope: '' };
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  isEdit.value = true;
  dialogVisible.value = true;
  
  try {
    const response = await getRoleDetail(row.id);
    const roleDetail = response.data;
    
    form.value = {
      id: roleDetail.id,
      roleName: roleDetail.roleName,
      roleCode: roleDetail.roleCode,
      menus: roleDetail.menus || [],
      dataScope: roleDetail.dataScope || ''
    };
  } catch (error) {
    messages.error('获取角色详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: '警告',
    content: '确定要删除这个角色吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await deleteRole(row.id);
        messages.success('删除成功');
        fetchRoles();
      } catch (error) {
        messages.error('删除失败');
      }
    }
  });
};

// 处理批量删除
const batchDelete = () => {
  if (selectedRoles.value.length === 0) {
    messages.warning('请选择要删除的角色');
    return;
  }
  dialog.warning({
    title: '警告',
    content: `确定要删除这 ${selectedRoles.value.length} 个角色吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await Promise.all(selectedRoles.value.map(role => deleteRole(role.id)));
        messages.success('批量删除成功');
        fetchRoles();
      } catch (error) {
        messages.error('批量删除失败');
      }
    }
  });
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedRoles.value = selection;
};

// 处理菜单选择
const handleMenuCheck = (checkedKeys) => {
  form.value.menus = checkedKeys;
  // 使用 Promise 风格的验证，并处理可能的验证错误
  if (formRef.value) {
    formRef.value.validate()
      .then(() => {
        // 验证成功
      })
      .catch((errors) => {
        // 验证失败，处理错误
        console.error('表单验证失败:', errors);
      });
  }
};

// 处理数据权限选择
const handleDataScopeCheck = (checkedKeys, info = {}) => {
  if (!Array.isArray(checkedKeys)) {
    console.error('checkedKeys is not an array:', checkedKeys);
    return;
  }
  
  if (info.checkedNodes && Array.isArray(info.checkedNodes)) {
    const leafNodes = info.checkedNodes.filter(node => !node.children || node.children.length === 0);
    form.value.dataScope = leafNodes.map(node => node.key).join(',');
  } else {
    // 如果 checkedNodes 不可用，我们直接使用 checkedKeys
    form.value.dataScope = checkedKeys.join(',');
  }
};

// 保存或更新角色
const handleSave = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const roleData = {
      ...form.value,
      menus: form.value.menus
    };
    if (isEdit.value) {
      await updateRole(roleData);
      messages.success('角色更新成功');
    } else {
      await saveRole(roleData);
      messages.success('角色创建成功');
    }
    dialogVisible.value = false;
    fetchRoles();
  } catch (errors) {
    console.error('表单验证失败:', errors);
    messages.error('请检查表单填写是否正确');
  }
};

const isFullscreen = ref(false);
const showMaximizeButton = ref(false);

// 计算话框宽度
const dialogWidth = computed(() => {
  if (isFullscreen.value) {
    return '100%';
  }
  return window.innerWidth < 1920 ? '75%' : '30%';
})

// 监听窗口大小变化
const handleResize = () => {
  showMaximizeButton.value = window.innerWidth < 1920;
  if (window.innerWidth < 1920 && dialogVisible.value) {
    isFullscreen.value = true;
  }
}

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
}

// 监听对话框可见性变化
watch(dialogVisible, (newValue) => {
  if (newValue && window.innerWidth < 1920) {
    isFullscreen.value = true;
  } else {
    isFullscreen.value = false;
  }
});

onMounted(() => {
  fetchRoles();
  fetchMenuTree();
  fetchDepartmentTree();
  window.addEventListener('resize', handleResize);
  handleResize(); // 初始化时调用一次
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

/* 表格内容居中展示 */
:deep(.n-data-table-td) {
  text-align: center;
}

/* 确保所有单元格内容居中 */
:deep(.n-data-table-td .n-data-table-td__content) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 调整操作栏图标的间距 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 8px !important;
}

/* 如果需要特定列左对齐，可以添加如下样式 */
:deep(.n-data-table-td[data-col-key="roleName"] .n-data-table-td__content),
:deep(.n-data-table-td[data-col-key="roleCode"] .n-data-table-td__content) {
  justify-content: flex-start;
}

/* 其他样式可以根据需要进行调整 */

.fullscreen-modal {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.fullscreen-modal .n-card-header) {
  padding: 14px 20px !important;
}

:deep(.fullscreen-modal .n-card__content) {
  height: calc(100vh - 120px) !important;
  overflow-y: auto !important;
}

:deep(.fullscreen-modal .n-card__footer) {
  padding: 14px 20px !important;
}

/* 添加以下样式来调整最大化按钮的位置 */
:deep(.n-card-header .n-card-header__extra) {
  display: flex;
  align-items: center;
}

:deep(.n-card-header .n-card-header__extra .n-button) {
  margin-right: 8px;
}

/* 调整关闭按钮的样式，使其与最大化按钮对齐 */
:deep(.n-modal .n-card-header .n-base-close) {
  top: 14px;
  right: 20px;
}
</style>