<template>
  <div class="admin-page">
    <!-- 二次验证界面 -->
    <div v-if="!isVerified" class="verification-container">
      <n-card class="verification-card" title="账号管理 - 二次验证">
        <template #header-extra>
          <n-icon size="24" color="#2080f0">
            <shield-checkmark-outline />
          </n-icon>
        </template>

        <div class="verification-content">
          <n-alert type="info" class="verification-alert">
            <template #icon>
              <n-icon><information-circle-outline /></n-icon>
            </template>
            请输入二级密码以继续访问管理功能
          </n-alert>

          <n-form
            ref="verificationFormRef"
            :model="verificationForm"
            :rules="verificationRules"
          >
            <n-form-item path="password" label="二级密码">
              <n-input
                v-model:value="verificationForm.password"
                type="password"
                placeholder="请输入二级密码"
                show-password-on="click"
                @keydown.enter="handleVerification"
                :loading="verificationLoading"
              >
                <template #prefix>
                  <n-icon><lock-closed-outline /></n-icon>
                </template>
              </n-input>
            </n-form-item>
          </n-form>

          <div class="verification-actions">
            <n-button
              type="primary"
              @click="handleVerification"
              :loading="verificationLoading"
              block
              round
            >
              进入
            </n-button>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 账号管理主界面 -->
    <div v-else class="account-management">
      <n-card title="账号管理" style="height: 95vh; width: 100%">
        <template #header-extra>
          <n-space align="center">
            <n-text type="info"> 你好，{{ userNickname }} </n-text>
            <n-button @click="handleLogout" type="warning" round>
              <template #icon>
                <n-icon><log-out-outline /></n-icon>
              </template>
              退出管理
            </n-button>
          </n-space>
        </template>

        <div class="management-content">
          <n-alert type="success" class="welcome-alert">
            <template #icon>
              <n-icon><checkmark-circle-outline /></n-icon>
            </template>
            欢迎进入账号管理系统 - 管理员模式
          </n-alert>

          <!-- 用户管理功能 -->
          <div class="user-management-content">
            <ContactsPage :is-admin="true" />
          </div>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import {
  NCard,
  NAlert,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NSpace,
  NEmpty,
  NText,
  useMessage,
} from "naive-ui";
import ContactsPage from "@/views/contacts/ContactsPage.vue";
import {
  ShieldCheckmarkOutline,
  InformationCircleOutline,
  LockClosedOutline,
  LogOutOutline,
  CheckmarkCircleOutline,
  ConstructOutline,
} from "@vicons/ionicons5";

// 消息提示
const message = useMessage();

// 验证状态
const isVerified = ref(false);
const verificationLoading = ref(false);

// SessionStorage 键名
const ADMIN_VERIFIED_KEY = "admin_verified_session";

// 用户信息
const userNickname = ref("");

// 获取用户昵称
const getUserNickname = () => {
  try {
    const userInfo = localStorage.getItem("user");
    if (userInfo) {
      const user = JSON.parse(userInfo);
      userNickname.value = user.nickname || "用户";
    } else {
      userNickname.value = "用户";
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    userNickname.value = "用户";
  }
};

// 检查会话验证状态
const checkSessionVerification = () => {
  try {
    const verified = sessionStorage.getItem(ADMIN_VERIFIED_KEY);
    if (verified === "true") {
      isVerified.value = true;
      return true;
    }
  } catch (error) {
    console.error("检查会话验证状态失败:", error);
  }
  return false;
};

// 保存会话验证状态
const saveSessionVerification = () => {
  try {
    sessionStorage.setItem(ADMIN_VERIFIED_KEY, "true");
  } catch (error) {
    console.error("保存会话验证状态失败:", error);
  }
};

// 清除会话验证状态
const clearSessionVerification = () => {
  try {
    sessionStorage.removeItem(ADMIN_VERIFIED_KEY);
  } catch (error) {
    console.error("清除会话验证状态失败:", error);
  }
};

const whatIsThis = "qianjue2025";

// 验证表单
const verificationFormRef = ref(null);
const verificationForm = reactive({
  password: "",
});

// 验证规则
const verificationRules = {
  password: [
    {
      required: true,
      message: "请输入二级密码",
      trigger: ["input", "blur"],
    },
  ],
};

// 处理二次验证
const handleVerification = async () => {
  if (!verificationFormRef.value) return;

  try {
    await verificationFormRef.value.validate();
    verificationLoading.value = true;

    // 模拟验证过程
    await new Promise((resolve) => setTimeout(resolve, 1000));

    if (verificationForm.password === whatIsThis) {
      isVerified.value = true;
      saveSessionVerification(); // 保存会话验证状态
      message.success("验证成功，欢迎进入账号管理系统");
      verificationForm.password = ""; // 清空密码
    } else {
      message.error("二级密码错误，请重新输入");
      verificationForm.password = "";
    }
  } catch (error) {
    console.error("验证失败:", error);
    message.error("验证过程中出现错误");
  } finally {
    verificationLoading.value = false;
  }
};

// 退出管理
const handleLogout = () => {
  isVerified.value = false;
  verificationForm.password = "";
  clearSessionVerification(); // 清除会话验证状态
  message.info("已退出账号管理系统");
};

// 组件挂载时获取用户信息
onMounted(() => {
  getUserNickname();
  // 检查会话验证状态
  if (checkSessionVerification()) {
    message.info("检测到已验证会话，自动进入管理系统");
  }
  // 开发阶段默认填充密码
  if (import.meta.env.DEV) {
    verificationForm.password = whatIsThis;
  }
});
</script>

<style>
/* 全局样式 - 精确控制滚动 */
html {
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
}

body {
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden; /* 只禁用body滚动 */
}

#app {
  height: 100vh !important;
  overflow: hidden; /* 只禁用app滚动 */
}
</style>

<style scoped>
.admin-page {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.verification-container {
  width: 100%;
  max-width: 450px;
}

.verification-card {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.verification-content {
  padding: 20px 0;
}

.verification-alert {
  margin-bottom: 24px;
}

.verification-actions {
  margin-top: 24px;
}

.account-management {
  width: 95%;
  height: 95vh;
  min-height: 95vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.management-content {
  padding: 20px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100%;
}

.welcome-alert {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.user-management-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: calc(95vh - 164px);
  height: 100%;
  overflow: hidden;
}

/* 确保ContactsPage组件填充整个容器 */
.user-management-content :deep(.n-layout) {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-page {
    padding: 10px;
  }

  .verification-container {
    max-width: 100%;
  }
}
</style>