<script setup>
import { NLayout, NLayoutSider, NMenu, NLayoutHeader, NLayoutContent, NImage, NDropdown, NSpace, NIcon, NBreadcrumb, NBreadcrumbItem } from 'naive-ui'
import { PersonCircleOutline } from '@vicons/ionicons5'
import { useRouter, useRoute } from 'vue-router'
import { useMainStore } from '@/stores/mainStore'
import logoImage from '@/assets/images/qj-logo-Dgc-aGJV.png'  // 导入图片
import { onMounted, computed, ref, watch, markRaw } from 'vue'
import UsersPage from './system/UsersPage.vue'

// 使用 markRaw 标记 UsersPage 组件
const UsersPageComponent = markRaw(UsersPage)

const modules = import.meta.glob('@/views/**/*.vue', { eager: true })

const router = useRouter()
const route = useRoute()
const mainStore = useMainStore()

const activeKey = ref(null)
const breadcrumbs = ref([])
const currentComponent = ref(null)

onMounted(async () => {
  if (!mainStore.isUserLoggedIn) {
    const isLoggedIn = await mainStore.checkLoginStatus()
    if (!isLoggedIn) {
      router.push('/login')
      return
    }
  }
  
  // 每次都重新获取菜单数据
 // await mainStore.fetchMenus()
  
  console.log('Loaded menus:', mainStore.getMenus)
  
  // 设置默认路由为 /tasks，但只在工作区中加载
  const defaultPath = '/tasks'
  activeKey.value = defaultPath
  updateBreadcrumbs(defaultPath)
  await loadComponent(defaultPath)
})

const handleMenuClick = async (key) => {
  console.log('Menu clicked:', key)
  activeKey.value = key
  updateBreadcrumbs(key)
  await loadComponent(key)
}

const updateBreadcrumbs = (path) => {
  const matchedMenu = findMenuByPath(mainStore.getMenus, path)
  if (matchedMenu) {
    breadcrumbs.value = [{ label: '首页', key: '/' }, ...matchedMenu.breadcrumb]
  } else {
    breadcrumbs.value = [{ label: '首页', key: '/' }]
  }
}

const findMenuByPath = (menus, path) => {
  console.log('Searching for path:', path, 'in menus:', menus)
  for (const menu of menus) {
    console.log('Checking menu:', menu)
    if (menu.key === path) {
      console.log('Menu found:', menu)
      return { ...menu, breadcrumb: [{ label: menu.label, key: menu.key }] }
    }
    if (menu.children) {
      const found = findMenuByPath(menu.children, path)
      if (found) {
        console.log('Menu found in children:', found)
        return { ...found, breadcrumb: [{ label: menu.label, key: menu.key }, ...found.breadcrumb] }
      }
    }
  }
  console.log('Menu not found for path:', path)
  return null
}

const loadComponent = async (path) => {
  console.log('Loading component for path:', path)
  console.log('Current menus:', mainStore.getMenus)
  const matchedMenu = findMenuByPath(mainStore.getMenus, path)
  console.log('Matched menu:', matchedMenu)
  if (matchedMenu && matchedMenu.viewPath) {
    try {
      if (matchedMenu.viewPath === 'system/UsersPage') {
        currentComponent.value = UsersPageComponent  // 使用标记过的组件
      } else {
        // 原有的动态导入逻辑
        const modulePath = `@/views/${matchedMenu.viewPath}.vue`
        console.log('Attempting to import:', modulePath)
        console.log('Available modules:', Object.keys(modules))
        
        // 尝试不同的路径格式
        const possiblePaths = [
          modulePath,
          modulePath.replace('@/', '/src/'),
          `/src/views/${matchedMenu.viewPath}.vue`,
          `./src/views/${matchedMenu.viewPath}.vue`
        ]
        
        let module
        for (const path of possiblePaths) {
          if (modules[path]) {
            module = modules[path]
            break
          }
        }
        
        if (module) {
          console.log('Module found:', module)
          currentComponent.value = markRaw(module.default)
          console.log('Current component set:', currentComponent.value)
        } else {
          throw new Error(`Module not found: ${modulePath}`)
        }
      }
    } catch (error) {
      console.error('Failed to load component:', error)
      currentComponent.value = null
    }
  } else {
    console.log('No matching menu or viewPath found')
    currentComponent.value = null
  }
}

watch(() => route.path, async (newPath) => {
  updateBreadcrumbs(newPath)
  await loadComponent(newPath)
})

const userMenuOptions = [
  {
    label: '重置密码',
    key: 'reset-password'
  },
  {
    label: '退出系统',
    key: 'logout'
  }
]

const handleUserMenuSelect = (key) => {
  if (key === 'reset-password') {
    console.log('重置密码')
  } else if (key === 'logout') {
    mainStore.logout()
    router.push('/login')
  }
}

const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour >= 21 || hour < 6) return '您辛苦'
  if (hour >= 18) return '晚上好'
  if (hour >= 13) return '下午好'
  if (hour >= 11) return '中午好'
  if (hour >= 9) return '上午好'
  return '早上好'
})
</script>

<template>
  <n-layout position="absolute">
    <n-layout-header class="header">
      <div class="logo-container">
        <n-image
          :src="logoImage"
          width="380"
          preview-disabled
        />
      </div>
      <div class="user-info">
        <n-space align="center">
          <span>{{ greeting }}，</span>
          <span>{{ mainStore.user?.nickname || '用户' }}</span>
          
          <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
            <n-icon size="30" :component="PersonCircleOutline" />
          </n-dropdown>
        </n-space>
      </div>
    </n-layout-header>
    <n-layout has-sider position="absolute" style="top: 64px;">
      <n-layout-sider bordered content-style="padding: 14px;">
        <n-menu 
          :options="mainStore.getMenus" 
          @update:value="handleMenuClick"
          :value="activeKey"
          :indent="24"
          :collapsed-icon-size="22"
          :collapsed-width="64"
          :inverted="true"
        />
      </n-layout-sider>
      <n-layout-content>
        <div class="content-wrapper">
          <n-breadcrumb>
            <n-breadcrumb-item v-for="item in breadcrumbs" :key="item.key">
              {{ item.label }}
            </n-breadcrumb-item>
          </n-breadcrumb>
          <div class="router-view-container">
            <component :is="currentComponent" v-if="currentComponent" />
            <div v-else>未找到对应的组件: {{ activeKey }}</div>
          </div>
        </div>
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.logo-container {
  margin-top: 12px;
}

.user-info {
  display: flex;
  align-items: center;
}

.n-layout-sider {
  background-color: #08223a;
}

.n-layout-content {
  background-color: #f0f2f5;
}

:deep(.n-menu-item) {
  color: rgba(255, 255, 255, 0.65);
}

:deep(.n-menu-item:hover) {
  color: #fff;
  background-color: #1890ff;
}

:deep(.n-menu-item-content--selected) {
  color: #fff;
  background-color: #1890ff;
}

/* 移除父菜单的选中效果 */
:deep(.n-menu-item-content--child-active) {
  color: rgba(255, 255, 255, 0.65);
  background-color: transparent;
}

/* 移除默认的绿背景 */
:deep(.n-menu-item-content::before) {
  background-color: transparent !important;
}

:deep(.n-menu-item-content--selected::before),
:deep(.n-menu-item-content--child-active::before) {
  background-color: transparent !important;
}

.content-wrapper {
  padding: 14px;
}

.router-view-container {
  margin-top: 14px;
}

:deep(.n-breadcrumb) {
  margin-bottom: 14px;
}
</style>
