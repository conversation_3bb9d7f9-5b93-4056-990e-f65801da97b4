<script setup>
import { NLayout, NLayoutHeader, NLayoutContent, NImage, NDropdown, NSpace, NIcon, NPopover } from 'naive-ui'
import { PersonCircleOutline } from '@vicons/ionicons5'
import { ApiApp } from '@vicons/tabler'
import { AppSwitcher, UserMultiple,Money as AttachMoneyOutlined,Settings as SettingsOutline,MediaLibraryFilled } from '@vicons/carbon'
import { RobotOutlined } from '@vicons/antd'
import { useRouter } from 'vue-router'
import { useMainStore } from '@/stores/mainStore'
import logoImage from '@/assets/images/qj-logo-Dgc-aGJV.png'
import { onMounted, computed, ref, markRaw } from 'vue'
import AppCenter from '@/views/app/AppCenter.vue'
import ContactsPage from '@/views/contacts/ContactsPage.vue'
import IsaacPage from '@/views/isaac/IsaacPage.vue'
import DeepSeekLive from '@/views/isaac/DeepSeekLive.vue'
import RobotController from '@/views/isaac/RobotController.vue'

const router = useRouter()
const mainStore = useMainStore()

// 添加当前选中的菜单状态
const currentMenu = ref('app-center')  // 默认选中通讯录

// 登录状态检查
onMounted(async () => {
  if (!mainStore.isUserLoggedIn) {
    const isLoggedIn = await mainStore.checkLoginStatus()
    if (!isLoggedIn) {
      router.push('/login')
      return
    }
  }
})

// 用户菜单选项
const userMenuOptions = [
  {
    label: '重置密码',
    key: 'reset-password'
  },
  {
    label: '退出系统',
    key: 'logout'
  }
]

// 用户菜单处理
const handleUserMenuSelect = (key) => {
  if (key === 'reset-password') {
    console.log('重置密码')
  } else if (key === 'logout') {
    mainStore.logout()
    router.push('/login')
  }
}

// 问候语计算
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour >= 21 || hour < 6) return '您辛苦'
  if (hour >= 18) return '晚上好'
  if (hour >= 13) return '下午好'
  if (hour >= 11) return '中午好'
  if (hour >= 9) return '上午好'
  return '早上好'
})

// 应用切换菜单选项
const appSwitchOptions = [
  {
    label: '应用中心',
    icon: markRaw(ApiApp),
    key: 'app-center',
    color: '#1677ff'
  },
  {
    label: '通讯录',
    icon: markRaw(UserMultiple),
    key: 'contacts',
    color: '#52c41a'
  },
  {
    label: 'Isaac',
    icon: markRaw(AttachMoneyOutlined),
    key: 'isaac',
    color: '#722ed1'
  },
  {
    label: 'ChatLive',
    icon: markRaw(MediaLibraryFilled),
    key: 'deepseek',
    color: '#fa168c'
  },
    {
    label: 'RobotControl',
    icon: markRaw(RobotOutlined),
    key: 'robots',
    color: '#fa168c'
  },
  {
    label: '基础设置',
    icon: markRaw(SettingsOutline),
    key: 'settings',
    color: '#fa8c16'
  }
]

// 当前显示的组件
const currentComponent = ref('app-center')

// 修改应用切换菜单控制
const showAppMenu = ref(false)
const handleAppSwitchSelect = (key) => {
  showAppMenu.value = false
  currentMenu.value = key
  currentComponent.value = key
  console.log('切换到:', key)
}

// 在组件挂载时设置默认显示的组件
onMounted(() => {
  currentComponent.value = 'app-center'
  currentMenu.value = 'app-center'
})

// 组件映射表
const componentMap = {
  'app-center': markRaw(AppCenter),
  'contacts': markRaw(ContactsPage),
  'isaac': markRaw(IsaacPage),
  'deepseek': markRaw(DeepSeekLive),
  'robots': markRaw(RobotController),
  'settings': null // 待实现
}
</script>

<template>
  <n-layout position="absolute">
    <n-layout-header class="header">
      <div class="logo-container">
        <n-image
          :src="logoImage"
          width="380"
          preview-disabled
        />
      </div>
      <div class="user-info">
        <n-space align="center" :size="20">
          <div class="app-menu-wrapper">
            <n-popover
              trigger="click"
              placement="bottom"
              :show="showAppMenu"
              @update:show="showAppMenu = $event"
            >
              <template #trigger>
                <div class="app-switch-trigger">
                  <n-icon size="24" class="app-switch-icon">
                    <AppSwitcher />
                  </n-icon>
                </div>
              </template>
              <div class="app-menu-grid">
                <div
                  v-for="option in appSwitchOptions"
                  :key="option.key"
                  class="app-menu-item"
                  :class="{ 'app-menu-item-active': currentMenu === option.key }"
                  @click="handleAppSwitchSelect(option.key)"
                >
                  <n-icon size="24" :color="option.color">
                    <component :is="option.icon" />
                  </n-icon>
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </n-popover>
          </div>
          <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
            <n-space align="center" class="user-dropdown-trigger">
              <n-icon size="30" :component="PersonCircleOutline" />
              <span>{{ mainStore.user?.nickname || '用户' }},</span>
              <span>{{ greeting }} </span>
            </n-space>
          </n-dropdown>
        </n-space>
      </div>
    </n-layout-header>
    <n-layout-content content-style="padding: 14px;" class="main-content">
      <div class="content-wrapper">
        <component 
          :is="componentMap[currentComponent]" 
          v-if="componentMap[currentComponent]"
        />
        <div v-else class="placeholder">
          <n-empty description="功能开发中..." />
        </div>
      </div>
    </n-layout-content>
  </n-layout>
</template>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.logo-container {
  margin-top: 12px;
}

.main-content {
  height: calc(100vh - 64px);
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.content-wrapper) {
  position: absolute;
  top: 14px;
  left: 14px;
  right: 14px;
  bottom: 14px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Webkit 浏览器的滚动条样式 */
:deep(.content-wrapper::-webkit-scrollbar) {
  width: 6px;
  background-color: transparent;
}

:deep(.content-wrapper::-webkit-scrollbar-thumb) {
  background-color: transparent;
  border-radius: 3px;
  transition: background-color 0.3s;
}

/* 鼠标悬停或滚动时显示滚动条 */
:deep(.content-wrapper:hover::-webkit-scrollbar-thumb),
:deep(.content-wrapper:active::-webkit-scrollbar-thumb) {
  background-color: #d9d9d9;
}

/* 滚动条轨道 */
:deep(.content-wrapper::-webkit-scrollbar-track) {
  background-color: transparent;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown-trigger {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown-trigger:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.app-menu-wrapper {
  position: relative;
}

:deep(.n-popover) {
  padding: 0 !important;
  border-radius: 12px !important;
}

:deep(.n-popover-content-wrapper) {
  transform-origin: top center !important;
}

.app-menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px;
  width: 240px;
}

.app-menu-item {
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: transparent;
}

.app-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.app-menu-item .n-icon {
  margin-bottom: 8px;
  font-size: 24px;
  transition: transform 0.3s;
}

.app-menu-item:hover .n-icon {
  transform: scale(1.1);
}

.app-menu-item span {
  font-size: 12px;
  color: #333;
  text-align: center;
  white-space: nowrap;
  margin-top: 4px;
}

.app-switch-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.app-switch-trigger:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.app-switch-icon {
  transition: transform 0.3s;
}

.app-switch-icon.active {
  transform: rotate(180deg);
}

.placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
}
</style>
