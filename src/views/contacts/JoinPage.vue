<template>
  <div class="invite-container">
    <n-card class="invite-card">
      <template #header>
        <div class="card-header">
          <n-icon size="48" color="#2080f0">
            <TeamOutlined />
          </n-icon>
          <h2>团队邀请</h2>
        </div>
      </template>

      <div v-if="loading" class="loading-wrapper">
        <n-spin size="large" />
        <p>正在获取邀请信息...</p>
      </div>

      <div v-else-if="error" class="error-wrapper">
        <n-result status="error" title="获取邀请信息失败" :description="error">
          <template #footer>
            <n-button @click="fetchInviteInfo">重试</n-button>
          </template>
        </n-result>
      </div>

      <div v-else class="invite-content">
        <div class="invite-info">
          <p class="welcome-text">
            您好，<span class="highlight">{{ inviteInfo.nickname }}</span>，
            请确认是否加入<span class="highlight">{{ inviteInfo.deptName }}</span>？
            <br>确认加入请在下方设置您的登陆密码
          </p>
        </div>

        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="80"
          class="form-wrapper"
        >
          <div class="username-display">
            <span class="label">您的登陆用户名为：</span>
            <span class="username">{{ inviteInfo.username }}</span>
          </div>
          <n-form-item label="设置密码" path="password">
            <n-input
              v-model:value="formModel.password"
              type="password"
              placeholder="请输入密码"
              @keydown.enter.prevent
            />
          </n-form-item>
          <n-form-item label="确认密码" path="confirmPassword">
            <n-input
              v-model:value="formModel.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              @keydown.enter.prevent
            />
          </n-form-item>
          <n-form-item path="agreement">
            <n-checkbox v-model:checked="formModel.agreement">
              我已阅读并同意千诀科技开放平台的
              <a href="https://home.qj-robots.com/policies.pdf" target="_blank" class="agreement-link">用户协议、隐私政策</a>
            </n-checkbox>
          </n-form-item>
        </n-form>

        <div class="actions">
          <n-button
            type="primary"
            size="large"
            :loading="joining"
            :disabled="joining"
            block
            @click="handleJoin"
          >
            {{ joining ? '正在加入...' : '加入团队' }}
          </n-button>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  NCard, 
  NButton, 
  NSpin, 
  NResult, 
  NForm, 
  NFormItem, 
  NInput,
  NIcon,
  NCheckbox
} from 'naive-ui'
import { TeamOutlined } from '@vicons/antd'
import messages from '@/utils/messages'
import requests from '@/utils/requests'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)

const loading = ref(true)
const error = ref('')
const joining = ref(false)
const inviteInfo = ref({})

const formModel = ref({
  password: '',
  confirmPassword: '',
  agreement: false
})

const rules = {
  password: [
    { required: true, message: '请输入密码' },
    { min: 9, message: '密码长度不能小于9位' },
    {
      validator: (rule, value) => {
        if (!value) return true
        
        // 检查密码复杂度
        const hasUpperCase = /[A-Z]/.test(value)
        const hasLowerCase = /[a-z]/.test(value)
        const hasNumber = /[0-9]/.test(value)
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)
        
        // 计算满足的条件数
        const conditions = [
          hasUpperCase,
          hasLowerCase,
          hasNumber,
          hasSpecial
        ].filter(Boolean).length
        
        if (conditions < 2) {
          return new Error('密码必须包含大写字母、小写字母、数字、特殊符号中的至少2种')
        }
        return true
      }
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (rule, value) => {
        return value === formModel.value.password || new Error('两次输入的密码不一致')
      }
    }
  ],
  agreement: [
    {
      validator: (rule, value) => {
        if (!value) {
          return new Error('请阅读并同意用户协议和隐私政策')
        }
        return true
      },
      trigger: ['change', 'submit']
    }
  ]
}

const fetchInviteInfo = async () => {
  const inviteCode = route.query.invite_code
  if (!inviteCode) {
    error.value = '邀请链接无效，缺少邀请码'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''
    const { data } = await requests.get(`/auth-center/system/invitation`, {
      params: { invite_code: inviteCode }
    })
    inviteInfo.value = data
  } catch (err) {
    console.error('获取邀请信息错误:', err)
    error.value = err.message || '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

const handleJoin = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return

    const inviteCode = route.query.invite_code
    if (!inviteCode) {
      messages.error('邀请码无效')
      return
    }

    try {
      joining.value = true
      await requests.put(`/auth-center/system/invitation/${inviteCode}`, {
        id: inviteInfo.value.id,
        passwd: formModel.value.password
      })
      
      messages.success('成功加入团队，请使用设置的密码登录')
      // 跳转到登录页面并带上用户名
      router.push({
        path: '/login',
        query: { email: inviteInfo.value.username }
      })
    } catch (err) {
      messages.error(err.message || '加入团队失败')
    } finally {
      joining.value = false
    }
  })
}

onMounted(() => {
  fetchInviteInfo()
})
</script>

<style scoped>
.invite-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.invite-card {
  width: 100%;
  max-width: 480px;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
}

.loading-wrapper,
.error-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.invite-content {
  padding: 20px 0;
}

.welcome-text {
  line-height: 1.8;
  color: #333;
  margin: 0;
  padding: 0;
}

.highlight {
  color: #2080f0;
  font-weight: 500;
  margin: 0 4px;
  font-size: 16px;
}

.invite-info {
  margin-bottom: 32px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.info-item {
  margin: 8px 0;
  line-height: 1.5;
}

.info-item .label {
  color: #666;
  margin-right: 8px;
}

.info-item .value {
  color: #2c3e50;
  font-weight: 500;
}

.form-wrapper {
  margin-bottom: 24px;
}

.actions {
  margin-top: 32px;
}

.username-display {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 4px 0;
}

.username-display .label {
  color: #666;
}

.username-display .username {
  color: #2080f0;
  font-weight: 500;
  font-size: 16px;
}

.agreement-link {
  color: #2080f0;
  text-decoration: none;
}

.agreement-link:hover {
  text-decoration: underline;
}
</style> 