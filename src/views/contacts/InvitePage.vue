<template>
  <div class="invite-container">
    <n-card class="invite-card">
      <template #header>
        <div class="card-header">
          <n-icon size="48" color="#2080f0">
            <TeamOutlined />
          </n-icon>
          <h2>加入团队</h2>
        </div>
      </template>

      <div v-if="loading" class="loading-wrapper">
        <n-spin size="large" />
        <p>正在获取邀请信息...</p>
      </div>

      <div v-else-if="error" class="error-wrapper">
        <n-result status="error" title="获取邀请信息失败" :description="error">
          <template #footer>
            <n-button @click="fetchInviteInfo">重试</n-button>
          </template>
        </n-result>
      </div>

      <div v-else class="invite-content">
        <div class="invite-info">
          <p class="welcome-text">
            欢迎加入<span class="highlight">{{ inviteInfo.name }}</span>
          </p>
        </div>

        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="80"
          class="form-wrapper"
        >
          <n-form-item label="称呼" path="nickname">
            <n-input
              v-model:value="formModel.nickname"
              placeholder="请输入您的称呼（10字以内）"
              maxlength="10"
              @input="value => formModel.nickname = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '')"
            />
          </n-form-item>
          <n-form-item label="邮箱" path="email">
            <n-input
              v-model:value="formModel.email"
              placeholder="请输入邮箱（将作为登录用户名）"
            />
          </n-form-item>
          <n-form-item label="手机号" path="mobile">
            <n-input
              v-model:value="formModel.mobile"
              placeholder="请输入手机号"
            />
          </n-form-item>
          <n-form-item label="设置密码" path="password">
            <n-input
              v-model:value="formModel.password"
              type="password"
              placeholder="请输入密码"
              @keydown.enter.prevent
            />
          </n-form-item>
          <n-form-item label="确认密码" path="confirmPassword">
            <n-input
              v-model:value="formModel.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              @keydown.enter="handleJoin"
            />
          </n-form-item>
        </n-form>

        <div class="actions">
          <n-button
            type="primary"
            size="large"
            :loading="joining"
            :disabled="joining"
            block
            @click="handleJoin"
          >
            {{ joining ? '正在加入...' : '加入团队' }}
          </n-button>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  NCard, 
  NButton, 
  NSpin, 
  NResult, 
  NForm, 
  NFormItem, 
  NInput,
  NIcon
} from 'naive-ui'
import { TeamOutlined } from '@vicons/antd'
import messages from '@/utils/messages'
import { doGet, doPost } from '@/utils/requests'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)

const loading = ref(true)
const error = ref('')
const joining = ref(false)
const inviteInfo = ref({})

const formModel = ref({
  nickname: '',
  email: '',
  mobile: '',
  password: '',
  confirmPassword: ''
})

const rules = {
  nickname: [
    { required: true, message: '请输入您的称呼' },
    { max: 10, message: '称呼不能超过10个字符' },
    {
      validator: (rule, value) => {
        if (!value) return true
        if (/[^\u4e00-\u9fa5a-zA-Z0-9\s]/.test(value)) {
          return new Error('称呼不能包含特殊字符')
        }
        return true
      }
    }
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址'
    }
  ],
  mobile: [
    { required: true, message: '请输入手机号' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入有效的手机号'
    }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { min: 9, message: '密码长度不能小于9位' },
    {
      validator: (rule, value) => {
        if (!value) return true
        
        const hasUpperCase = /[A-Z]/.test(value)
        const hasLowerCase = /[a-z]/.test(value)
        const hasNumber = /[0-9]/.test(value)
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)
        
        const conditions = [
          hasUpperCase,
          hasLowerCase,
          hasNumber,
          hasSpecial
        ].filter(Boolean).length
        
        if (conditions < 2) {
          return new Error('密码必须包含大写字母、小写字母、数字、特殊符号中的至少2种')
        }
        return true
      }
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (rule, value) => {
        return value === formModel.value.password || new Error('两次输入的密码不一致')
      }
    }
  ]
}

const fetchInviteInfo = async () => {
  const inviteCode = route.query.dept_invite_code
  if (!inviteCode) {
    error.value = '邀请链接无效，缺少邀请码'
    loading.value = false
    return
  }

  loading.value = true
  const { data } = await doGet(`/auth-center/system/department/invitation?invite_code=${inviteCode}`)
  inviteInfo.value = data
  loading.value = false
}

const handleJoin = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return

    joining.value = true
    try {
      await doPost('/auth-center/system/user/join', {
        username: formModel.value.email,
        passwd: formModel.value.password,
        workMobile: formModel.value.mobile,
        nickname: formModel.value.nickname,
        disabled: false,
        tenantId: inviteInfo.value.tenantId,
        departmentId: inviteInfo.value.id
      })
      
      messages.success('成功加入团队，请使用邮箱和密码登录')
      router.push({
        path: '/login',
        query: { email: formModel.value.email }
      })
    } finally {
      joining.value = false
    }
  })
}

onMounted(() => {
  fetchInviteInfo()
})
</script>

<style scoped>
.invite-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.invite-card {
  width: 100%;
  max-width: 480px;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
}

.loading-wrapper,
.error-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.invite-content {
  padding: 20px 0;
}

.welcome-text {
  line-height: 1.8;
  color: #333;
  margin: 0;
  padding: 0;
  text-align: center;
  font-size: 16px;
}

.highlight {
  color: #2080f0;
  font-weight: 500;
  margin: 0 4px;
  font-size: 18px;
}

.invite-info {
  margin-bottom: 32px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.form-wrapper {
  margin-bottom: 24px;
}

.actions {
  margin-top: 32px;
}
</style> 