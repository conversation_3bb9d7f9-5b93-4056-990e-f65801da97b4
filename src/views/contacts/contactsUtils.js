// ContactsPage 组件的工具函数

/**
 * 将扁平的组织数据转换为树形结构
 * @param {Array} flatData - 扁平的组织数据
 * @returns {Array} 树形结构的数据
 */
export function buildTree(flatData) {
  const map = {};
  const roots = [];

  // 创建映射
  flatData.forEach((item) => {
    map[item.id] = {
      key: item.id.toString(),
      label: item.name,
      children: [],
      ...item,
    };
  });

  // 构建树形结构
  flatData.forEach((item) => {
    if (item.parentId === 0 || item.parentId === null) {
      roots.push(map[item.id]);
    } else if (map[item.parentId]) {
      map[item.parentId].children.push(map[item.id]);
    }
  });

  return roots;
}

/**
 * 获取树形结构的所有展开的key
 * @param {Array} treeData - 树形数据
 * @returns {Array} 展开的key数组
 */
export function getAllExpandedKeys(treeData) {
  const keys = [];
  
  function traverse(nodes) {
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        keys.push(node.key);
        traverse(node.children);
      }
    });
  }
  
  traverse(treeData);
  return keys;
}

/**
 * 在树形数据中查找节点
 * @param {Array} treeData - 树形数据
 * @param {string} key - 要查找的节点key
 * @returns {Object|null} 找到的节点或null
 */
export function findNodeInTree(treeData, key) {
  function traverse(nodes) {
    for (const node of nodes) {
      if (node.key === key) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = traverse(node.children);
        if (found) return found;
      }
    }
    return null;
  }
  
  return traverse(treeData);
}

/**
 * 获取节点的所有父级节点key
 * @param {Array} treeData - 树形数据
 * @param {string} targetKey - 目标节点key
 * @returns {Array} 父级节点key数组
 */
export function getParentKeys(treeData, targetKey) {
  const parentKeys = [];
  
  function traverse(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.key];
      
      if (node.key === targetKey) {
        parentKeys.push(...path);
        return true;
      }
      
      if (node.children && node.children.length > 0) {
        if (traverse(node.children, currentPath)) {
          return true;
        }
      }
    }
    return false;
  }
  
  traverse(treeData);
  return parentKeys;
}

/**
 * 格式化时间显示
 * @param {string|Date} time - 时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time) {
  if (!time) return '-';
  
  try {
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return '-';
  }
}

/**
 * 验证手机号格式
 * @param {string} mobile - 手机号
 * @returns {boolean} 是否有效
 */
export function validateMobile(mobile) {
  const mobileRegex = /^1[3-9]\d{9}$/;
  return mobileRegex.test(mobile);
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 生成用户状态标签的属性
 * @param {string} status - 用户状态
 * @returns {Object} 标签属性
 */
export function getUserStatusTagProps(status) {
  return {
    size: "small",
    type: status === "active" ? "success" : "error",
    round: true,
    style: {
      minWidth: "60px",
      justifyContent: "center",
    },
  };
}

/**
 * 获取用户状态显示文本
 * @param {string} status - 用户状态
 * @returns {string} 显示文本
 */
export function getUserStatusText(status) {
  return status === "active" ? "正常" : "停用";
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0;
  return function (...args) {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}
