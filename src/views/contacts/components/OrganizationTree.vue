<template>
  <div class="org-tree-container">
    <div class="tree-header">
      <span>组织机构</span>
      <n-button type="primary" ghost size="small" @click="() => handleAddOrg()">
        <template #icon>
          <n-icon>
            <AddCircleOutline />
          </n-icon>
        </template>
        新增组织
      </n-button>
    </div>
    <n-tree
      class="org-tree"
      :data="orgTreeData"
      :selected-keys="selectedKeys"
      :expanded-keys="expandedKeys"
      :loading="loading"
      block-line
      selectable
      expand-on-click
      @update:selected-keys="handleSelect"
      @update:expanded-keys="handleExpand"
    />
  </div>

  <!-- 新增组织弹窗 -->
  <n-modal v-model:show="showOrgModal" preset="dialog" title="新增组织">
    <n-form
      ref="orgFormRef"
      :model="orgFormModel"
      :rules="orgFormRules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item v-if="!isTopLevelAdd" label="上级组织" path="parentKey">
        <n-select
          v-model:value="orgFormModel.parentKey"
          :options="parentOrgOptions"
          placeholder="请选择上级组织"
        />
      </n-form-item>
      <n-form-item label="组织名称" path="name">
        <n-input
          v-model:value="orgFormModel.name"
          placeholder="请输入组织名称"
        />
      </n-form-item>
      <n-form-item label="组织类型" path="type">
        <n-select
          v-model:value="orgFormModel.type"
          :options="orgTypeOptions"
          placeholder="请选择组织类型"
        />
      </n-form-item>
    </n-form>
    <template #action>
      <n-space>
        <n-button @click="showOrgModal = false">取消</n-button>
        <n-button type="primary" :loading="orgLoading" @click="handleOrgSubmit">
          确定
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import {
  NTree,
  NButton,
  NIcon,
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NInput,
  NSpace,
  NDropdown,
  useDialog,
} from "naive-ui";
import {
  AddCircleOutline,
  CreateOutline,
  TrashOutline,
  EllipsisVerticalOutline,
} from "@vicons/ionicons5";
import { ref, computed, watch, h } from "vue";
import { contactsApi } from "@/api/contacts";
import messages from "@/utils/messages";
import {
  orgTypeOptions,
  orgFormRules,
  defaultOrgFormModel,
} from "../contactsConfig.js";

// 定义 props
const props = defineProps({
  isAdmin: {
    type: Boolean,
    default: false,
  },
  selectedOrgKey: {
    type: String,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits([
  "update:selectedOrgKey",
  "orgSelect",
  "orgTreeLoaded",
]);

// 对话框
const dialog = useDialog();

// 组织机构树数据
const orgTreeData = ref([]);
const selectedKeys = ref([]);
const expandedKeys = ref([]);
const loading = ref(false);

// 新增组织相关
const showOrgModal = ref(false);
const orgFormRef = ref(null);
const orgLoading = ref(false);
const isTopLevelAdd = ref(false);
const orgFormModel = ref({ ...defaultOrgFormModel });

// 监听外部传入的选中key
watch(
  () => props.selectedOrgKey,
  (newKey) => {
    if (newKey) {
      selectedKeys.value = [newKey];
    }
  },
  { immediate: true }
);

// 父级组织选项
const parentOrgOptions = computed(() => {
  const options = [];
  const traverse = (nodes, prefix = "") => {
    nodes.forEach((node) => {
      options.push({
        label: prefix + node.label,
        value: node.key,
      });
      if (node.children) {
        traverse(node.children, prefix + node.label + " / ");
      }
    });
  };
  traverse(orgTreeData.value);
  return options;
});

// 构建树数据的函数（包含UI逻辑）
const buildTreeWithActions = (flatData) => {
  const treeData = [];
  const map = {};

  // 检查数据有效性
  if (!flatData || !Array.isArray(flatData)) {
    console.warn(
      "buildTreeWithActions: flatData is not a valid array",
      flatData
    );
    return treeData;
  }

  // 首先创建所有节点的映射
  flatData.forEach((item) => {
    // 验证item数据的完整性
    if (!item || !item.id || !item.name) {
      console.warn("Invalid item data:", item);
      return;
    }

    map[item.id] = {
      key: item.id.toString(),
      label: item.name,
      children: [],
      // 添加 suffix 函数来渲染操作按钮
      suffix: () =>
        selectedKeys.value.includes(item.id.toString())
          ? h(
              NDropdown,
              {
                trigger: "hover",
                options: [
                  {
                    label: "新增下级",
                    key: "add-child",
                    icon: () => h(NIcon, { component: AddCircleOutline }),
                  },
                  {
                    label: "重命名",
                    key: "rename",
                    icon: () => h(NIcon, { component: CreateOutline }),
                  },
                  {
                    label: "删除",
                    key: "delete",
                    icon: () => h(NIcon, { component: TrashOutline }),
                  },
                ],
                onSelect: (key) => handleOrgAction(key, map[item.id]),
              },
              {
                default: () =>
                  h(
                    NButton,
                    {
                      size: "small",
                      quaternary: true,
                      circle: true,
                      class: "org-tree-action",
                    },
                    {
                      icon: () =>
                        h(NIcon, { component: EllipsisVerticalOutline }),
                    }
                  ),
              }
            )
          : null,
      ...item,
    };
  });

  // 构建树形结构
  flatData.forEach((item) => {
    // 验证item数据
    if (!item || !item.id || !map[item.id]) {
      console.warn("Invalid item or missing map entry:", item);
      return;
    }

    if (item.parentId === 0 || item.parentId === null) {
      treeData.push(map[item.id]);
    } else if (map[item.parentId]) {
      // 确保children数组存在
      if (!map[item.parentId].children) {
        map[item.parentId].children = [];
      }
      map[item.parentId].children.push(map[item.id]);
    } else {
      console.warn("Parent not found for item:", item);
    }
  });

  return treeData;
};

// 加载组织机构树数据
const loadOrgTree = async () => {
  loading.value = true;
  try {
    const response = await contactsApi.getOrganizations(props.isAdmin);
    const data = response?.data || [];

    // 将扁平数据转换为树形结构
    const treeData = buildTreeWithActions(data);
    orgTreeData.value = treeData;

    // 默认选中第一个根节点
    if (treeData && treeData.length > 0 && !props.selectedOrgKey) {
      const firstKey = treeData[0].key;
      selectedKeys.value = [firstKey];
      emit("update:selectedOrgKey", firstKey);
      emit("orgSelect", firstKey);
    }

    // 默认展开所有节点
    const allKeys = [];
    const traverse = (nodes) => {
      nodes.forEach((node) => {
        allKeys.push(node.key);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    expandedKeys.value = allKeys;

    // 通知父组件树已加载
    emit("orgTreeLoaded", treeData);
  } catch (error) {
    console.error("Failed to load organizations:", error);
    messages.error("加载组织机构失败");
  } finally {
    loading.value = false;
  }
};

// 处理节点选择
const handleSelect = (keys) => {
  if (keys.length > 0) {
    selectedKeys.value = keys;
    emit("update:selectedOrgKey", keys[0]);
    emit("orgSelect", keys[0]);
  }
};

// 处理节点展开
const handleExpand = (keys) => {
  expandedKeys.value = keys;
};

// 处理新增组织
const handleAddOrg = (parentId = null) => {
  showOrgModal.value = true;
  // 重置表单
  orgFormModel.value = { ...defaultOrgFormModel };

  // 如果传入了父级ID，说明是从节点菜单触发的，显示上级组织字段
  if (parentId) {
    isTopLevelAdd.value = false;
    orgFormModel.value.parentKey = parentId;
  } else {
    // 从顶部按钮触发的，不显示上级组织字段，默认传0到接口
    isTopLevelAdd.value = true;
    orgFormModel.value.parentKey = "";
  }
};

// 处理提交新增组织
const handleOrgSubmit = () => {
  orgFormRef.value?.validate(async (errors) => {
    if (errors) return;

    orgLoading.value = true;
    try {
      await contactsApi.createOrganization(
        {
          name: orgFormModel.value.name,
          parentId: orgFormModel.value.parentKey || 0,
          type: orgFormModel.value.type,
        },
        props.isAdmin
      );

      messages.success("创建组织成功");
      showOrgModal.value = false;
      // 重新加载组织树
      await loadOrgTree();
      // 重置表单
      orgFormModel.value = { ...defaultOrgFormModel };
    } catch (error) {
      console.error("创建组织失败:", error);
      messages.error("创建组织失败");
    } finally {
      orgLoading.value = false;
    }
  });
};

// 处理组织节点操作
const handleOrgAction = (action, node) => {
  switch (action) {
    case "add-child":
      handleAddOrg(node.key);
      break;
    case "rename":
      handleRenameOrg(node);
      break;
    case "delete":
      handleDeleteOrg(node);
      break;
  }
};

// 处理重命名组织
const handleRenameOrg = (node) => {
  messages.info("重命名功能开发中...");
};

// 处理删除组织
const handleDeleteOrg = (node) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除组织"${node.label}"吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      messages.info("删除功能开发中...");
    },
  });
};

// 暴露方法给父组件
defineExpose({
  loadOrgTree,
  refreshTree: loadOrgTree,
});

// 组件挂载时加载数据
loadOrgTree();
</script>

<style scoped>
.org-tree-container {
  height: 100%;
  max-height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
  flex-shrink: 0;
}

.tree-header span {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.org-tree {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  margin-bottom: 0;
  min-height: 0;
  max-height: calc(95vh - 300px);
}

/* 自定义滚动条样式 */
.org-tree::-webkit-scrollbar {
  width: 2px;
}

.org-tree::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.org-tree::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 树节点操作按钮样式 */
.org-tree-action {
  margin-left: 8px;
  /* 添加左边距 */
}

/* 确保下拉菜单在最上层 */
:deep(.n-dropdown-menu) {
  z-index: 1000;
}

/* 树节点展开图标样式 */
:deep(.n-tree-node-switcher) {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 树节点图标和文字对齐 */
:deep(.n-tree-node-wrapper) {
  display: flex;
  align-items: center;
}

/* 确保树节点内容垂直居中 */
:deep(.n-tree-node) {
  display: flex;
  align-items: center;
  width: 280px;
  height: 32px;
}

</style>
