<template>
  <n-modal
    v-model:show="visible"
    preset="dialog"
    title="修改用户信息"
    positive-text="保存"
    negative-text="取消"
    :loading="loading"
    @positive-click="handleSubmit"
    @negative-click="handleCancel"
    :style="{ width: isAdmin ? '800px' : '600px' }"
    class="edit-user-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="editUserFormRules"
      label-placement="top"
      require-mark-placement="right-hanging"
      :show-feedback="true"
      :show-label="true"
    >
      <!-- 基础信息 -->
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="用户名" path="nickname">
            <n-input
              v-model:value="formData.nickname"
              placeholder="请输入用户名"
              :maxlength="20"
              show-count
              size="large"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="登录名" path="username">
            <n-input
              v-model:value="formData.username"
              placeholder="请输入邮箱地址作为登录名"
              type="email"
              size="large"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="手机号码" path="workMobile">
            <n-input
              v-model:value="formData.workMobile"
              placeholder="请输入11位手机号码"
              :maxlength="11"
              size="large"
              :allow-input="onlyAllowNumber"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="所属部门" path="departmentId">
            <n-select
              v-model:value="formData.departmentId"
              placeholder="请选择所属部门"
              :options="departmentOptions"
              size="large"
              disabled
              filterable
            />
          </n-form-item>
        </n-grid-item>
      </n-grid>

      <!-- 管理员模式下的额外字段 -->
      <template v-if="isAdmin">
        <!-- 剩余次数字段 -->
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="感知模型WEB剩余次数" path="perceptionWebCount">
              <n-input-number
                v-model:value="formData.perceptionWebCount"
                placeholder="请输入剩余次数"
                :min="0"
                button-placement="both"
                :max="999999999"
                size="large"
                style="width: 100%"
                class="centered-input-number"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="感知模型API剩余次数" path="perceptionApiCount">
              <n-input-number
                v-model:value="formData.perceptionApiCount"
                placeholder="请输入剩余次数"
                :min="0"
                :max="999999999"
                size="large"
                button-placement="both"
                style="width: 100%"
                class="centered-input-number"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="决策模型WEB剩余次数" path="decisionWebCount">
              <n-input-number
                v-model:value="formData.decisionWebCount"
                placeholder="请输入剩余次数"
                :min="0"
                button-placement="both"
                :max="999999999"
                size="large"
                style="width: 100%"
                class="centered-input-number"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="决策模型API剩余次数" path="decisionApiCount">
              <n-input-number
                v-model:value="formData.decisionApiCount"
                placeholder="请输入剩余次数"
                :min="0"
                button-placement="both"
                :max="999999999"
                size="large"
                style="width: 100%"
                class="centered-input-number"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- SKU选择表格 -->
        <n-form-item path="skuId">
          <template #label>
            <span>选择服务套餐</span>
            <span style="color: red; font-size: 12px; margin-left: 8px">
              修改服务套餐将会影响该机构的全部用户
            </span>
          </template>
          <div style="width: 100%">
            <n-data-table
              :columns="skuColumns"
              :data="skuList"
              :row-key="(row) => row.id"
              single-line
              size="small"
              max-height="300px"
            />
          </div>
        </n-form-item>
      </template>
    </n-form>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, nextTick, h } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NGrid,
  NGridItem,
  NInputNumber,
  NDataTable,
  NRadio,
} from "naive-ui";
import { contactsApi } from "@/api/contacts";
import messages from "@/utils/messages";
import { skuList, baseSkuColumns } from "@/config/skuConfig";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  userData: {
    type: Object,
    default: () => ({}),
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:show", "submit"]);

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

const formRef = ref(null);
const loading = ref(false);
const formData = ref({
  nickname: "",
  username: "",
  workMobile: "",
  departmentId: null,
  // 管理员模式下的额外字段
  skuId: null,
  perceptionWebCount: null,
  perceptionApiCount: null,
  decisionWebCount: null,
  decisionApiCount: null,
});

// 部门选项
const departmentOptions = ref([]);

// SKU选择状态
const selectedSkuKeys = ref([]);

// 计算选中的SKU信息
const selectedSkuInfo = computed(() => {
  if (selectedSkuKeys.value.length === 0) return null;
  return skuList.find((sku) => sku.id === selectedSkuKeys.value[0]);
});

// 动态生成SKU表格列定义
const skuColumns = computed(() => {
  return [
    {
      title: "选择",
      key: "select",
      align: "center",
      width: 60,
      render(row) {
        return h(NRadio, {
          checked: selectedSkuKeys.value.includes(row.id),
          onUpdateChecked: (checked) => {
            if (checked) {
              handleSkuSelect([row.id]);
            }
          },
        });
      },
    },
    ...baseSkuColumns,
  ];
});

// 表单验证规则
const editUserFormRules = computed(() => {
  const baseRules = {
    nickname: [
      {
        required: true,
        message: "请输入用户名",
        trigger: ["input", "blur"],
      },
      {
        min: 1,
        max: 20,
        message: "用户名长度应在1-20个字符之间",
        trigger: ["input", "blur"],
      },
    ],
    username: [
      {
        required: true,
        message: "请输入登录名",
        trigger: ["input", "blur"],
      },
      {
        type: "email",
        message: "请输入有效的邮箱地址",
        trigger: ["input", "blur"],
      },
    ],
    workMobile: [
      {
        required: true,
        message: "请输入手机号码",
        trigger: ["input", "blur"],
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入有效的11位手机号码",
        trigger: ["input", "blur"],
      },
    ],
  };

  // 管理员模式下添加额外的验证规则
  if (props.isAdmin) {
    baseRules.perceptionWebCount = [
      {
        type: "number",
        message: "请输入有效的数字",
        trigger: ["input", "blur"],
      },
    ];
    baseRules.perceptionApiCount = [
      {
        type: "number",
        message: "请输入有效的数字",
        trigger: ["input", "blur"],
      },
    ];
    baseRules.decisionWebCount = [
      {
        type: "number",
        message: "请输入有效的数字",
        trigger: ["input", "blur"],
      },
    ];
    baseRules.decisionApiCount = [
      {
        type: "number",
        message: "请输入有效的数字",
        trigger: ["input", "blur"],
      },
    ];
  }

  return baseRules;
});

// 手机号输入限制 - 只允许数字
const onlyAllowNumber = (value) => !value || /^\d+$/.test(value);

// 加载部门选项
const loadDepartmentOptions = async () => {
  try {
    const { data } = await contactsApi.getOrganizations(props.isAdmin);
    departmentOptions.value = buildDepartmentOptions(data);
  } catch (error) {
    console.error("加载部门列表失败:", error);
    messages.error("加载部门列表失败");
  }
};

// 构建部门选项树
const buildDepartmentOptions = (departments) => {
  const options = [];

  const buildOptions = (items, level = 0) => {
    items.forEach((item) => {
      const prefix = "　".repeat(level);
      options.push({
        label: prefix + item.name,
        value: item.id,
      });

      if (item.children && item.children.length > 0) {
        buildOptions(item.children, level + 1);
      }
    });
  };

  buildOptions(departments);
  return options;
};

// 处理SKU选择
const handleSkuSelect = (keys) => {
  selectedSkuKeys.value = keys;
};

// 监听SKU选择变化
watch(selectedSkuInfo, (newSku) => {
  formData.value.skuId = newSku?.id || null;
  // 当选择了SKU时，清除该字段的验证错误状态
  if (formRef.value && newSku) {
    nextTick(() => {
      try {
        // 清除skuId字段的验证状态
        formRef.value.restoreValidation();
      } catch (error) {
        console.warn("清除表单验证状态失败:", error);
      }
    });
  }
});

// 处理提交
const handleSubmit = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (errors) => {
      if (errors) {
        // 显示具体的验证错误信息
        const errorMessages = [];
        Object.keys(errors).forEach((field) => {
          if (errors[field] && errors[field].length > 0) {
            errorMessages.push(errors[field][0].message);
          }
        });
        messages.error(
          errorMessages.length > 0 ? errorMessages[0] : "请检查表单信息"
        );
        reject(new Error("表单验证失败"));
        return;
      }

      loading.value = true;
      try {
        // 准备提交数据，排除departmentId
        const { departmentId, ...submitData } = formData.value;

        // 触发提交事件
        emit("submit", {
          userId: props.userData.id,
          formData: submitData,
        });

        // 关闭弹窗
        visible.value = false;
        resolve();
      } catch (error) {
        console.error("修改用户失败:", error);
        messages.error("修改用户失败");
        reject(error);
      } finally {
        loading.value = false;
      }
    });
  });
};

// 处理取消
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 重置表单
const resetForm = () => {
  formData.value = {
    nickname: "",
    username: "",
    workMobile: "",
    departmentId: null,
    skuId: null,
    perceptionWebCount: null,
    perceptionApiCount: null,
    decisionWebCount: null,
    decisionApiCount: null,
  };
  selectedSkuKeys.value = [];
  formRef.value?.restoreValidation();
};

// 初始化表单数据
const initFormData = async () => {
  if (props.userData && Object.keys(props.userData).length > 0) {
    // 基础信息
    formData.value = {
      nickname: props.userData.name || "",
      username: props.userData.email || "",
      workMobile: props.userData.workMobile || "",
      departmentId: props.userData.departmentId || null,
      skuId: null,
      perceptionWebCount: null,
      perceptionApiCount: null,
      decisionWebCount: null,
      decisionApiCount: null,
    };

    // 管理员模式下加载用户详情
    if (props.isAdmin && props.userData.id) {
      try {
        const { data } = await contactsApi.getUserDetail(
          props.userData.id,
          props.isAdmin
        );

        // 更新所有字段，包括departmentId
        formData.value.skuId = data.skuId || null;
        formData.value.perceptionWebCount = data.perceptionWebCount || 0;
        formData.value.perceptionApiCount = data.perceptionApiCount || 0;
        formData.value.decisionWebCount = data.decisionWebCount || 0;
        formData.value.decisionApiCount = data.decisionApiCount || 0;

        // 如果API返回了departmentId，使用API的值覆盖初始值
        if (data.departmentId !== undefined && data.departmentId !== null) {
          formData.value.departmentId = data.departmentId;
        }

        // 设置SKU选择状态
        if (data.skuId) {
          selectedSkuKeys.value = [data.skuId];
        } else {
          selectedSkuKeys.value = [];
        }
      } catch (error) {
        console.error("加载用户详情失败:", error);
        messages.error("加载用户详情失败");
      }
    }
  }
};

// 监听弹窗显示状态
watch(visible, async (newVisible) => {
  if (newVisible) {
    await loadDepartmentOptions();
    await initFormData();
  } else {
    resetForm();
  }
});
</script>

<style scoped>
/* 表单项间距优化 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

/* 表单验证反馈样式 */
:deep(.n-form-item--feedback-error .n-input) {
  border-color: #d03050;
}

:deep(.n-form-item--feedback-error .n-input:focus) {
  border-color: #d03050;
  box-shadow: 0 0 0 2px rgba(208, 48, 80, 0.2);
}

:deep(.n-form-item--feedback-success .n-input) {
  border-color: #18a058;
}

/* 网格布局优化 */
:deep(.n-grid) {
  margin-bottom: 8px;
}

/* 确保弹窗按钮对齐 */
.edit-user-modal :deep(.n-dialog__action) {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

/* 数字输入框居中样式 */
.centered-input-number :deep(.n-input-number-input) {
  text-align: center;
}

.centered-input-number :deep(.n-input__input-el) {
  text-align: center;
}
</style>
