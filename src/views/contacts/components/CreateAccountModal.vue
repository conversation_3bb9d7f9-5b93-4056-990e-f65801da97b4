<template>
  <n-modal
    v-model:show="visible"
    preset="dialog"
    title="开通账户"
    positive-text="开通"
    negative-text="取消"
    :loading="loading"
    @positive-click="handleSubmit"
    @negative-click="handleCancel"
    style="width: 800px"
    class="create-account-modal"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="accountFormRules"
      label-placement="top"
      require-mark-placement="right-hanging"
      :show-feedback="true"
      :show-label="true"
    >
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-form-item label="机构名称" path="organizationName">
            <n-input
              v-model:value="formData.organizationName"
              placeholder="请输入机构名称"
              :maxlength="20"
              show-count
              size="large"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="登录名" path="username">
            <n-input
              v-model:value="formData.username"
              placeholder="请输入邮箱地址作为登录名"
              type="email"
              size="large"
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="用户名" path="nickname">
            <n-input
              v-model:value="formData.nickname"
              placeholder="请输入用户名"
              size="large"
              :maxlength="20"
              show-count
            />
          </n-form-item>
        </n-grid-item>

        <n-grid-item>
          <n-form-item label="手机号码" path="mobile">
            <n-input
              v-model:value="formData.mobile"
              placeholder="请输入11位手机号码"
              :maxlength="11"
              size="large"
              :allow-input="onlyAllowNumber"
            />
          </n-form-item>
        </n-grid-item>

      </n-grid>

      <n-form-item path="selectedSku">
        <template #label>
          <span>选择开通服务</span>
          <span style="color: #999; font-size: 12px; margin-left: 8px">
            机构下的所有账号及应用将共享调用次数
          </span>
        </template>
        <div style="width: 100%">
          <n-data-table
            :columns="skuColumns"
            :data="skuList"
            :row-key="(row) => row.id"
            single-line
            size="small"
            max-height="300px"
          />
        </div>
      </n-form-item>
    </n-form>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch, h, nextTick } from "vue";
import {
  NModal,
  NForm,
  NFormItem,
  NInput,
  NDataTable,
  NGrid,
  NGridItem,
  NRadio,
} from "naive-ui";
import {
  skuList,
  baseSkuColumns,
  accountFormRules,
  defaultAccountForm,
} from "@/config/skuConfig";
import messages from "@/utils/messages";

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["update:show", "submit"]);

// 响应式数据
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

const formRef = ref(null);
const loading = ref(false);
const formData = ref({ ...defaultAccountForm });
const selectedSkuKeys = ref([]);

// 计算选中的SKU信息
const selectedSkuInfo = computed(() => {
  if (selectedSkuKeys.value.length === 0) return null;
  return skuList.find((sku) => sku.id === selectedSkuKeys.value[0]);
});

// 动态生成SKU表格列定义
const skuColumns = computed(() => {
  return [
    {
      title: "选择",
      key: "select",
      align: "center",
      width: 60,
      render(row) {
        return h(NRadio, {
          checked: selectedSkuKeys.value.includes(row.id),
          onUpdateChecked: (checked) => {
            if (checked) {
              handleSkuSelect([row.id]);
            }
          },
        });
      },
    },
    ...baseSkuColumns,
  ];
});

// 监听SKU选择变化
watch(selectedSkuInfo, (newSku) => {
  formData.value.selectedSku = newSku?.id || null;
  // 当选择了SKU时，清除该字段的验证错误状态
  if (formRef.value && newSku) {
    nextTick(() => {
      try {
        // 清除selectedSku字段的验证状态
        formRef.value.restoreValidation();
      } catch (error) {
        console.warn("清除表单验证状态失败:", error);
      }
    });
  }
});

// 处理SKU选择
const handleSkuSelect = (keys) => {
  selectedSkuKeys.value = keys;
};

// 手机号输入限制 - 只允许数字
const onlyAllowNumber = (value) => !value || /^\d+$/.test(value);

// 处理提交
const handleSubmit = () => {
  return new Promise((resolve, reject) => {
    formRef.value?.validate(async (errors) => {
      if (errors) {
        // 显示具体的验证错误信息
        const errorMessages = [];
        Object.keys(errors).forEach((field) => {
          if (errors[field] && errors[field].length > 0) {
            errorMessages.push(errors[field][0].message);
          }
        });
        messages.error(
          errorMessages.length > 0 ? errorMessages[0] : "请检查表单信息"
        );
        reject(new Error("表单验证失败"));
        return;
      }

      if (!selectedSkuInfo.value) {
        messages.error("请选择一个服务套餐");
        reject(new Error("未选择服务套餐"));
        return;
      }

      loading.value = true;
      try {
        // 构造提交数据
        const submitData = {
          ...formData.value,
          skuInfo: selectedSkuInfo.value,
        };

        // 触发提交事件
        emit("submit", submitData);

        // 重置表单并关闭弹窗
        resetForm();
        visible.value = false;
        resolve();
      } catch (error) {
        console.error("开通账户失败:", error);
        messages.error("开通账户失败");
        reject(error);
      } finally {
        loading.value = false;
      }
    });
  });
};

// 处理取消
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 重置表单
const resetForm = () => {
  formData.value = { ...defaultAccountForm };
  selectedSkuKeys.value = [];
  formRef.value?.restoreValidation();
};

// 监听弹窗显示状态，重置表单
watch(visible, (newVisible) => {
  if (newVisible) {
    resetForm();
  }
});
</script>

<style scoped>
:deep(.n-data-table-th) {
  text-align: center;
}

:deep(.n-data-table-td) {
  text-align: center;
}

/* 确保弹窗按钮对齐 */
.create-account-modal :deep(.n-dialog__action) {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

/* 表单项间距优化 */
:deep(.n-form-item) {
  margin-bottom: 20px;
}

/* 表单验证反馈样式 */
:deep(.n-form-item--feedback-error .n-input) {
  border-color: #d03050;
}

:deep(.n-form-item--feedback-error .n-input:focus) {
  border-color: #d03050;
  box-shadow: 0 0 0 2px rgba(208, 48, 80, 0.2);
}

:deep(.n-form-item--feedback-success .n-input) {
  border-color: #18a058;
}

/* 网格布局优化 */
:deep(.n-grid) {
  margin-bottom: 8px;
}

/* SKU表格样式优化 */
:deep(.n-data-table) {
  border-radius: 6px;
  overflow: hidden;
}
</style>
