<script setup>
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NDataTable,
  NSpace,
  NInput,
  NButton,
  NIcon,
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NTag,
  NDialog,
  useDialog,
  NDropdown,
  NTooltip,
} from "naive-ui";
import {
  SearchOutline,
  PersonAddOutline,
  StopCircleOutline,
  LinkOutline,
  EllipsisHorizontalOutline,
  CreateOutline,
  SwapHorizontalOutline,
  KeyOutline,
  TrashOutline,
  RefreshOutline,
  BusinessOutline,
} from "@vicons/ionicons5";
import { ref, computed, h } from "vue";
import { contactsApi } from "@/api/contacts";
import messages from "@/utils/messages";
import { doDelete, doPut, doPost } from "@/utils/requests";
import {
  formatRelativeTime,
  formatCreateTime,
  getDetailedTimeInfo,
} from "@/utils/timeUtils";

// 导入组件
import OrganizationTree from "./components/OrganizationTree.vue";
import CreateAccountModal from "./components/CreateAccountModal.vue";
import EditUserModal from "./components/EditUserModal.vue";

// 导入配置和工具函数
import {
  orgTypeOptions,
  orgFormRules,
  memberFormRules,
  defaultPagination,
  defaultOrgFormModel,
  defaultMemberFormModel,
  // getBaseColumns,
  // getAdminColumns,
  // getBaseActionOptions,
  // getAdminActionOptions,
  // getOrgNodeActions,
} from "./contactsConfig.js";
// 工具函数暂时保留导入，后续可能会使用
// import {
//   buildTree,
//   getAllExpandedKeys,
//   findNodeInTree,
//   getParentKeys,
//   formatTime,
//   validateMobile,
//   validateEmail,
//   getUserStatusTagProps,
//   getUserStatusText,
//   deepClone,
//   debounce,
//   throttle,
// } from "./contactsUtils.js";

// 定义 props
const props = defineProps({
  isAdmin: {
    type: Boolean,
    default: false,
  },
});

// 组织树组件引用
const orgTreeRef = ref(null);

// 选中的组织节点
const selectedOrgKey = ref(null);

// 添加选中行的状态
const checkedRowKeys = ref([]);

// 表格列定义
const columns = computed(() => {
  const baseColumns = [
    {
      type: "selection",
      align: "center",
      width: 50,
    },
    {
      title: "姓名",
      key: "name",
      align: "left",
      ellipsis: true,
      minWidth: 100,
    },
    {
      title: "部门",
      key: "department",
      align: "left",
      ellipsis: true,
      minWidth: 150,
    },
    {
      title: "手机",
      key: "workMobile",
      align: "left",
      ellipsis: true,
      minWidth: 120,
    },
    {
      title: "邮箱",
      key: "email",
      align: "left",
      ellipsis: true,
      minWidth: 180,
    },
  ];

  // 管理员模式下添加更多列
  if (props.isAdmin) {
    baseColumns.push(
      {
        title: "用户ID",
        key: "id",
        align: "center",
        width: 80,
      },
      {
        title: "创建时间",
        key: "createTime",
        align: "center",
        width: 160,
        render(row) {
          return h(
            NTooltip,
            {
              trigger: "hover",
            },
            {
              trigger: () => formatCreateTime(row.createTime),
              default: () => getDetailedTimeInfo(row.createTime),
            }
          );
        },
      },
      {
        title: "最后登录",
        key: "lastLoginTime",
        align: "center",
        width: 160,
        render(row) {
          const relativeTime = formatRelativeTime(row.lastLoginTime);
          return h(
            NTooltip,
            {
              trigger: "hover",
            },
            {
              trigger: () => relativeTime,
              default: () => getDetailedTimeInfo(row.lastLoginTime),
            }
          );
        },
      }
    );
  }

  // 添加状态列和操作列
  baseColumns.push(
    {
      title: "状态",
      key: "status",
      align: "center",
      width: 100,
      render(row) {
        return h(
          NTag,
          {
            size: "small",
            type: row.status === "active" ? "success" : "error",
            round: true,
            style: {
              minWidth: "60px",
              justifyContent: "center",
            },
          },
          { default: () => (row.status === "active" ? "正常" : "停用") }
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: props.isAdmin ? 150 : 120,
      align: "center",
      render(row) {
        return h("div", { class: "table-actions" }, [
          h(
            NButton,
            {
              size: "small",
              quaternary: true,
              circle: true,
              onClick: (e) => {
                e.stopPropagation();
                handleEditUser(row);
              },
            },
            { icon: () => h(NIcon, { component: CreateOutline }) }
          ),
          h(
            NDropdown,
            {
              trigger: "click",
              options: actionOptions(row),
              onSelect: (key) => handleActionSelect(key, row),
            },
            {
              default: () =>
                h(
                  NButton,
                  {
                    size: "small",
                    quaternary: true,
                    circle: true,
                    onClick: (e) => e.stopPropagation(),
                  },
                  {
                    icon: () =>
                      h(NIcon, { component: EllipsisHorizontalOutline }),
                  }
                ),
            }
          ),
        ]);
      },
    }
  );

  return baseColumns;
});

// 表格数据和分页状态
const tableData = ref([]);
const pagination = ref({ ...defaultPagination });

// 搜索关键词
const searchKeyword = ref("");

// 成员列表加载状态
const memberLoading = ref(false);
const selectedRowKeys = ref([]);

// 新增组织机构相关变量已移至 OrganizationTree 组件中

// parentOrgOptions 已移至 OrganizationTree 组件中

// 树节点加载状态已移至 OrganizationTree 组件中

// 处理组织树选择
const handleOrgSelect = async (orgKey) => {
  selectedOrgKey.value = orgKey;
  // 重置分页到第一页
  pagination.value.page = 1;
  // 加载选中节点的成员
  await loadMembers(orgKey);
};

// 处理分页变化
const handlePageChange = async (page) => {
  await loadMembers(selectedOrgKey.value, page);
};

// 添加页大小变化处函数
const handlePageSizeChange = async (pageSize) => {
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  await loadMembers(selectedOrgKey.value, 1);
};

// 处理搜索
const handleSearch = async () => {
  if (selectedOrgKey.value) {
    pagination.value.page = 1;
    await loadMembers(selectedOrgKey.value);
  }
};

// 处理清除搜索
const handleClearSearch = async () => {
  searchKeyword.value = "";
  if (selectedOrgKey.value) {
    pagination.value.page = 1;
    await loadMembers(selectedOrgKey.value);
  }
};

// 处理刷新
const handleRefresh = async () => {
  if (selectedOrgKey.value) {
    await loadMembers(selectedOrgKey.value, pagination.value.page);
  }
};

// 处理添加成员
const handleAddMember = () => {
  // 设置默认部门ID为当前选中的组织
  memberFormModel.value.departmentId = selectedOrgKey.value;
  showMemberModal.value = true;
};

// 新增组织相关函数已移至 OrganizationTree 组件中

// defaultExpandedKeys 已移至 OrganizationTree 组件中

// 处理组织树加载完成
const handleOrgTreeLoaded = (treeData) => {
  // 树加载完成后的处理逻辑
  console.log("组织树加载完成:", treeData);
};

// buildTreeWithActions 函数已移至 OrganizationTree 组件中

// 修改成员加载函数
const loadMembers = async (orgKey, page = 1) => {
  memberLoading.value = true;
  try {
    const { data } = await contactsApi.getMembers(
      orgKey,
      searchKeyword.value,
      page,
      pagination.value.pageSize,
      props.isAdmin
    );

    console.log("分页数据:", data); // 调试日志，查看返回的分页数据

    // 将后端数据映射到表格所需的格式
    tableData.value = data.records.map((item) => {
      const mappedItem = {
        id: item.id,
        name: item.nickname,
        department: item.deptName,
        departmentId: item.departmentId, // 保留部门ID
        employeeId: item.username,
        workMobile: item.workMobile,
        email: item.username,
        status: item.disabled ? "disabled" : "active",
        // position 字段暂时为空
        position: "",
        // 添加时间字段
        createTime: item.createTime,
        lastLoginTime: item.lastLoginTime,
      };
      console.log("映射后的数据项:", mappedItem); // 调试日志
      return mappedItem;
    });

    // 更新分页信息
    pagination.value.itemCount = data.totalRow;
    pagination.value.page = data.pageNumber;

    // 确保pageSize正确设置
    if (data.pageSize && data.pageSize > 0) {
      pagination.value.pageSize = data.pageSize;
    }

    // 计算总页数并打印日志
    const calculatedTotalPage = Math.ceil(
      data.totalRow / pagination.value.pageSize
    );
    console.log(
      "计算的总页数:",
      calculatedTotalPage,
      "后端返回的总页数:",
      data.totalPage
    );

    // 如果后端返回的总页数与计算的不一致，使用计算的总页数
    if (calculatedTotalPage !== data.totalPage) {
      console.warn("后端返回的总页数与计算的不一致，使用计算的总页数");
    }
  } catch (error) {
    console.error("Failed to load members:", error);
    messages.error("加载成员列表失败");
  } finally {
    memberLoading.value = false;
  }
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  checkedRowKeys.value = keys;
  console.log("选中的行:", keys);
};

// 组件挂载时的数据加载已移至 OrganizationTree 组件中

// 批量禁用处理函数
const handleBatchDisable = async () => {
  const selectedIds = checkedRowKeys.value;
  if (!selectedIds.length) return;

  dialog.warning({
    title: "确认禁用",
    content: `确定要禁用选中的 ${selectedIds.length} 名成员吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        await contactsApi.batchUpdateMemberStatus(
          selectedIds,
          "disabled",
          props.isAdmin
        );
        messages.success("批量禁用成功");
        // 重新加载数据
        await loadMembers(selectedOrgKey.value, pagination.value.page);
        // 清空选择
        checkedRowKeys.value = [];
      } catch (error) {
        messages.error("批量禁用失败");
      }
    },
  });
};

// 修改邀请成员处理函数
const handleInvite = async () => {
  try {
    const { data } = await doPost("/auth-center/system/department/invitation");
    // 复制到剪贴板
    navigator.clipboard
      .writeText(data.txt)
      .then(() => {
        messages.success("邀请链接已复制到剪贴板");
      })
      .catch(() => {
        messages.error("复制失败，请手动复制");
      });
  } catch (err) {
    messages.error("获取邀请链接失败");
  }
};

// 注入对话框服务
const dialog = useDialog();

// 添加操作菜单选项
const actionOptions = (row) => {
  const baseOptions = [
    {
      label: row.status === "active" ? "禁用" : "启用",
      key: row.status === "active" ? "disable" : "enable",
      props: {
        type: row.status === "active" ? "error" : "success",
      },
    },
    {
      label: "变更部门",
      key: "change-department",
    },
    {
      label: "重置密码",
      key: "reset-password",
    },
  ];

  // 管理员模式下添加更多操作
  if (props.isAdmin) {
    baseOptions.push(
      {
        type: "divider",
      },
      {
        label: "查看详情",
        key: "view-details",
        icon: () => h(NIcon, { component: SearchOutline }),
      },
      {
        label: "删除用户",
        key: "delete-user",
        props: {
          type: "error",
        },
        icon: () => h(NIcon, { component: TrashOutline }),
      }
    );
  }

  return baseOptions;
};

// 处理操作菜单点击
const handleActionSelect = (key, row) => {
  switch (key) {
    case "disable":
    case "enable":
      handleStatusChange(row);
      break;
    case "change-department":
      handleChangeDepartment(row);
      break;
    case "reset-password":
      handleResetPassword(row);
      break;
    case "view-details":
      handleViewDetails(row);
      break;
    case "delete-user":
      handleDeleteUser(row);
      break;
  }
};

// 处理状态变更
const handleStatusChange = (row) => {
  const newStatus = row.status === "active" ? "disabled" : "active";
  dialog.warning({
    title: `确认${newStatus === "active" ? "启用" : "禁用"}`,
    content: `确定要${newStatus === "active" ? "启用" : "禁用"}该成员吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        await contactsApi.batchUpdateMemberStatus(
          [row.id],
          newStatus,
          props.isAdmin
        );
        messages.success(`${newStatus === "active" ? "启用" : "禁用"}成功`);
        await loadMembers(selectedOrgKey.value, pagination.value.page);
      } catch (error) {
        messages.error(`${newStatus === "active" ? "启用" : "禁用"}失败`);
      }
    },
  });
};

// 处理部门变更
const handleChangeDepartment = (row) => {
  messages.info("功能开发中...");
};

// 处理重置密码
const handleResetPassword = (row) => {
  messages.info("功能开发中...");
};

// 处理查看详情（管理员功能）
const handleViewDetails = (row) => {
  dialog.info({
    title: "用户详情",
    content: () =>
      h("div", { style: "line-height: 1.6;" }, [
        h("p", `用户ID: ${row.id}`),
        h("p", `姓名: ${row.name}`),
        h("p", `部门: ${row.department}`),
        h("p", `手机: ${row.workMobile}`),
        h("p", `邮箱: ${row.email}`),
        h("p", `状态: ${row.status === "active" ? "正常" : "停用"}`),
        h("p", `创建时间: ${formatCreateTime(row.createTime)}`),
        h("p", `最后登录: ${getDetailedTimeInfo(row.lastLoginTime)}`),
      ]),
    positiveText: "确定",
  });
};

// 处理删除用户（管理员功能）
const handleDeleteUser = (row) => {
  dialog.warning({
    title: "危险操作",
    content: `确定要删除用户 "${row.name}" 吗？此操作不可恢复！`,
    positiveText: "确定删除",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        // 调用删除用户的API
        await contactsApi.deleteUser(row.id, props.isAdmin);
        messages.success("删除用户成功");
        // 重新加载数据
        await loadMembers(selectedOrgKey.value, pagination.value.page);
      } catch (error) {
        console.error("删除用户失败:", error);
        messages.error("删除用户失败: " + (error.message || "未知错误"));
      }
    },
  });
};

// 添加新成员相关状态
const showMemberModal = ref(false);
const memberFormRef = ref(null);
// memberLoading 已在上面定义

const memberFormModel = ref({ ...defaultMemberFormModel });

// 开通账户相关状态
const showCreateAccountModal = ref(false);

// 修改用户相关状态
const showEditUserModal = ref(false);
const editingUser = ref({});

// 成员表单规则已从配置文件导入

// 处理提交新增成员
const handleMemberSubmit = () => {
  memberFormRef.value?.validate(async (errors) => {
    if (errors) return;

    memberLoading.value = true;
    try {
      await contactsApi.createMember(memberFormModel.value);
      messages.success("添加成员成功");
      showMemberModal.value = false;
      // 重新加载成员列表
      await loadMembers(selectedOrgKey.value, pagination.value.page);
      // 重置表单
      memberFormModel.value = {
        nickname: "",
        workMobile: "",
        username: "",
        departmentId: null,
      };
    } catch (error) {
      console.error("添加成员失败:", error);
    } finally {
      memberLoading.value = false;
    }
  });
};

// 处理开通账户
const handleCreateAccount = () => {
  showCreateAccountModal.value = true;
};

// 处理开通账户提交
const handleCreateAccountSubmit = async (accountData) => {
  try {
    console.log("开通账户原始数据:", accountData);

    // 构造API请求数据
    const apiData = {
      organizationName: accountData.organizationName,
      username: accountData.username,
      nickname: accountData.nickname,
      mobile: accountData.mobile,
      selectedSku: accountData.selectedSku,
      skuInfo: accountData.skuInfo,
    };

    console.log("API请求数据:", apiData);

    // 调用开通账户的API
    await contactsApi.createAccount(apiData);

    messages.success(
      `成功为 ${accountData.organizationName} 开通 ${accountData.skuInfo.name} 服务`
    );

    // 刷新机构树和成员数据
    // 先刷新机构树，因为可能有新的机构被创建
    if (orgTreeRef.value) {
      await orgTreeRef.value.refreshTree();
    }

    // 然后刷新当前选中机构的成员列表
    if (selectedOrgKey.value) {
      await loadMembers(selectedOrgKey.value, pagination.value.page);
    }
  } catch (error) {
    console.error("开通账户失败:", error);
    messages.error("开通账户失败: " + (error.message || "未知错误"));
  }
};

// 处理编辑用户
const handleEditUser = (row) => {
  // 设置编辑的用户数据，需要包含部门ID信息
  editingUser.value = {
    id: row.id,
    name: row.name,
    email: row.email, // 这里是登录名（邮箱）
    workMobile: row.workMobile,
    department: row.department,
    // 使用用户实际的部门ID
    departmentId: row.departmentId,
  };
  showEditUserModal.value = true;
};

// 处理修改用户提交
const handleEditUserSubmit = async (submitData) => {
  try {
    console.log("修改用户原始数据:", submitData);

    // 调用修改用户的API
    await contactsApi.updateUser(
      submitData.userId,
      submitData.formData,
      props.isAdmin
    );

    messages.success("修改用户信息成功");

    // 重新加载当前选中机构的成员列表
    if (selectedOrgKey.value) {
      await loadMembers(selectedOrgKey.value, pagination.value.page);
    }
  } catch (error) {
    console.error("修改用户失败:", error);
    messages.error("修改用户失败: " + (error.message || "未知错误"));
  }
};

// 组织操作函数已移至 OrganizationTree 组件中
</script>

<template>
  <n-layout has-sider :x-gap="16">
    <!-- 左侧组织机构树 -->
    <n-layout-sider
      bordered
      :width="280"
      :native-scrollbar="true"
      class="sider"
      style="height: 100%; max-height: 100%; min-width: 280px"
    >
      <OrganizationTree
        :is-admin="props.isAdmin"
        v-model:selected-org-key="selectedOrgKey"
        @org-select="handleOrgSelect"
        @org-tree-loaded="handleOrgTreeLoaded"
        ref="orgTreeRef"
      />
    </n-layout-sider>

    <!-- 右侧成员列表 -->
    <n-layout-content class="content">
      <div class="member-list-container">
        <!-- 工具栏 -->
        <n-space align="center" justify="space-between" class="toolbar">
          <n-input
            v-model:value="searchKeyword"
            placeholder="搜索成员"
            @keyup.enter="handleSearch"
            clearable
            @clear="handleClearSearch"
            size="large"
            style="width: 300px"
          >
            <template #prefix>
              <n-icon :component="SearchOutline" />
            </template>
          </n-input>
          <n-space>
            <n-button @click="handleRefresh">
              <template #icon>
                <n-icon :component="RefreshOutline" />
              </template>
              刷新数据
            </n-button>
            <n-button
              type="warning"
              :disabled="!checkedRowKeys.length"
              @click="handleBatchDisable"
            >
              <template #icon>
                <n-icon>
                  <StopCircleOutline />
                </n-icon>
              </template>
              批量禁用
            </n-button>
            <n-button type="primary" @click="handleAddMember">
              <template #icon>
                <n-icon :component="PersonAddOutline" />
              </template>
              添加成员
            </n-button>
            <n-button type="info" @click="handleInvite">
              <template #icon>
                <n-icon>
                  <LinkOutline />
                </n-icon>
              </template>
              邀请成员
            </n-button>
            <!-- 管理员模式下显示开通账户按钮 -->
            <n-button
              v-if="props.isAdmin"
              type="success"
              @click="handleCreateAccount"
            >
              <template #icon>
                <n-icon :component="BusinessOutline" />
              </template>
              开通账户
            </n-button>
          </n-space>
        </n-space>

        <!-- 成员表格 -->
        <n-data-table
          :columns="columns"
          :data="tableData"
          :loading="memberLoading"
          :pagination="pagination"
          :bordered="false"
          remote
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
          @update:checked-row-keys="handleCheckedRowKeysChange"
          :checked-row-keys="checkedRowKeys"
          class="member-table"
          :row-key="(row) => row.id"
          :scroll-x="1200"
        />
      </div>
    </n-layout-content>

    <!-- 新增组织弹窗已移至 OrganizationTree 组件中 -->

    <!-- 开通账户弹窗 -->
    <CreateAccountModal
      v-model:show="showCreateAccountModal"
      @submit="handleCreateAccountSubmit"
    />

    <!-- 修改用户弹窗 -->
    <EditUserModal
      v-model:show="showEditUserModal"
      :user-data="editingUser"
      :is-admin="props.isAdmin"
      @submit="handleEditUserSubmit"
    />

    <!-- 新增成员弹窗 -->
    <n-modal
      v-model:show="showMemberModal"
      preset="dialog"
      title="添加成员"
      positive-text="确定"
      negative-text="取消"
      :loading="memberLoading"
      @positive-click="handleMemberSubmit"
    >
      <n-form
        ref="memberFormRef"
        :model="memberFormModel"
        :rules="memberFormRules"
        label-placement="left"
        label-width="80"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="姓名" path="nickname">
          <n-input
            v-model:value="memberFormModel.nickname"
            placeholder="请输入姓名"
          />
        </n-form-item>
        <n-form-item label="手机号" path="workMobile">
          <n-input
            v-model:value="memberFormModel.workMobile"
            placeholder="请输入手机号"
          />
        </n-form-item>
        <n-form-item label="邮箱" path="username">
          <n-input
            v-model:value="memberFormModel.username"
            placeholder="请输入邮箱"
          />
        </n-form-item>
      </n-form>
    </n-modal>
  </n-layout>
</template>

<style scoped src="./ContactsPage.scss"></style>

<!-- 所有样式已移至 ContactsPage.scss 文件 -->