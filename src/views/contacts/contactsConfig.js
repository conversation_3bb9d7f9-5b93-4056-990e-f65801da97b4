// ContactsPage 组件的配置文件

// 组织类型选项
export const orgTypeOptions = [
  {
    label: "公司",
    value: "company",
  },
  {
    label: "部门",
    value: "department",
  },
  {
    label: "小组",
    value: "group",
  },
];

// 组织表单验证规则
export const orgFormRules = {
  name: {
    required: true,
    message: "请输入组织名称",
    trigger: "blur",
  },
  type: {
    required: true,
    message: "请选择组织类型",
    trigger: "change",
  },
};

// 成员表单验证规则
export const memberFormRules = {
  nickname: {
    required: true,
    message: "请输入姓名",
    trigger: "blur",
  },
  workMobile: {
    required: true,
    message: "请输入手机号",
    trigger: "blur",
    pattern: /^1[3-9]\d{9}$/,
    validator(rule, value) {
      if (!value) return true;
      if (!/^1[3-9]\d{9}$/.test(value)) {
        return new Error("请输入正确的手机号");
      }
      return true;
    },
  },
  username: {
    required: true,
    message: "请输入邮箱",
    trigger: "blur",
    validator(rule, value) {
      if (!value) return true;
      if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
        return new Error("请输入正确的邮箱地址");
      }
      return true;
    },
  },
};

// 分页配置
export const defaultPagination = {
  page: 1,
  pageSize: 20,
  itemCount: 0,
  pageSizes: [20, 50],
  showSizePicker: true,
  showQuickJumper: false,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条`;
  },
};

// 默认组织表单模型
export const defaultOrgFormModel = {
  name: "",
  parentKey: null,
  type: "department",
};

// 默认成员表单模型
export const defaultMemberFormModel = {
  nickname: "",
  workMobile: "",
  username: "",
  departmentId: null,
};

// 表格基础列配置
export const getBaseColumns = () => [
  {
    type: "selection",
    fixed: "left",
    align: "center",
    width: 50,
  },
  {
    title: "姓名",
    key: "name",
    align: "left",
    ellipsis: true,
    minWidth: 100,
  },
  {
    title: "部门",
    key: "department",
    align: "left",
    ellipsis: true,
    minWidth: 150,
  },
  {
    title: "手机",
    key: "workMobile",
    align: "left",
    ellipsis: true,
    minWidth: 120,
  },
  {
    title: "邮箱",
    key: "email",
    align: "left",
    ellipsis: true,
    minWidth: 180,
  },
];

// 管理员专用列配置
export const getAdminColumns = () => [
  {
    title: "用户ID",
    key: "id",
    align: "center",
    width: 80,
  },
  {
    title: "创建时间",
    key: "createTime",
    align: "center",
    width: 160,
    render(row) {
      return row.createTime ? row.createTime : "-";
    },
  },
  {
    title: "最后登录",
    key: "lastLoginTime",
    align: "center",
    width: 160,
    render(row) {
      return row.lastLoginTime ? row.lastLoginTime : "从未登录";
    },
  },
];

// 基础操作选项
export const getBaseActionOptions = (row) => [
  {
    label: row.status === "active" ? "禁用" : "启用",
    key: row.status === "active" ? "disable" : "enable",
    props: {
      type: row.status === "active" ? "error" : "success",
    },
  },
  {
    label: "变更部门",
    key: "change-department",
  },
  {
    label: "重置密码",
    key: "reset-password",
  },
];

// 管理员专用操作选项
export const getAdminActionOptions = () => [
  {
    type: "divider"
  },
  {
    label: "查看详情",
    key: "view-details",
  },
  {
    label: "删除用户",
    key: "delete-user",
    props: {
      type: "error"
    },
  },
];

// 组织树节点操作选项
export const getOrgNodeActions = () => [
  {
    label: "新增下级",
    key: "add-child",
  },
  {
    label: "重命名",
    key: "rename",
  },
  {
    label: "删除",
    key: "delete",
  },
];
