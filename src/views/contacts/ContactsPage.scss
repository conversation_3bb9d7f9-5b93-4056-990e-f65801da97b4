.sider {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 机构树容器样式已移至OrganizationTree.vue组件中 */

.content {
  height: 100%;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  border-radius: 0 8px 8px 0;
}

.member-list-container {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  height: 95%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.toolbar {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.member-table {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.n-data-table-wrapper) {
  flex: 1;
  overflow: auto;
}

/* 机构树滚动条样式已移至OrganizationTree.vue组件中 */

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

/* 批量操作按钮样式 */
.batch-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 搜索框样式 */
.search-input {
  width: 240px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 树节点相关样式已移至OrganizationTree.vue组件中 */

/* 确保侧边栏容器高度正确 */
.sider {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.n-layout-sider-border) {
  border-radius: 8px 0 0 8px;
}

/* 树节点样式统一在OrganizationTree.vue中定义，此处不重复定义 */

/* 优化行的样式 */
:deep(.n-data-table-tr) {
  height: 48px;
}

:deep(.n-data-table-td) {
  padding: 8px 12px !important;
}

/* 优化选中行的样式 */
:deep(.n-data-table-tr--checked) {
  background-color: rgba(24, 160, 88, 0.1) !important;
}

:deep(.n-data-table-tr--checked:hover) {
  background-color: rgba(24, 160, 88, 0.15) !important;
}

/* 添加批量操作按钮的过渡效果 */
.toolbar {
  position: relative;
}

.n-button {
  transition: all 0.3s;
}

/* 添加按钮间距样式 */
.n-space .n-button {
  margin-left: 12px;
}

/* 添加表格操作列样式 */
:deep(.table-actions) {
  display: flex;
  justify-content: center;
  gap: 4px;
}

:deep(.table-actions .n-button) {
  width: 28px;
  height: 28px;
  padding: 0;
}

:deep(.table-actions .n-button:hover) {
  background-color: rgba(0, 0, 0, 0.06);
}

/* 树节点样式已移至OrganizationTree.vue组件中 */

/* 树节点样式统一在OrganizationTree.vue中定义，此处不重复定义 */

/* 选中状态样式已在上方定义，此处删除重复定义 */

/* 表单样式 */
.form-container {
  padding: 16px 0;
}

/* 模态框样式 */
.modal-content {
  max-width: 500px;
}

/* 状态标签样式 */
.status-tag {
  min-width: 60px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sider {
    width: 100% !important;
    min-width: 100% !important;
  }

  .search-input {
    width: 100%;
  }

  .batch-actions {
    flex-wrap: wrap;
  }

  .table-actions {
    gap: 2px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
}

.empty-container .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-container .empty-text {
  font-size: 14px;
}

/* 工具栏样式 */
.toolbar-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
}

/* 表格行样式 */
:deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: #f5f5f5;
}

/* 选中行样式 */
:deep(.n-data-table-tbody .n-data-table-tr--checked) {
  background-color: #e6f7ff;
}

/* 固定列阴影 */
:deep(.n-data-table-td--fixed-left) {
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

:deep(.n-data-table-td--fixed-right) {
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

/* 表格头部样式 */
:deep(.n-data-table-thead .n-data-table-th) {
  background-color: #fafafa;
  font-weight: 600;
}

/* 分页器样式 */
:deep(.n-pagination) {
  justify-content: flex-end;
}

/* 树节点图标样式已移至OrganizationTree.vue组件中 */

/* 表单项样式 */
:deep(.n-form-item-label) {
  font-weight: 500;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 8px;
}

/* 卡片样式 */
.info-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

/* 标题样式 */
.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

/* 统计信息样式 */
.stats-container {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}