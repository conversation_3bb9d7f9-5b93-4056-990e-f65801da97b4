<template>
  <div class="video-chat-container">
    <!-- 标题栏 -->
    <div class="chat-header">
      <div class="header-title">视频问答助手</div>
      <div class="header-actions">
        <n-button @click="clearMessages" size="small" quaternary>
          清空记录
        </n-button>
        <n-button @click="resetForm" size="small" quaternary>
          重置表单
        </n-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="chat-main">
      <!-- 左侧问题设置区域 (30%) -->
      <div class="left-panel">
        <div class="panel-title">问题设置</div>

        <!-- 视频文件上传 -->
        <div class="form-section">
          <div class="section-label">视频文件</div>
          <div class="upload-area">
            <n-upload
              v-if="!videoFile"
              accept="video/*"
              :show-file-list="false"
              @change="handleVideoUpload"
              :disabled="isUploading || isProcessing"
            >
              <div class="upload-trigger">
                <n-icon size="48" class="upload-icon">
                  <videocam-outline />
                </n-icon>
                <p class="upload-text">点击上传视频文件</p>
                <p class="upload-hint">支持 MP4、WebM 等格式，最大 1GB</p>
              </div>
            </n-upload>

            <!-- 视频预览 -->
            <div v-else class="video-preview">
              <video
                :src="videoUrl"
                controls
                class="preview-video"
                preload="metadata"
              ></video>
              <div class="video-info">
                <span class="file-name">{{ videoFile.name }}</span>
                <span class="file-size"
                  >{{ Math.round(videoFile.size / 1024 / 1024) }}MB</span
                >
              </div>
              <n-button
                @click="removeVideo"
                size="small"
                type="error"
                quaternary
                class="remove-btn"
                :disabled="isUploading || isProcessing"
              >
                <template #icon>
                  <n-icon><trash-outline /></n-icon>
                </template>
                移除视频
              </n-button>
            </div>
          </div>
        </div>

        <!-- 时分秒输入框 -->
        <div class="form-section">
          <div class="section-label">时间点</div>
          <div class="time-inputs">
            <div class="time-input-group">
              <div class="time-label">时</div>
              <n-input-number
                v-model:value="hours"
                :min="0"
                :max="23"
                :disabled="isUploading || isProcessing"
                class="time-number-input"
                button-placement="both"
                placeholder="0"
              />
            </div>
            <span class="time-separator">:</span>
            <div class="time-input-group">
              <div class="time-label">分</div>
              <n-input-number
                v-model:value="minutes"
                :min="0"
                :max="59"
                :disabled="isUploading || isProcessing"
                class="time-number-input"
                button-placement="both"
                placeholder="0"
              />
            </div>
            <span class="time-separator">:</span>
            <div class="time-input-group">
              <div class="time-label">秒</div>
              <n-input-number
                v-model:value="seconds"
                :min="0"
                :max="59"
                :disabled="isUploading || isProcessing"
                button-placement="both"
                class="time-number-input"
                placeholder="0"
              />
            </div>
          </div>
          <div class="input-hint">请输入视频中的具体时间点</div>
        </div>

        <!-- 问题输入框 -->
        <div class="form-section">
          <div class="section-label">问题内容</div>
          <n-input
            v-model:value="question"
            type="textarea"
            placeholder="请输入您想要询问的问题..."
            :autosize="{ minRows: 4, maxRows: 8 }"
            :disabled="isUploading || isProcessing"
            :maxlength="500"
            show-count
            class="question-input"
          />
        </div>

        <!-- 提交按钮 -->
        <div class="form-section">
          <n-button
            @click="submitVideoChat"
            type="primary"
            size="large"
            block
            :loading="isUploading || isProcessing"
            :disabled="!canSubmit"
            class="submit-btn"
          >
            <template #icon>
              <n-icon><send-outline /></n-icon>
            </template>
            {{ uploadProgress || "提交问答" }}
          </n-button>

          <!-- 重试按钮 -->
          <n-button
            v-if="isTimeout"
            @click="retryPolling"
            type="warning"
            size="large"
            block
            class="retry-btn"
          >
            <template #icon>
              <n-icon><refresh-outline /></n-icon>
            </template>
            重试获取结果
          </n-button>

          <div
            class="submit-hint"
            v-if="!canSubmit && !isUploading && !isProcessing && !isTimeout"
          >
            请完整填写视频文件、时间点和问题内容
          </div>

          <div class="timeout-hint" v-if="isTimeout">
            问答超时，请点击重试按钮重新获取结果
          </div>
        </div>
      </div>

      <!-- 右侧结果展示区域 (70%) -->
      <div class="right-panel">
        <div class="panel-title">对话记录</div>

        <div class="messages-container">
          <div class="messages-wrapper">
            <div
              v-for="(message, index) in messages"
              :key="index"
              class="message-row"
              :class="[message.type, { 'has-video': message.isVideo }]"
            >
              <!-- 视频消息 -->
              <div
                v-if="message.isVideo"
                class="message-bubble video-bubble"
                :class="message.type"
              >
                <div class="video-message-content">
                  <div class="video-thumbnail">
                    <video
                      :src="message.videoUrl"
                      class="thumbnail-video"
                      preload="metadata"
                    ></video>
                  </div>
                  <div class="video-details">
                    <div class="question-text">{{ message.question }}</div>
                    <div class="video-meta">
                      <span class="timestamp-tag"
                        >时间点: {{ message.timestamp }}</span
                      >
                      <span class="duration-tag"
                        >时长: {{ Math.round(message.duration) }}s</span
                      >
                      <span class="size-tag">大小: {{ message.size }}MB</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 文本消息 -->
              <div
                v-else
                class="message-bubble text-bubble"
                :class="message.type"
              >
                <div class="text-content">{{ message.content }}</div>
                <div class="message-time">{{ message.timestamp }}</div>
              </div>
            </div>

            <n-empty v-if="messages.length === 0" description="暂无对话记录" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  NButton,
  NUpload,
  NInput,
  NIcon,
  NEmpty,
  NInputNumber,
} from "naive-ui";
import {
  VideocamOutline,
  TrashOutline,
  TimeOutline,
  SendOutline,
  RefreshOutline,
} from "@vicons/ionicons5";
import { useVideoChat } from "@/composables/useVideoChat.js";

// 使用视频聊天组合式函数
const {
  // 响应式数据
  isUploading,
  isProcessing,
  messages,
  videoFile,
  videoUrl,
  hours,
  minutes,
  seconds,
  question,
  isPolling,
  isTimeout,

  // 计算属性
  canSubmit,
  uploadProgress,
  formattedTimestamp,

  // 方法
  handleVideoUpload,
  removeVideo,
  submitVideoTask,
  submitVideoChat,
  retryPolling,
  clearMessages,
  resetForm,
  mockSubmit,
} = useVideoChat();
</script>

<style scoped>
.video-chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 标题栏 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 主要内容区域 */
.chat-main {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

/* 左侧面板 (30%) */
.left-panel {
  width: 30%;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  transition: border-color 0.3s;
  min-height: 160px;
}

.upload-area:hover {
  border-color: #18a058;
}

/* 确保 n-upload 组件内容居中 */
.upload-area :deep(.n-upload) {
  width: 100%;
  height: 100%;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.upload-area :deep(.n-upload-dragger) {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.upload-area :deep(.n-upload-trigger) {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.upload-trigger {
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.upload-icon {
  color: #999;
  margin-bottom: 12px;
  display: block;
}

.upload-text {
  font-size: 16px;
  color: #333;
  margin: 0 0 8px 0;
  text-align: center;
}

.upload-hint {
  font-size: 12px;
  color: #999;
  margin: 0;
  text-align: center;
}

/* 视频预览 */
.video-preview {
  padding: 16px;
}

.preview-video {
  width: 100%;
  max-height: 200px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #666;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.remove-btn {
  width: 100%;
}

/* 输入框 */
.question-input {
  width: 100%;
}

/* 时间输入框 */
.time-inputs {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.time-input-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 0 0 auto;
  min-width: 0;
}

.time-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  white-space: nowrap;
  text-align: center;
}

.time-number-input {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}

.time-number-input :deep(.n-input-number-input) {
  text-align: center;
}

.time-number-input :deep(.n-input__input-el) {
  text-align: center !important;
}

.time-number-input :deep(.n-input-number-input-wrapper) {
  text-align: center;
}

.time-number-input :deep(.n-input__input) {
  text-align: center;
}

.time-separator {
  font-size: 16px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
  flex-shrink: 0;
  line-height: 1;
  user-select: none;
}

.input-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 提交按钮 */
.submit-btn {
  margin-bottom: 8px;
}

.retry-btn {
  margin-top: 8px;
  margin-bottom: 8px;
}

.submit-hint {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.timeout-hint {
  font-size: 12px;
  color: #f56c6c;
  text-align: center;
  margin-top: 8px;
}

/* 右侧面板 (70%) */
.right-panel {
  width: 70%;
  min-width: 600px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-wrapper {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
  display: flex;
  flex-direction: column;
}

/* 消息样式 */
.message-row {
  margin-bottom: 16px;
  display: flex;
  width: 100%;
}

.message-row:last-child {
  margin-bottom: 0;
}

.message-row.user {
  justify-content: flex-start;
}

.message-row.server {
  justify-content: flex-end;
}

.message-row.system {
  justify-content: center;
}

.message-bubble {
  max-width: 80%;
  min-width: 120px;
  padding: 12px 16px;
  border-radius: 12px;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 用户消息 */
.message-bubble.user {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #e0e0e0;
  text-align: left;
  align-self: flex-start;
}

/* 服务器消息 */
.message-bubble.server {
  background: #18a058;
  color: white;
  text-align: left;
  align-self: flex-end;
}

/* 系统消息 */
.message-bubble.system {
  background: #e8f4fd;
  color: #666;
  font-size: 12px;
  max-width: 60%;
  text-align: center;
}

/* 视频消息特殊样式 */
.video-bubble {
  max-width: 90%;
  min-width: 300px;
}

.video-message-content {
  display: flex;
  gap: 12px;
  width: 100%;
}

.video-thumbnail {
  flex-shrink: 0;
  width: 120px;
}

.thumbnail-video {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.video-details {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.question-text {
  font-weight: 500;
  margin-bottom: 8px;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  text-align: left;
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 11px;
  opacity: 0.8;
}

.timestamp-tag,
.duration-tag,
.size-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

/* 文本消息 */
.text-content {
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  text-align: left;
  width: 100%;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
  white-space: nowrap;
}

/* 滚动条样式 */
.messages-wrapper::-webkit-scrollbar {
  width: 6px;
}

.messages-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.left-panel::-webkit-scrollbar {
  width: 6px;
}

.left-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 - 仅支持13寸以上设备 */
@media (max-width: 1200px) {
  .left-panel {
    width: 35%;
  }

  .right-panel {
    width: 65%;
    min-width: 500px;
  }
}

/* 动画效果 */
.message-row {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.upload-trigger:hover .upload-icon {
  color: #18a058;
  transform: scale(1.1);
  transition: all 0.3s ease;
}

/* 禁用状态 */
.upload-trigger[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-trigger[disabled]:hover .upload-icon {
  transform: none;
  color: #999;
}
</style>