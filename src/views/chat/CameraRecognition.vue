<template>
  <div class="camera-recognition-container">
    <!-- 标题栏 -->
    <div class="header">
      <h1 class="title">摄像头识别系统</h1>
      <p class="subtitle">实时视频流识别与推送</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 视频显示区域 -->
      <div class="video-section">
        <!-- 本地摄像头区域 -->
        <div class="video-container local-video">
          <div class="video-title">本地摄像头</div>
          <div v-if="!isStreaming" class="video-placeholder">
            <n-icon size="80" class="camera-icon">
              <videocam-outline />
            </n-icon>
            <p class="placeholder-text">摄像头未开启</p>
          </div>
          <video
            ref="videoElement"
            class="video-display"
            :class="{ hidden: !isStreaming }"
            autoplay
            muted
            playsinline
          ></video>

          <!-- 状态指示器 -->
          <div v-if="isStreaming" class="status-indicator">
            <div class="status-dot" :class="{ active: isRecognizing }"></div>
            <span class="status-text">
              {{ isRecognizing ? "采集中" : "已连接" }}
            </span>
          </div>
        </div>

        <!-- 输出流区域 -->
        <div class="video-container output-video">
          <div class="video-title">输出流播放</div>
          <div v-if="!isPlaying" class="video-placeholder">
            <n-icon size="80" class="camera-icon">
              <PlayCircleOutline />
            </n-icon>
            <p class="placeholder-text">
              {{ isRecognizing ? "等待流数据..." : "未开始推流" }}
            </p>
          </div>
          <video
            ref="outputVideoElement"
            class="video-display"
            :class="{ hidden: !isPlaying }"
            autoplay
            playsinline
          ></video>

          <!-- 播放状态指示器 -->
          <div v-if="isPlaying" class="status-indicator">
            <div class="status-dot active"></div>
            <span class="status-text">播放中</span>
          </div>

          <!-- 流地址显示 -->
          <div v-if="playbackUrl" class="stream-url">
            <div class="url-label">订阅地址:</div>
            <div class="url-container">
              <div class="url-text">{{ playbackUrl }}</div>
              <n-button
                size="small"
                type="primary"
                ghost
                class="copy-button"
                @click="copyToClipboard(playbackUrl)"
                title="复制地址"
              >
                <template #icon>
                  <n-icon><CopyOutline /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制按钮区域 -->
      <div class="controls-section">
        <div class="button-group">
          <!-- 开启摄像头按钮 -->
          <n-button
            v-if="!isStreaming"
            @click="startCamera"
            type="primary"
            size="large"
            :loading="isStarting"
            class="control-button"
          >
            <template #icon>
              <n-icon><VideocamOutline /></n-icon>
            </template>
            {{ isStarting ? "正在开启摄像头..." : "开启摄像头" }}
          </n-button>

          <!-- 关闭摄像头按钮 -->
          <n-button
            v-if="isStreaming && !isRecognizing"
            @click="stopCamera"
            type="error"
            size="large"
            class="control-button"
          >
            <template #icon>
              <n-icon><VideocamOffOutline /></n-icon>
            </template>
            关闭摄像头
          </n-button>

          <!-- 开始识别按钮 -->
          <n-button
            v-if="isStreaming && !isRecognizing"
            @click="startRecognition"
            type="success"
            size="large"
            :loading="isStartingRecognition"
            class="control-button"
          >
            <template #icon>
              <n-icon><PlayOutline /></n-icon>
            </template>
            {{ isStartingRecognition ? "正在连接服务器..." : "开始识别" }}
          </n-button>

          <!-- 停止识别按钮 -->
          <n-button
            v-if="isRecognizing"
            @click="stopRecognition"
            type="warning"
            size="large"
            class="control-button"
          >
            <template #icon>
              <n-icon><StopOutline /></n-icon>
            </template>
            停止识别
          </n-button>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
          <div v-if="errorMessage" class="error-message">
            <n-icon class="error-icon"><AlertCircleOutline /></n-icon>
            {{ errorMessage }}
          </div>
          <div v-else-if="statusMessage" class="status-message">
            {{ statusMessage }}
          </div>
        </div>

        <!-- 流信息 -->
        <div v-if="streamInfo" class="stream-info">
          <div class="info-item">
            <span class="info-label">播放地址:</span>
            <div class="info-value-with-copy">
              <span class="info-value url-value">{{ streamInfo.url }}</span>
              <n-button
                size="tiny"
                type="primary"
                ghost
                class="info-copy-button"
                @click="copyToClipboard(streamInfo.url)"
                title="复制播放地址"
              >
                <template #icon>
                  <n-icon size="14"><CopyOutline /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
          <div class="info-item">
            <span class="info-label">分辨率:</span>
            <span class="info-value">{{ streamInfo.resolution }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">状态:</span>
            <span class="info-value" :class="streamInfo.status">{{
              streamInfo.statusText
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { NButton, NIcon, useMessage } from "naive-ui";
import {
  VideocamOutline,
  VideocamOffOutline,
  PlayOutline,
  StopOutline,
  AlertCircleOutline,
  PlayCircleOutline,
  CopyOutline,
} from "@vicons/ionicons5";
import { useCamera } from "@/composables/useCamera.js";

const message = useMessage();

// 复制到剪贴板功能
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success("地址已复制到剪贴板");
  } catch (err) {
    // 如果现代API不可用，使用传统方法
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      message.success("地址已复制到剪贴板");
    } catch (fallbackErr) {
      message.error("复制失败，请手动复制");
    }
    document.body.removeChild(textArea);
  }
};

// 创建视频元素的引用
const videoElement = ref(null);
const outputVideoElement = ref(null);

// 输出流相关状态
const isPlaying = ref(false);
const playbackUrl = ref("");

// 使用摄像头组合式函数，传入视频元素引用
const {
  // 响应式数据
  isStreaming,
  isStarting,
  isRecognizing,
  isStartingRecognition,
  errorMessage,
  statusMessage,
  streamInfo,

  // 方法
  startCamera,
  stopCamera,
  startRecognition,
  stopRecognition,
  cleanup,

  // 获取当前推流URL用于播放
  getCurrentStreamUrl,
} = useCamera(videoElement, outputVideoElement, isPlaying, playbackUrl);

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
});

// 组件卸载时清理资源
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.camera-recognition-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

/* 标题栏 */
.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  width: 100%;
  max-width: 1200px; /* 增加最大宽度以容纳横向排列的两个视频 */
}

/* 视频显示区域 */
.video-section {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: nowrap; /* 防止换行，保持横向排列 */
}

.video-container {
  position: relative;
  width: 400px;
  height: 450px; /* 增加高度以容纳标题和流地址 */
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: #000;
  display: flex;
  flex-direction: column;
}

.video-title {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  padding: 12px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.local-video .video-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.output-video .video-title {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.video-placeholder {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  margin: 0;
}

.camera-icon {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 16px;
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  margin: 0;
  font-weight: 500;
  text-align: center;
  padding: 0 20px;
}

.video-display {
  flex: 1;
  width: 100%;
  object-fit: cover;
}

.video-display.hidden {
  display: none;
}

/* 流地址显示 */
.stream-url {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(20, 20, 20, 0.95) 100%
  );
  color: white;
  padding: 16px;
  font-size: 13px;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 0 0 20px 20px; /* 底部圆角与容器匹配 */
}

.url-label {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.url-label::before {
  content: "🔗";
  font-size: 16px;
}

.url-container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.url-text {
  color: #00ff88;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  word-break: break-all;
  line-height: 1.5;
  flex: 1;
  padding: 8px 10px;
  background: rgba(0, 255, 136, 0.08);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 136, 0.25);
  white-space: pre-wrap;
  overflow-wrap: break-word;
  font-size: 12px;
  min-height: 20px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.copy-button {
  flex-shrink: 0;
  min-width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  background: linear-gradient(
    135deg,
    rgba(103, 126, 234, 0.25) 0%,
    rgba(118, 75, 162, 0.25) 100%
  ) !important;
  border: 1px solid rgba(103, 126, 234, 0.4) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: linear-gradient(
    135deg,
    rgba(103, 126, 234, 0.4) 0%,
    rgba(118, 75, 162, 0.4) 100%
  ) !important;
  border-color: rgba(103, 126, 234, 0.6) !important;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.copy-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 25px; /* title区域的中心位置 (12px padding + 8px 约等于title高度的一半) */
  right: 16px;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px; /* 减小padding以适应title区域 */
  border-radius: 12px; /* 减小圆角 */
  backdrop-filter: blur(10px);
  z-index: 10; /* 确保在最上层 */
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
  transition: background-color 0.3s;
}

.status-dot.active {
  background: #ff4757;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-text {
  color: white;
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
}

/* 控制按钮区域 */
.controls-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
}

.button-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.control-button {
  min-width: 160px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.control-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 状态信息 */
.status-info {
  text-align: center;
  min-height: 24px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
  font-weight: 500;
}

.error-icon {
  flex-shrink: 0;
}

.status-message {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

/* 推流信息 */
.stream-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 2400px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-value {
  color: white;
  font-weight: 600;
  text-align: right;
  max-width: 1200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 带复制按钮的信息值容器 */
.info-value-with-copy {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

/* URL值样式 - 允许完整显示 */
.url-value {
  color: #00ff88 !important;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  word-break: break-all !important;
  white-space: pre-wrap !important;
  text-overflow: unset !important;
  overflow: visible !important;
  max-width: none !important;
  flex: 1;
  line-height: 1.4;
}

/* 信息区域的复制按钮 */
.info-copy-button {
  flex-shrink: 0;
  min-width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  border-radius: 4px !important;
  background: transparent !important;
  border: none !important;
  transition: all 0.2s ease;
}

.info-copy-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.info-value.connected {
  color: #2ed573;
}

.info-value.connecting {
  color: #ffa502;
}

.info-value.error {
  color: #ff4757;
}

/* 响应式设计 */
@media (min-width: 1025px) {
  .video-section {
    justify-content: space-between; /* 在大屏幕上均匀分布两个视频 */
  }

  .video-container {
    width: 450px; /* 在大屏幕上稍微增加视频容器宽度 */
    height: 500px; /* 相应增加高度 */
  }
}

@media (max-width: 1024px) {
  .main-content {
    max-width: 600px; /* 在中等屏幕上恢复较小的最大宽度 */
  }

  .video-section {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .camera-recognition-container {
    padding: 16px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 14px;
  }

  .video-container {
    width: 350px;
    height: 400px;
  }

  .control-button {
    min-width: 140px;
    height: 44px;
    font-size: 14px;
  }

  .button-group {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .video-container {
    width: 300px;
    height: 350px;
  }

  .button-group {
    flex-direction: column;
    width: 100%;
  }

  .control-button {
    width: 100%;
    max-width: 300px;
  }

  .url-text {
    font-size: 11px;
    padding: 6px 8px;
  }

  .url-container {
    flex-direction: column;
    gap: 8px;
    padding: 10px;
  }

  .copy-button {
    align-self: flex-end;
    min-width: 32px !important;
    height: 32px !important;
  }

  .url-label {
    font-size: 13px;
  }

  .stream-url {
    padding: 12px;
  }

  /* 小屏幕上的推流地址样式 */
  .info-value-with-copy {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .url-value {
    font-size: 10px !important;
  }

  .info-copy-button {
    align-self: flex-end;
    min-width: 20px !important;
    height: 20px !important;
  }
}
</style>
