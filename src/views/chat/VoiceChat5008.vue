<template>
  <div class="wechat-container">
    <!-- 标题栏 -->
    <div class="chat-header">
      <div class="header-title">机器狗5008</div>
      <div class="header-status">
        <div
          class="status-dot"
          :class="{ connected: connectionStatus === 'connected' }"
        ></div>
        <span class="status-text">{{
          connectionStatus === "connected" ? "在线" : "离线"
        }}</span>
        <n-button
          v-if="connectionStatus !== 'connected'"
          @click="connect"
          type="primary"
          size="tiny"
          class="connect-btn"
        >
          连接
        </n-button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-content">
      <div class="messages-wrapper">
        <div
          v-for="(message, index) in filteredMessages"
          :key="index"
          class="message-row"
          :class="[message.type, { 'has-voice': message.isVoice }]"
        >
          <!-- 语音消息 -->
          <div
            v-if="message.isVoice"
            class="message-bubble voice-bubble"
            :class="[
              message.type,
              { 'has-image': message.type === 'user' && message.hasImage },
            ]"
          >
            <!-- 图片预览（仅用户消息且有图片时显示） -->
            <div
              v-if="message.type === 'user' && message.hasImage"
              class="image-preview"
            >
              <img
                :src="message.imageUrl"
                :alt="message.imageName"
                class="preview-image"
                @click.stop="
                  openImagePreview(message.imageUrl, message.imageName)
                "
              />
              <div class="image-info">
                <span class="image-name">{{ message.imageName }}</span>
                <span class="image-size">{{ message.imageSize }}KB</span>
              </div>
            </div>

            <div
              class="voice-content"
              @click="
                message.type === 'user'
                  ? playUserAudio(message.audioUrl, message.id)
                  : playServerAudio(message.audioUrl, message.id)
              "
            >
              <div
                class="voice-icon"
                :class="{ playing: playingAudioId === message.id }"
              >
                <!-- 播放图标 -->
                <svg
                  v-if="playingAudioId !== message.id"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M8 5v14l11-7z" />
                </svg>
                <!-- 播放中图标 -->
                <svg
                  v-else
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  class="playing-icon"
                >
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                </svg>
              </div>
              <div class="voice-info">
                <!-- 只有服务器消息显示时长和大小 -->
                <div v-if="message.type === 'server'" class="voice-duration">
                  {{ Math.round(message.duration || 0) }}"
                </div>
                <div
                  v-if="message.type === 'server' && message.size"
                  class="voice-size"
                >
                  {{ message.size }}KB
                </div>
              </div>
            </div>
            <div class="bubble-time">{{ formatTime(message.timestamp) }}</div>

            <!-- 语音转文字显示 -->
            <div
              v-if="message.transcription"
              class="voice-transcription"
              :class="message.type"
            >
              {{ message.transcription }}
            </div>
          </div>

          <!-- 文本消息 -->
          <div v-else class="message-bubble" :class="message.type">
            <!-- Loading状态 -->
            <div v-if="message.isLoading" class="loading-content">
              <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
              <span class="loading-text" v-html="message.content"></span>
            </div>
            <!-- 普通文本 -->
            <div v-else class="text-message-content">
              <div class="bubble-content" v-html="message.content"></div>
              <div class="bubble-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>
        </div>

        <!-- 录音状态提示 -->
        <div v-if="isRecording" class="recording-indicator">
          <div class="recording-animation">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
          </div>
          <span class="recording-text">{{ getStatusText() }}</span>
          <span v-if="recordingDuration > 0" class="recording-time"
            >{{ recordingDuration }}s</span
          >
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="chat-input">
      <div class="input-wrapper">
        <div class="voice-controls">
          <!-- 图片上传按钮 -->
          <button
            @click="triggerImageUpload"
            :disabled="isUploading"
            class="image-btn"
            :class="{ uploading: isUploading, uploaded: imageSelected }"
            title="上传图片"
          >
            <svg
              v-if="!isUploading && !imageSelected"
              class="image-icon"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"
              />
            </svg>
            <svg
              v-else-if="isUploading"
              class="loading-icon"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
            </svg>
            <svg
              v-else
              class="check-icon"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
              />
            </svg>
          </button>

          <!-- 隐藏的文件输入 -->
          <input
            ref="imageInput"
            type="file"
            accept="image/*"
            @change="handleImageSelect"
            style="display: none"
          />

          <button
            @click="toggleRecording"
            :disabled="!canStartRecordingWithImage && !isRecording"
            class="voice-btn"
            :class="{
              recording: isRecording,
              disabled: !canStartRecordingWithImage && !isRecording,
            }"
          >
            <svg class="mic-icon" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"
              />
              <path
                d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"
              />
            </svg>
            <span class="voice-text">{{
              isRecording ? "点击结束" : "点击说话"
            }}</span>
          </button>

          <button
            v-if="isProcessing"
            @click="stopTaskPolling"
            class="stop-btn"
            title="停止等待更多消息"
          >
            <svg class="stop-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M6 6h12v12H6z" />
            </svg>
          </button>

          <button @click="clearMessages" class="clear-btn">
            <svg class="clear-icon" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { NButton, useMessage } from "naive-ui";
import { useVoiceChatCOS } from "@/composables/useVoiceChatCOS.js";
import { validateImageSize } from "@/utils/audioUpload.js";

// 使用COS上传的语音聊天组合式函数，传入conn_id=5008
const {
  // 响应式数据
  connectionStatus,
  isRecording,
  messages,
  isProcessing,
  playingAudioId,

  // 计算属性
  canStartRecording,
  recordingDuration,

  // 方法
  connect,
  toggleRecording,
  stopTaskPolling,
  clearMessages,
  getStatusText,
  formatTime,
  playUserAudio,
  playServerAudio,
  clearImageKey,
  setImageFile,
} = useVoiceChatCOS({ connId: "5008" });

// 图片上传相关状态
const imageInput = ref(null);
const isUploading = ref(false);
const imageSelected = ref(false); // 改为图片选择状态
const selectedImageFile = ref(null); // 保存选择的图片文件对象
const selectedImageInfo = ref(null); // 保存选择的图片信息

const message = useMessage();

// 计算属性：只有在图片选择后才能开始录音
const canStartRecordingWithImage = computed(() => {
  return canStartRecording.value && imageSelected.value;
});

// 过滤消息，只显示用户和服务器的消息，不显示调试信息
const filteredMessages = computed(() => {
  return messages.value.filter(
    (message) => message.type === "user" || message.type === "server"
  );
});

// 触发图片选择
const triggerImageUpload = () => {
  // 如果已经选择了图片，先清空状态以便重新选择
  if (imageSelected.value) {
    imageSelected.value = false;
    selectedImageFile.value = null;
    selectedImageInfo.value = null;
    clearImageKey(); // 这会同时清空 currentImageKey 和 currentImageFile
  }

  if (imageInput.value) {
    imageInput.value.click();
  }
};

// 处理图片选择（不立即上传）
const handleImageSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 检查文件类型
  if (!file.type.startsWith("image/")) {
    message.error("请选择图片文件");
    return;
  }

  // 检查文件大小（5MB限制）
  if (!validateImageSize(file)) {
    message.error("图片大小不能超过5MB");
    return;
  }

  // 保存选择的图片文件，等待语音录制完成后一起上传
  selectedImageFile.value = file;
  selectedImageInfo.value = {
    name: file.name,
    size: file.size,
    type: file.type,
    url: URL.createObjectURL(file), // 临时URL用于预览
  };
  imageSelected.value = true;

  // 设置图片文件到语音聊天组合式函数中
  setImageFile(file);

  message.success("图片选择成功，录制语音后将一起上传");

  // 清空input值，允许重新选择同一文件
  if (imageInput.value) {
    imageInput.value.value = "";
  }
};

// 打开图片预览
const openImagePreview = (imageUrl, imageName) => {
  // 创建一个新窗口来显示图片
  const newWindow = window.open("", "_blank");
  if (newWindow) {
    newWindow.document.write(`
      <html>
        <head>
          <title>图片预览 - ${imageName}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              background: #000;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              font-family: Arial, sans-serif;
            }
            img {
              max-width: 100%;
              max-height: 100vh;
              object-fit: contain;
              border-radius: 8px;
              box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
            }
            .info {
              position: fixed;
              top: 20px;
              left: 20px;
              color: white;
              background: rgba(0, 0, 0, 0.7);
              padding: 10px 15px;
              border-radius: 6px;
              font-size: 14px;
            }
          </style>
        </head>
        <body>
          <div class="info">${imageName}</div>
          <img src="${imageUrl}" alt="${imageName}" />
        </body>
      </html>
    `);
    newWindow.document.close();
  }
};

// 监听处理状态变化，当开始处理时重置图片选择状态
watch(isProcessing, (newValue) => {
  if (newValue) {
    // 任务开始处理时，重置图片选择状态，为下一次对话做准备
    imageSelected.value = false;
    selectedImageFile.value = null;
    selectedImageInfo.value = null;
  }
});
</script>

<style scoped>
/* 微信风格聊天容器 */
.wechat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 800px;
  max-width: 400px;
  margin: 0 auto;
  background: #f7f7f7;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 标题栏 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4757;
  transition: background-color 0.3s ease;
}

.status-dot.connected {
  background: #2ed573;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.connect-btn {
  margin-left: 8px;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.messages-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 16px; /* 移除额外的底部空间 */
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 消息行 */
.message-row {
  display: flex;
  width: 100%;
  position: relative;
  margin-bottom: 12px; /* 基础间距 */
}

/* 包含语音消息的行需要更多间距 */
.message-row:has(.voice-bubble) {
  margin-bottom: 20px; /* 为语音转文字留出更多空间 */
}

/* 如果浏览器不支持:has选择器，使用类名方式 */
.message-row.has-voice {
  margin-bottom: 20px;
}

.message-row.user {
  justify-content: flex-end;
}

.message-row.server {
  justify-content: flex-start;
}

/* 消息气泡 */
.message-bubble {
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
}

.message-bubble.user {
  background: #95ec69;
  color: #1a1a1a;
  border-bottom-right-radius: 6px;
}

.message-bubble.server {
  background: #ffffff;
  color: #1a1a1a;
  border-bottom-left-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 文本消息内容 */
.text-message-content {
  position: relative;
  padding-bottom: 16px; /* 为时间留出空间 */
}

.bubble-content {
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 0;
}

.bubble-time {
  position: absolute;
  bottom: 2px;
  font-size: 10px;
  color: rgba(0, 0, 0, 0.4);
  text-align: left;
}

/* 文本消息的时间显示在左下角 */
.text-message-content .bubble-time {
  left: 4px;
}

.message-bubble.user .bubble-time {
  color: rgba(0, 0, 0, 0.4);
}

.message-bubble.server .bubble-time {
  color: rgba(0, 0, 0, 0.3);
}

/* 图片预览样式 */
.image-preview {
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-image {
  width: 100%;
  max-width: 150px;
  height: auto;
  max-height: 120px;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.2s ease;
  display: block;
}

.preview-image:hover {
  transform: scale(1.02);
  opacity: 0.9;
}

.image-info {
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: rgba(0, 0, 0, 0.6);
}

.image-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.image-size {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.5);
}

/* 语音消息样式 */
.voice-bubble {
  min-width: 120px;
  max-width: 200px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding-bottom: 20px; /* 为时间留出空间 */
  margin-bottom: 8px; /* 为语音转文字留出额外空间 */
}

/* 有图片的语音消息需要更大的最大宽度 */
.voice-bubble:has(.image-preview) {
  max-width: 250px;
}

/* 如果浏览器不支持:has选择器，使用类名方式 */
.voice-bubble.has-image {
  max-width: 250px;
}

/* 左侧语音消息保证时间显示空间 */
.voice-bubble.server {
  min-width: 140px; /* 增加最小宽度以完整显示时间 */
}

/* 右侧用户语音消息样式调整 */
.voice-bubble.user {
  min-width: 100px; /* 用户消息可以更紧凑 */
}

/* 语音消息的时间显示 */
.voice-bubble .bubble-time {
  position: absolute;
  bottom: 4px;
  font-size: 10px;
  color: rgba(0, 0, 0, 0.4);
}

/* 服务器语音消息时间在左下角 */
.voice-bubble.server .bubble-time {
  left: 8px;
}

/* 用户语音消息时间在右下角 */
.voice-bubble.user .bubble-time {
  right: 8px;
}

/* 语音转文字样式 */
.voice-transcription {
  position: absolute;
  top: 100%;
  margin-top: 6px;
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap; /* 不换行显示 */
  line-height: 1.3;
  border: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1; /* 确保在其他元素之上 */
}

/* 用户语音转文字使用浅色气泡样式 */
.voice-transcription.user {
  background: rgba(149, 236, 105, 0.3); /* 浅绿色背景，与用户消息气泡颜色呼应 */
  border: 1px solid rgba(149, 236, 105, 0.5);
  color: rgba(0, 0, 0, 0.7);
}

/* 服务器语音消息的转文字与气泡左对齐，向右延伸 */
.voice-bubble.server .voice-transcription {
  left: 0;
  right: auto;
}

/* 用户语音消息的转文字与气泡右对齐，向左延伸 */
.voice-bubble.user .voice-transcription {
  right: 0;
  left: auto;
}

.voice-bubble:hover {
  transform: scale(1.02);
}

.voice-bubble:active {
  transform: scale(0.98);
}

.voice-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.voice-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.7);
}

.voice-icon svg {
  width: 16px;
  height: 16px;
  transition: all 0.2s ease;
}

/* 播放中的图标样式 */
.voice-icon.playing {
  color: #07c160;
}

.playing-icon {
  animation: playing-pulse 1s ease-in-out infinite;
}

@keyframes playing-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

.voice-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.voice-duration {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.8);
}

.voice-size {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.5);
}

/* 用户语音消息特殊样式 */
.voice-bubble.user .voice-icon {
  color: rgba(0, 0, 0, 0.6);
}

.voice-bubble.user .voice-info {
  align-items: flex-start;
}

/* Loading状态样式 */
.loading-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-dots {
  display: flex;
  gap: 3px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.4);
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

/* 录音状态指示器 */
.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  margin: 8px auto;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recording-animation {
  display: flex;
  gap: 3px;
}

.wave {
  width: 3px;
  height: 12px;
  background: #ff6b6b;
  border-radius: 2px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.1s;
}

.wave:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

.recording-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.recording-time {
  font-size: 12px;
  color: #666;
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

/* 底部输入区域 */
.chat-input {
  background: #ffffff;
  border-top: 1px solid #e5e5e5;
  padding: 12px 16px;
  position: sticky;
  bottom: 0;
  z-index: 200;
  box-sizing: border-box;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  margin-top: auto; /* 确保吸附在底部 */
}

.input-wrapper {
  display: flex;
  align-items: center;
}

.voice-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

/* 图片上传按钮 */
.image-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.image-btn:hover {
  background: #e8e8e8;
  color: #333;
}

.image-btn:active {
  transform: scale(0.95);
}

.image-btn.uploading {
  background: #ffa940;
  color: white;
  cursor: not-allowed;
}

.image-btn.uploaded {
  background: #52c41a;
  color: white;
}

.image-btn.uploaded:hover {
  background: #389e0d;
}

.image-icon,
.loading-icon,
.check-icon {
  width: 20px;
  height: 20px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 语音按钮 */
.voice-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: #07c160;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px;
}

.voice-btn:hover {
  background: #06ad56;
  transform: translateY(-1px);
}

.voice-btn:active {
  transform: translateY(0);
}

.voice-btn.recording {
  background: #ff6b6b;
  animation: recording-pulse 1s ease-in-out infinite;
}

.voice-btn.recording:hover {
  background: #ff5252;
}

.voice-btn.disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
  transform: none;
}

.voice-btn.disabled:hover {
  background: #d9d9d9;
  transform: none;
}

@keyframes recording-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 107, 107, 0);
  }
}

.mic-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.voice-text {
  font-size: 16px;
  font-weight: 500;
}

/* 停止按钮 */
.stop-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: #ff6b6b;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  animation: stop-pulse 2s ease-in-out infinite;
}

.stop-btn:hover {
  background: #ff5252;
  color: white;
}

.stop-btn:active {
  transform: scale(0.95);
}

.stop-icon {
  width: 16px;
  height: 16px;
}

@keyframes stop-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  50% {
    box-shadow: 0 0 0 6px rgba(255, 107, 107, 0);
  }
}

/* 清空按钮 */
.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.clear-btn:hover {
  background: #e8e8e8;
  color: #333;
}

.clear-btn:active {
  transform: scale(0.95);
}

.clear-icon {
  width: 18px;
  height: 18px;
}

/* 滚动条样式 */
.messages-wrapper::-webkit-scrollbar {
  width: 4px;
}

.messages-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.messages-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.messages-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .wechat-container {
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .header-title {
    font-size: 16px;
  }

  .voice-text {
    font-size: 14px;
  }

  .bubble-content {
    font-size: 15px;
  }

  .chat-input {
    border-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .voice-bubble {
    min-width: 100px;
    max-width: 180px;
  }
}

/* 确保在所有屏幕尺寸下底部输入区域都固定 */
@media (max-width: 400px) {
  .chat-input {
    padding: 10px 12px;
  }

  .image-btn {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }

  .voice-btn {
    font-size: 14px;
    min-height: 40px;
    padding: 10px 16px;
  }

  .stop-btn {
    width: 40px;
    height: 40px;
  }

  .clear-btn {
    width: 40px;
    height: 40px;
  }
}
</style>
