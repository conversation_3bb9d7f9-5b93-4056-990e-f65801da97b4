.result-cards {
  width: 100%;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.result-card {
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  background: #fff;
}

.result-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #18a058;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

.object-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: left;
  flex: 1;
}

.image-container {
  width: 100%;
  height: 120px;
  /* 固定高度 */
  overflow: hidden;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  /* 添加flex布局 */
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  position: relative;
}

.cropped-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qa-result {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.question {
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

.answer {
  color: #333;
}

.masks-container {
  margin-top: 24px;
}

.masks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.mask-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.mask-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: contain;
  background: #f5f7fa;
  border-radius: 8px;
}

.preview-container {
  width: 90vw;
  height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: #000;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.1s ease;
  user-select: none;
}

.cropped-image,
.mask-image {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cropped-image:hover,
.mask-image:hover {
  transform: scale(1.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  width: 100%;
}

/* 确保卡片内容垂直对齐 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

/* 优化折叠面板样式 */
:deep(.n-collapse) {
  background: transparent;
  border: none;
}

:deep(.n-collapse-item__header) {
  padding: 8px 0;
}

:deep(.n-collapse-item__content-inner) {
  padding: 8px 16px;
  background: white;
  border-radius: 4px;
}

.preview-thumbnail {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  /* 保持图片比例 */
  background: #f5f7fa;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: block;
}

.preview-thumbnail:hover {
  transform: scale(1.02);
}

/* 修改预览模态框样式 */
:deep(.preview-modal) {
  background: none !important;
}

.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow: hidden;
}

.preview-image {
  /* 允许显示完整尺寸，但初始状态适合屏幕 */
  width: auto;
  height: auto;
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  transition: transform 0.1s ease;
  user-select: none;
  /* 当缩放时，移除尺寸限制 */
  transform-origin: center center;
}

/* 新的网格布局样式 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.grid-item {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  min-height: 280px;
  /* 设置最小高度确保一致性 */
}

.grid-item.image-item {
  justify-content: space-between;
}

.grid-item:hover {
  border-color: #18a058;
  box-shadow: 0 2px 8px rgba(24, 160, 88, 0.1);
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  color: #18a058;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #18a058;
  flex-shrink: 0;
  /* 防止标题被压缩 */
}

.item-title-with-confidence {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #18a058;
  flex-shrink: 0;
}

.item-title-with-confidence .item-title {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
  flex: 1;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  /* 占据剩余空间 */
  justify-content: space-between;
  /* 内容分布在顶部和底部 */
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  padding: 4px 0;
}

.info-item .label {
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.info-item .value {
  color: #333;
  font-family: monospace;
  text-align: right;
  word-break: break-word;
}

.image-item .image-container {
  height: 180px;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e5e7eb;
  flex: 1;
  margin-top: auto;
}

/* 优化问答列表样式 */
.qa-section {
  background: #f8f9fa;
  border: 1px solid #18a058;
  border-radius: 8px;
  padding: 16px;
  /* width: 100%; */
  overflow: hidden;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #18a058;
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid #18a058;
}

.qa-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.qa-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  /* width: 100%; */
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  /* border-radius: 6px; */
  border-left: 1px solid #18a058;
  transition: all 0.2s ease;
}

.qa-item:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateX(2px);
}

.qa-key {
  color: #18a058;
  font-weight: 600;
  white-space: nowrap;
  min-width: fit-content;
  font-size: 13px;
}

.qa-value {
  color: #333;
  text-align: right;
  word-break: break-word;
  font-weight: 500;
  font-size: 13px;
  line-height: 1.4;
  max-width: 70%;
}

/* 角度预测卡片特殊布局 */
.grid-item.angle-item .item-content {
  justify-content: flex-start;
  /* 重置对齐方式 */
}

.grid-item.angle-item .item-content>.info-item:last-of-type {
  margin-bottom: auto;
  /* 推送角点网格到底部 */
}

.grid-item.angle-item .angles-grid {
  margin-top: auto;
  /* 自动推到底部 */
}

/* 抓取点信息卡片特殊布局 */
.grid-item.grasp-item .item-content {
  justify-content: flex-start;
  /* 重置对齐方式 */
}

.grid-item.grasp-item .grasp-points-section {
  margin-top: auto;
  /* 自动推到底部 */
}

/* 角度信息2x2网格布局 */
.angles-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.angle-point-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 6px;
  transition: all 0.2s ease;
  min-height: 50px;
  justify-content: center;
}

.angle-point-item:hover {
  border-color: #18a058;
  background: rgba(24, 160, 88, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(24, 160, 88, 0.1);
}

.angle-label {
  color: #18a058;
  font-weight: 600;
  font-size: 11px;
  text-align: center;
  white-space: nowrap;
}

.angle-value {
  color: #333;
  font-family: monospace;
  font-size: 11px;
  text-align: center;
  word-break: break-word;
  line-height: 1.2;
}

/* 抓取点信息2x2网格布局 */
.grasp-points-section {
  margin-top: 8px;
}

.grasp-points-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.grasp-point-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 6px;
  transition: all 0.2s ease;
  min-height: 50px;
  justify-content: center;
}

.grasp-point-item:hover {
  border-color: #18a058;
  background: rgba(24, 160, 88, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(24, 160, 88, 0.1);
}

.grasp-label {
  color: #18a058;
  font-weight: 600;
  font-size: 11px;
  text-align: center;
  white-space: nowrap;
}

.grasp-value {
  color: #333;
  font-family: monospace;
  font-size: 11px;
  text-align: center;
  word-break: break-word;
  line-height: 1.2;
}

.angle-points {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.points-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 4px;
  min-width: 400px;
}

.point-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
  background: #fff;
  padding: 4px 6px;
  border-radius: 4px;
  min-width: 190px;
}

.point-label {
  color: #666;
  font-weight: 500;
  font-size: 11px;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-family: monospace;
}

/* 关键点信息样式 */
.point-section {
  background: #f8f9fa;
  border: 1px solid #18a058;
  border-radius: 8px;
  padding: 16px;
  width: 100%;
}

.point-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.point-labels,
.point-boxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.labels-list,
.boxes-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.point-label-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.point-label-item {
  background: rgba(24, 160, 88, 0.1);
  color: #18a058;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid rgba(24, 160, 88, 0.2);
}

.point-box-item {
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 12px;
  border-left: 3px solid #18a058;
}

.box-coordinates {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.coordinate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  padding: 2px 0;
  font-size: 12px;
}

.coordinate-item::before {
  content: attr(data-label);
  color: #666;
  font-weight: 500;
}

.retry-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
}

.no-results-info {
  margin-bottom: 20px;
}

.retry-button {
  min-width: 120px;
}

/* 响应式优化 */
@media screen and (max-width: 1200px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .grid-item {
    min-height: 240px;
    /* 在小屏幕上减少最小高度 */
  }

  .cards-container {
    gap: 16px;
  }

  .result-card {
    border-radius: 8px;
  }

  .card-content {
    padding: 16px;
    gap: 12px;
  }

  .qa-section,
  .point-section {
    padding: 12px;
    border-radius: 8px;
  }

  .section-title {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .item-title {
    font-size: 13px;
    margin-bottom: 6px;
  }
}

@media screen and (max-width: 768px) {
  .cards-container {
    gap: 12px;
  }

  .card-content {
    padding: 12px;
    gap: 10px;
  }

  .info-grid {
    gap: 8px;
  }

  .grid-item {
    padding: 8px;
    min-height: 200px;
    /* 在移动端进一步减少最小高度 */
  }

  .qa-item,
  .info-item {
    padding: 6px 8px;
    font-size: 12px;
  }

  .qa-key,
  .info-item .label {
    font-size: 12px;
  }

  .qa-value,
  .info-item .value {
    font-size: 12px;
  }

  .image-item .image-container {
    height: 80px;
  }

  /* 角度网格在小屏幕上的响应式调整 */
  .angles-grid {
    gap: 6px;
  }

  .angle-point-item {
    padding: 6px 4px;
    min-height: 45px;
  }

  .angle-label {
    font-size: 10px;
  }

  .angle-value {
    font-size: 10px;
  }

  /* 抓取点网格在小屏幕上的响应式调整 */
  .grasp-points-grid {
    gap: 6px;
  }

  .grasp-point-item {
    padding: 6px 4px;
    min-height: 45px;
  }

  .grasp-label {
    font-size: 10px;
  }

  .grasp-value {
    font-size: 10px;
  }
}

/* 全局图片展示区域样式 */
.global-images-container {
  margin-bottom: 24px;
}

.global-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.global-image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.global-image-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #18a058;
}

.global-image-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.global-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.global-image:hover {
  transform: scale(1.02);
}

/* 问题列表样式 */
.questions-container {
  margin-bottom: 24px;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.question-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #18a058;
}

.question-text {
  font-size: 14px;
  color: #333;
  flex: 1;
}

/* 3D角度信息样式 */
.angles3d-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.angles3d-content {
  margin-top: 8px;
}

/* 裁剪图片展示样式 */
.cropped-images-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #f0a020;
}

.cropped-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

.cropped-image-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.cropped-image-group:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.group-title {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
  text-align: center;
}

.cropped-image {
  max-width: 100%;
  max-height: 120px;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cropped-image:hover {
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .global-images-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .global-image {
    max-height: 150px;
  }

  .cropped-images-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .cropped-image {
    max-height: 80px;
  }

  .question-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 所有抓取点展示样式 */
.all-grasps-container {
  margin-bottom: 24px;
}

.all-grasps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.grasp-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
}

.grasp-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #18a058;
}

.grasp-card-header {
  background: linear-gradient(135deg, #18a058, #36ad6a);
  color: white;
  padding: 12px 16px;
  font-weight: 500;
}

.grasp-title {
  font-size: 14px;
}

.grasp-card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.grasp-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #18a058;
}

.grasp-label {
  font-size: 13px;
  font-weight: 500;
  color: #555;
  flex-shrink: 0;
}

.grasp-value {
  font-size: 13px;
  color: #333;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  text-align: right;
  word-break: break-all;
}

/* 3D抓取点坐标样式 */
.grasp-3d-point {
  margin-top: 8px;
}

.grasp-3d-point .info-item {
  background: #e8f5e8;
  border-left: 3px solid #18a058;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .all-grasps-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .grasp-card-content {
    padding: 12px;
    gap: 8px;
  }

  .grasp-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    padding: 8px;
  }

  .grasp-value {
    text-align: left;
    font-size: 12px;
  }
}