<script setup>
import { NCard, NIcon, NTag, NDivider } from 'naive-ui'
import { 
  AccessibilitySharp, LogoChrome, ServerOutline, 
  CloudDoneOutline, AnalyticsOutline, BuildOutline, 
  BusinessOutline, CartOutline, DocumentTextOutline, 
  FileTrayFullOutline, GridOutline, LogoAmplify, 
  LogoApple, LogoDesignernews, LogoEdge, LogoElectron 
} from '@vicons/ionicons5'
import { markRaw } from 'vue'

const props = defineProps({
  app: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click'])

// 图标映射表
const iconMap = {
  AccessibilitySharp: markRaw(AccessibilitySharp),
  LogoChrome: markRaw(LogoChrome),
  ServerOutline: markRaw(ServerOutline),
  CloudDoneOutline: markRaw(CloudDoneOutline),
  AnalyticsOutline: markRaw(AnalyticsOutline),
  BuildOutline: markRaw(BuildOutline),
  BusinessOutline: markRaw(BusinessOutline),
  CartOutline: markRaw(CartOutline),
  DocumentTextOutline: markRaw(DocumentTextOutline),
  FileTrayFullOutline: markRaw(FileTrayFullOutline),
  GridOutline: markRaw(GridOutline),
  LogoAmplify: markRaw(LogoAmplify),
  LogoApple: markRaw(LogoApple),
  LogoDesignernews: markRaw(LogoDesignernews),
  LogoEdge: markRaw(LogoEdge),
  LogoElectron: markRaw(LogoElectron)
}

// 获取图标组件
const getIconComponent = (iconName) => {
  const icon = iconMap[iconName]
  if (!icon) {
    console.warn(`Icon not found: ${iconName}, using default icon`)
    return iconMap.AccessibilitySharp
  }
  return icon
}
</script>

<template>
  <n-card hoverable class="app-card" @click="emit('click', app)">
    <div class="app-content">
      <div class="app-main">
        <n-icon
          size="48"
          :component="getIconComponent(app.iconName)"
          class="app-logo"
          :color="app.iconColor"
        />
        <div class="app-info">
          <div class="app-name">{{ app.name }}</div>
          <div class="app-meta">
            <span>所有者：{{ app.owner }}</span>
            <span class="divider">|</span>
            <span>我的角色：{{ app.myRole }}</span>
          </div>
        </div>
        <div class="app-status">
          <n-tag :type="app.enabled ? 'success' : 'warning'" size="small">
            {{ app.enabled ? '已启用' : '已禁用' }}
          </n-tag>
        </div>
      </div>
      <n-divider />
      <div class="app-update">
        <span class="update-label">最新动态：</span>
        <span class="update-text">{{ app.latestUpdate }}</span>
      </div>
    </div>
  </n-card>
</template>

<style scoped>
.app-card {
  height: 140px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
  border-color: #52c41a;
}

.app-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 12px;
  box-sizing: border-box;
}

.app-main {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  position: relative;
  flex: 1;
  min-height: 0;
  margin-bottom: 8px;
}

.app-logo {
  flex-shrink: 0;
}

.app-info {
  flex: 1;
  min-width: 0;
  padding-right: 60px;
}

.app-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app-meta {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.divider {
  margin: 0 8px;
  color: #e8e8e8;
}

.app-status {
  position: absolute;
  top: 0;
  right: 0;
}

:deep(.n-divider) {
  margin: 8px 0;
}

.app-update {
  padding: 0;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 4px;
  min-height: 20px;
  margin-top: auto;
}

.update-label {
  flex-shrink: 0;
  color: #999;
}

.update-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 