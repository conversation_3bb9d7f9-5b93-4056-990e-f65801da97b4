<script setup>
import { NForm, NFormItem, NInput, NIcon } from 'naive-ui'
import { ref, markRaw } from 'vue'
import { 
  AccessibilitySharp, LogoChrome, ServerOutline, 
  CloudDoneOutline, AnalyticsOutline, BuildOutline, 
  BusinessOutline, CartOutline, DocumentTextOutline, 
  FileTrayFullOutline, GridOutline, LogoAmplify, 
  LogoApple, LogoDesignernews, LogoEdge, LogoElectron 
} from '@vicons/ionicons5'

// 先定义颜色选项
const colors = [
  '#1677ff', '#52c41a', '#722ed1', '#eb2f96', '#fa8c16',
  '#13c2c2', '#2f54eb', '#faad14', '#a0d911', '#eb2f96', '#f5222d'
]

// 再定义图标列表
const iconList = [
  { name: 'AccessibilitySharp', component: markRaw(AccessibilitySharp) },
  { name: 'LogoChrome', component: markRaw(LogoChrome) },
  { name: 'ServerOutline', component: markRaw(ServerOutline) },
  { name: 'CloudDoneOutline', component: markRaw(CloudDoneOutline) },
  { name: 'AnalyticsOutline', component: markRaw(AnalyticsOutline) },
  { name: 'BuildOutline', component: markRaw(BuildOutline) },
  { name: 'BusinessOutline', component: markRaw(BusinessOutline) },
  { name: 'CartOutline', component: markRaw(CartOutline) },
  { name: 'DocumentTextOutline', component: markRaw(DocumentTextOutline) },
  { name: 'FileTrayFullOutline', component: markRaw(FileTrayFullOutline) },
  { name: 'GridOutline', component: markRaw(GridOutline) },
  { name: 'LogoAmplify', component: markRaw(LogoAmplify) },
  { name: 'LogoApple', component: markRaw(LogoApple) },
  { name: 'LogoDesignernews', component: markRaw(LogoDesignernews) },
  { name: 'LogoEdge', component: markRaw(LogoEdge) },
  { name: 'LogoElectron', component: markRaw(LogoElectron) }
]

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({
      name: '',
      description: '',
      iconName: '',
      iconColor: '',
      docUrl: ''
    })
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref(null)
// 然后再初始化表单数据
const formModel = ref({
  name: props.initialData.name || '',
  description: props.initialData.description || '',
  iconName: props.initialData.iconName || iconList[0].name,
  iconColor: props.initialData.iconColor || colors[0],
  docUrl: props.initialData.docUrl || ''
})

// 表单验证规则
const rules = {
  name: {
    required: true,
    message: '请输入应用名称',
    trigger: ['blur', 'input']
  },
  description: {
    required: true,
    message: '请输入应用描述',
    trigger: ['blur', 'input']
  },
  iconName: {
    required: true,
    message: '请选择应用图标'
  },
  iconColor: {
    required: true,
    message: '请选择图标颜色'
  },
  docUrl: {
    validator: (rule, value) => {
      if (!value) return true
      if (value.length > 1024) return new Error('文档地址长度不能超过1024个字符')
      if (!value.startsWith('http')) return new Error('请输入正确的文档地址URL')
      return true
    },
    trigger: ['blur', 'input']
  }
}

// 获取图标组件
const getIconComponent = (iconName) => {
  const icon = iconList.find(i => i.name === iconName)
  if (!icon) {
    console.warn(`Icon not found: ${iconName}, using default icon`)
    return AccessibilitySharp
  }
  return icon.component
}

const handleSubmit = () => {
  console.log('表单提交触发', formModel.value)
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      emit('submit', formModel.value)
    } else {
      console.log('表单验证错误:', errors)
    }
  })
}

// 将 handleSubmit 方法暴露给父组件
defineExpose({
  handleSubmit
})
</script>

<template>
  <n-form
    ref="formRef"
    :model="formModel"
    :rules="rules"
    label-placement="left"
    label-width="100"
    require-mark-placement="right-hanging"
  >
    <n-form-item label="图标预览" class="preview-form-item">
      <div class="icon-preview">
        <n-icon
          v-if="formModel.iconName && formModel.iconColor"
          :component="getIconComponent(formModel.iconName)"
          :color="formModel.iconColor"
          size="48"
        />
        <div v-else class="preview-placeholder">
          选择图标和颜色后预览
        </div>
      </div>
    </n-form-item>

    <n-form-item label="应用名称" path="name">
      <n-input v-model:value="formModel.name" placeholder="请输入应用名称" />
    </n-form-item>
    
    <n-form-item label="应用描述" path="description">
      <n-input
        v-model:value="formModel.description"
        type="textarea"
        placeholder="请输入应用描述"
        :rows="3"
      />
    </n-form-item>

    <n-form-item label="图标颜色" path="iconColor">
      <div class="color-grid">
        <div
          v-for="color in colors.slice(0, 8)"
          :key="color"
          class="option-button"
          :class="{ 'option-selected': formModel.iconColor === color }"
          @click="formModel.iconColor = color"
          :style="{ backgroundColor: color }"
        />
      </div>
    </n-form-item>

    <n-form-item label="应用图标" path="iconName">
      <div class="icon-grid">
        <div
          v-for="icon in iconList"
          :key="icon.name"
          class="option-button"
          :class="{ 'option-selected': formModel.iconName === icon.name }"
          @click="formModel.iconName = icon.name"
        >
          <n-icon :component="icon.component" size="24" />
        </div>
      </div>
    </n-form-item>

    <n-form-item label="文档地址" path="docUrl">
      <n-input
        v-model:value="formModel.docUrl"
        placeholder="请输入文档地址（选填）"
        clearable
      />
    </n-form-item>
  </n-form>
</template>

<style scoped>
.preview-form-item {
  margin-bottom: 24px !important;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  background-color: transparent;
  border-radius: 8px;
}

.preview-placeholder {
  color: #999;
  font-size: 14px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 16px;
  padding: 4px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  padding: 4px;
}

.option-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.option-button:hover {
  transform: scale(1.05);
}

.option-selected {
  border: 2px solid #52c41a !important;
  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
}

:deep(.n-form-item) {
  margin-bottom: 24px !important;
}

:deep(.n-form-item:last-child) {
  margin-bottom: 0 !important;
}
</style> 