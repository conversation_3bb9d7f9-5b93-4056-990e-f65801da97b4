<template>
  <pre class="result-code"><code>{{ displayText }}</code></pre>
</template>

<script setup>
import { ref, watch, onUnmounted } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true
  }
})

const displayText = ref('')
let currentIndex = 0
let typingTimer = null

const typeText = () => {
  if (currentIndex < props.text.length) {
    displayText.value += props.text[currentIndex]
    currentIndex++
    typingTimer = setTimeout(typeText, 10)
  }
}

watch(() => props.text, (newText) => {
  // 清理之前的定时器
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
  // 重置状态
  currentIndex = 0
  displayText.value = ''
  // 开始新的打字效果
  if (newText) {
    typeText()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<style scoped>
.result-code {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
}
</style> 