<template>
  <div class="result-cards">
    <!-- 添加重试按钮 -->
    <div v-if="result.taskStatus === 'TIMEOUT'" class="retry-container">
      <n-alert type="warning" title="提示"> 获取结果超时 </n-alert>
      <n-button type="primary" @click="$emit('retry')" class="retry-button">
        重试
      </n-button>
    </div>

    <!-- 全局图片展示区域 -->
    <div class="global-images-container" v-if="hasGlobalImages()">
      <n-divider>全局分析图片</n-divider>
      <div class="global-images-grid">
        <!-- 角度图片 -->
        <div class="global-image-item" v-if="result.angeleImage">
          <div class="global-image-title">角度分析图</div>
          <img
            :src="result.angeleImage"
            alt="角度分析图"
            class="global-image"
            @click="showImagePreview(result.angeleImage)"
          />
        </div>

        <!-- 抓取图片 -->
        <div class="global-image-item" v-if="result.graspImage">
          <div class="global-image-title">抓取分析图</div>
          <img
            :src="result.graspImage"
            alt="抓取分析图"
            class="global-image"
            @click="showImagePreview(result.graspImage)"
          />
        </div>
      </div>
    </div>

    <!-- 问题列表展示 -->
    <div
      class="questions-container"
      v-if="result.questions && result.questions.length > 0"
    >
      <n-divider>问题列表</n-divider>
      <div class="questions-list">
        <div
          v-for="(question, index) in result.questions"
          :key="index"
          class="question-item"
        >
          <n-tag type="info" size="small">问题 {{ index + 1 }}</n-tag>
          <span class="question-text">{{ question }}</span>
        </div>
      </div>
    </div>

    <!-- 原有的卡片内容 - 只有当有标签时才显示 -->
    <div
      class="cards-container"
      v-if="result.labels && result.labels.length > 0"
    >
      <n-card
        v-for="(label, index) in result.labels"
        :key="index"
        class="result-card"
      >
        <div class="card-content">
          <!-- 物体名称和置信度 -->
          <div class="card-header">
            <div class="object-name">{{ label }}</div>
          </div>

          <!-- 2x2 网格布局：图片、边界框、角度信息、抓取点信息 -->
          <div class="info-grid">
            <!-- 预览图片 -->
            <div class="grid-item image-item" v-if="imagesList?.[index]">
              <div class="item-title-with-confidence">
                <span class="item-title">识别结果</span>
                <n-tag
                  v-if="result.scores && result.scores[index] !== undefined"
                  :type="getScoreType(result.scores[index])"
                  size="small"
                >
                  置信度: {{ result.scores[index] }}
                </n-tag>
              </div>
              <div class="image-container">
                <img
                  :src="imagesList[index]"
                  :alt="label"
                  class="preview-thumbnail"
                  @click="showImagePreview(imagesList[index])"
                />
              </div>
            </div>

            <!-- 边界框信息 -->
            <div class="grid-item" v-if="result.boxes && result.boxes[index]">
              <div class="item-title">边界框信息</div>
              <div class="item-content">
                <div class="info-item">
                  <span class="label">左上</span>
                  <span class="value">
                    ({{ result.boxes[index][0] }}, {{ result.boxes[index][1] }})
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">右下</span>
                  <span class="value">
                    ({{ result.boxes[index][2] }}, {{ result.boxes[index][3] }})
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">尺寸</span>
                  <span class="value">
                    {{ result.boxes[index][2] - result.boxes[index][0] }} ×
                    {{ result.boxes[index][3] - result.boxes[index][1] }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 角度预测 -->
            <div class="grid-item angle-item" v-if="result.angles?.[index]">
              <div class="item-title">角度预测</div>
              <div class="item-content">
                <div class="info-item">
                  <span class="label">角度</span>
                  <span class="value">{{ result.angles[index].angle }}°</span>
                </div>
                <div class="info-item">
                  <span class="label"></span>
                  <span class="value"> </span>
                </div>
                <div class="info-item">
                  <span class="label">角点</span>
                  <span class="value"> </span>
                </div>
                <!-- 四个角度信息的2x2网格布局 -->
                <div class="angles-grid">
                  <div class="angle-point-item">
                    <span class="angle-label">左上</span>
                    <span class="angle-value">
                      ({{ result.angles[index].anglesPoint[0][0] }},
                      {{ result.angles[index].anglesPoint[0][1] }})
                    </span>
                  </div>
                  <div class="angle-point-item">
                    <span class="angle-label">右上</span>
                    <span class="angle-value">
                      ({{ result.angles[index].anglesPoint[2][0] }},
                      {{ result.angles[index].anglesPoint[2][1] }})
                    </span>
                  </div>
                  <div class="angle-point-item">
                    <span class="angle-label">左下</span>
                    <span class="angle-value">
                      ({{ result.angles[index].anglesPoint[1][0] }},
                      {{ result.angles[index].anglesPoint[1][1] }})
                    </span>
                  </div>
                  <div class="angle-point-item">
                    <span class="angle-label">右下</span>
                    <span class="angle-value">
                      ({{ result.angles[index].anglesPoint[3][0] }},
                      {{ result.angles[index].anglesPoint[3][1] }})
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 抓取点信息 -->
            <div class="grid-item grasp-item" v-if="getGraspForObject(index)">
              <div class="item-title">抓取点信息</div>
              <div class="item-content">
                <div class="info-item">
                  <span class="label">抓取角度</span>
                  <span class="value"
                    >{{
                      formatNumber(getGraspForObject(index).graspAngle)
                    }}°</span
                  >
                </div>
                <div
                  class="info-item"
                  v-if="
                    getGraspForObject(index).graspDepth !== undefined &&
                    getGraspForObject(index).graspDepth !== null
                  "
                >
                  <span class="label">抓取深度</span>
                  <span class="value">{{
                    formatNumber(getGraspForObject(index).graspDepth)
                  }}</span>
                </div>
                <div
                  class="info-item"
                  v-if="
                    getGraspForObject(index).graspHeight !== undefined &&
                    getGraspForObject(index).graspHeight !== null
                  "
                >
                  <span class="label">抓取高度</span>
                  <span class="value">{{
                    formatNumber(getGraspForObject(index).graspHeight)
                  }}</span>
                </div>
                <div
                  class="info-item"
                  v-if="
                    getGraspForObject(index).graspWidth !== undefined &&
                    getGraspForObject(index).graspWidth !== null
                  "
                >
                  <span class="label">抓取宽度</span>
                  <span class="value">{{
                    formatNumber(getGraspForObject(index).graspWidth)
                  }}</span>
                </div>
                <!-- 抓取点坐标 - 根据数据格式动态显示 -->
                <div
                  v-if="getGraspForObject(index).graspPoint"
                  class="grasp-points-section"
                >
                  <!-- 3D坐标格式 (新格式) -->
                  <div
                    v-if="is3DGraspPoint(getGraspForObject(index).graspPoint)"
                    class="grasp-3d-point"
                  >
                    <div class="info-item">
                      <span class="label">抓取点坐标</span>
                      <span class="value">
                        ({{
                          formatNumber(
                            getGraspForObject(index).graspPoint[0]?.[0]
                          )
                        }},
                        {{
                          formatNumber(
                            getGraspForObject(index).graspPoint[1]?.[0]
                          )
                        }},
                        {{
                          formatNumber(
                            getGraspForObject(index).graspPoint[2]?.[0]
                          )
                        }})
                      </span>
                    </div>
                  </div>

                  <!-- 2D角点格式 (旧格式) -->
                  <div v-else class="grasp-points-grid">
                    <div class="grasp-point-item">
                      <span class="grasp-label">左上</span>
                      <span class="grasp-value">
                        ({{ getGraspForObject(index).graspPoint[0]?.[0] }},
                        {{ getGraspForObject(index).graspPoint[0]?.[1] }})
                      </span>
                    </div>
                    <div class="grasp-point-item">
                      <span class="grasp-label">右上</span>
                      <span class="grasp-value">
                        ({{ getGraspForObject(index).graspPoint[2]?.[0] }},
                        {{ getGraspForObject(index).graspPoint[2]?.[1] }})
                      </span>
                    </div>
                    <div class="grasp-point-item">
                      <span class="grasp-label">左下</span>
                      <span class="grasp-value">
                        ({{ getGraspForObject(index).graspPoint[1]?.[0] }},
                        {{ getGraspForObject(index).graspPoint[1]?.[1] }})
                      </span>
                    </div>
                    <div class="grasp-point-item">
                      <span class="grasp-label">右下</span>
                      <span class="grasp-value">
                        ({{ getGraspForObject(index).graspPoint[3]?.[0] }},
                        {{ getGraspForObject(index).graspPoint[3]?.[1] }})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 问答列表 - 单独显示，占满宽度 -->
          <div class="qa-section" v-if="result.answers?.[index]">
            <div class="section-title">问答结果</div>
            <div class="qa-content">
              <div
                v-for="(value, key) in parseAnswer(result.answers[index])"
                :key="key"
                class="qa-item"
              >
                <span class="qa-key">{{ key }}:</span>
                <span class="qa-value">{{ value }}</span>
              </div>
            </div>
          </div>

          <!-- 3D角度信息 -->
          <div
            class="angles3d-section"
            v-if="
              result.angles3D &&
              result.angles3D.length > 0 &&
              result.angles3D[index]
            "
          >
            <div class="section-title">3D角度信息</div>
            <div class="angles3d-content">
              <div class="info-item">
                <span class="label">3D角度</span>
                <span class="value">{{ result.angles3D[index] }}</span>
              </div>
            </div>
          </div>

          <!-- 裁剪图片展示 -->
          <div class="cropped-images-section" v-if="hasCroppedImages(index)">
            <div class="section-title">裁剪图片</div>
            <div class="cropped-images-grid">
              <!-- 角度裁剪图片 -->
              <div
                class="cropped-image-group"
                v-if="result.croppedImagesListAngle?.[index]"
              >
                <div class="group-title">角度裁剪</div>
                <img
                  :src="result.croppedImagesListAngle[index]"
                  alt="角度裁剪图片"
                  class="cropped-image"
                  @click="
                    showImagePreview(result.croppedImagesListAngle[index])
                  "
                />
              </div>

              <!-- 边界框裁剪图片 -->
              <div
                class="cropped-image-group"
                v-if="result.croppedImagesListBbox?.[index]"
              >
                <div class="group-title">边界框裁剪</div>
                <img
                  :src="result.croppedImagesListBbox[index]"
                  alt="边界框裁剪图片"
                  class="cropped-image"
                  @click="showImagePreview(result.croppedImagesListBbox[index])"
                />
              </div>

              <!-- 抓取裁剪图片 -->
              <div
                class="cropped-image-group"
                v-if="result.croppedImagesListGrasp?.[index]"
              >
                <div class="group-title">抓取裁剪</div>
                <img
                  :src="result.croppedImagesListGrasp[index]"
                  alt="抓取裁剪图片"
                  class="cropped-image"
                  @click="
                    showImagePreview(result.croppedImagesListGrasp[index])
                  "
                />
              </div>

              <!-- 关键点裁剪图片 -->
              <div
                class="cropped-image-group"
                v-if="result.croppedImagesListPoint?.[index]"
              >
                <div class="group-title">关键点裁剪</div>
                <img
                  :src="result.croppedImagesListPoint[index]"
                  alt="关键点裁剪图片"
                  class="cropped-image"
                  @click="
                    showImagePreview(result.croppedImagesListPoint[index])
                  "
                />
              </div>

              <!-- 分割裁剪图片 -->
              <div
                class="cropped-image-group"
                v-if="result.croppedImagesListSegment?.[index]"
              >
                <div class="group-title">分割裁剪</div>
                <img
                  :src="result.croppedImagesListSegment[index]"
                  alt="分割裁剪图片"
                  class="cropped-image"
                  @click="
                    showImagePreview(result.croppedImagesListSegment[index])
                  "
                />
              </div>
            </div>
          </div>

          <!-- 关键点信息 -->
          <div class="point-section" v-if="result.points?.[index]">
            <div class="section-title">关键点信息</div>
            <div class="point-content">
              <!-- 关键点标签 -->
              <div class="point-labels">
                <span class="label">关键点</span>
                <div class="labels-list">
                  <div
                    v-for="(labels, pIndex) in result.points[index].pointLabels"
                    :key="pIndex"
                    class="point-label-group"
                  >
                    <div
                      v-for="(label, lIndex) in labels"
                      :key="lIndex"
                      class="point-label-item"
                    >
                      {{ label }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 关键点边界框 -->
              <div class="point-boxes">
                <span class="label">关键点位置</span>
                <div class="boxes-list">
                  <div
                    v-for="(box, pIndex) in result.points[index].pointBoxes"
                    :key="pIndex"
                    class="point-box-item"
                  >
                    <div class="box-coordinates">
                      <div class="coordinate-item" data-label="左上">
                        ({{ box[0] }}, {{ box[1] }})
                      </div>
                      <div class="coordinate-item" data-label="右下">
                        ({{ box[2] }}, {{ box[3] }})
                      </div>
                      <div class="coordinate-item" data-label="尺寸">
                        {{ box[2] - box[0] }} × {{ box[3] - box[1] }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 所有抓取点展示 -->
    <div
      class="all-grasps-container"
      v-if="result.grasps && result.grasps.length > 0"
    >
      <n-divider>所有抓取点信息</n-divider>
      <div class="all-grasps-grid">
        <div
          v-for="(grasp, graspIndex) in result.grasps"
          :key="graspIndex"
          class="grasp-card"
        >
          <div class="grasp-card-header">
            <span class="grasp-title">抓取点 {{ graspIndex + 1 }}</span>
          </div>
          <div class="grasp-card-content">
            <div class="grasp-info-item">
              <span class="grasp-label">抓取角度</span>
              <span class="grasp-value"
                >{{ formatNumber(grasp.graspAngle) }}°</span
              >
            </div>
            <div
              class="grasp-info-item"
              v-if="grasp.graspDepth !== null && grasp.graspDepth !== undefined"
            >
              <span class="grasp-label">抓取深度</span>
              <span class="grasp-value">{{
                formatNumber(grasp.graspDepth)
              }}</span>
            </div>
            <div
              class="grasp-info-item"
              v-if="
                grasp.graspHeight !== null && grasp.graspHeight !== undefined
              "
            >
              <span class="grasp-label">抓取高度</span>
              <span class="grasp-value">{{
                formatNumber(grasp.graspHeight)
              }}</span>
            </div>
            <div
              class="grasp-info-item"
              v-if="grasp.graspWidth !== null && grasp.graspWidth !== undefined"
            >
              <span class="grasp-label">抓取宽度</span>
              <span class="grasp-value">{{
                formatNumber(grasp.graspWidth)
              }}</span>
            </div>
            <div class="grasp-info-item" v-if="grasp.graspPoint">
              <span class="grasp-label">抓取点坐标</span>
              <span class="grasp-value" v-if="is3DGraspPoint(grasp.graspPoint)">
                ({{ formatNumber(grasp.graspPoint[0]?.[0]) }},
                {{ formatNumber(grasp.graspPoint[1]?.[0]) }},
                {{ formatNumber(grasp.graspPoint[2]?.[0]) }})
              </span>
              <span class="grasp-value" v-else>
                多点坐标 ({{ grasp.graspPoint.length }} 个点)
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分割遮罩展示 -->
    <div class="masks-container" v-if="result.maskImage?.length">
      <n-divider>分割遮罩</n-divider>
      <div class="masks-grid">
        <div
          v-for="(maskImage, index) in result.maskImage"
          :key="index"
          class="mask-item"
        >
          <img
            :src="maskImage"
            alt="分割遮罩"
            class="mask-image"
            @click="showImagePreview(maskImage)"
          />
          <n-button
            size="small"
            @click="downloadMaskData(result.maskData?.[index])"
            :loading="downloading"
          >
            下载遮罩数据
          </n-button>
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <n-modal
      v-model:show="showPreview"
      :mask-closable="true"
      transform-origin="center"
      class="preview-modal"
    >
      <div class="preview-container">
        <img
          :src="previewImage"
          class="preview-image"
          @wheel.prevent="handleZoom"
          @mousedown="startDrag"
          @mousemove="onDrag"
          @mouseup="stopDrag"
          @mouseleave="stopDrag"
          @dblclick="handleDoubleClick"
          :style="imageStyle"
        />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import {
  NCard,
  NDivider,
  NButton,
  NModal,
  NTag,
  NCollapse,
  NCollapseItem,
} from "naive-ui";
import { useResultCards } from "./ResultCards.js";

const props = defineProps({
  result: {
    type: Object,
    required: true,
  },
  imagesList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["mounted", "retry"]);

// 使用提取的逻辑
const {
  showPreview,
  previewImage,
  downloading,
  imageStyle,
  showImagePreview,
  handleZoom,
  startDrag,
  onDrag,
  stopDrag,
  handleDoubleClick,
  resetToFitScreen,
  downloadMaskData,
  getScoreType,
  parseAnswer,
  imageNaturalSize,
  fitToScreenScale,
  hasGlobalImages,
  hasCroppedImages,
  is3DGraspPoint,
  formatNumber,
  getGraspForObject,
} = useResultCards(props, emit);
</script>

<style src="./ResultCards.css" scoped></style>