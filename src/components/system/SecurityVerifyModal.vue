<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { NModal, NForm, NFormItem, NInput, NSpin, NButton, NSpace, NIcon } from 'naive-ui'
import { WarningOutline } from '@vicons/ionicons5'
import messages from '@/utils/messages'
import { doPost, doGet } from '@/utils/requests'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '安全验证'
  },
  action: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:show', 'verify'])

const formRef = ref(null)
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const codeInputs = ref(Array(6).fill(''))
const inputRefs = ref([])

// 添加计算属性检查输入是否完整
const isCodeComplete = computed(() => {
  return codeInputs.value.every(code => code !== '')
})

// 处理验证码输入
const handleCodeInput = (index, event) => {
  const value = event.target.value
  // 只取最后一个字符
  const char = value.slice(-1)
  codeInputs.value[index] = char

  if (char && index < 5) {
    // 自动聚焦下一个输入框
    inputRefs.value[index + 1]?.focus()
  }
}

// 处理键盘事件
const handleKeydown = (index, event) => {
  if (event.key === 'Backspace' && !codeInputs.value[index] && index > 0) {
    // 当前框为空且按下删除键时，焦点移到上一个框
    codeInputs.value[index - 1] = ''
    inputRefs.value[index - 1]?.focus()
  }
}

// 处理粘贴
const handlePaste = (event) => {
  event.preventDefault()
  const pastedText = event.clipboardData.getData('text')
  const chars = [...pastedText.trim()].slice(0, 6)
  
  // 清空所有输入框
  codeInputs.value = Array(6).fill('')
  
  // 依次填充字符
  chars.forEach((char, index) => {
    codeInputs.value[index] = char
  })

  // 聚焦到下一个空输入框或最后一个输入框
  const nextEmptyIndex = codeInputs.value.findIndex(v => !v)
  const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex
  inputRefs.value[focusIndex]?.focus()
}

// 发送验证码
const handleSendCode = async () => {
  if (sendingCode.value || countdown.value > 0) return
  
  sendingCode.value = true
  try {
    await doPost('/auth-center/system/validation', {
      operation: props.action
    })
    messages.success('验证码已发送至您的邮箱')
    countdown.value = 60
    startCountdown()
  } catch (error) {
    messages.error('验证码发送失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 倒计时
const startCountdown = () => {
  const timer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理验证提交
const handleVerify = async () => {
  if (loading.value) return
  
  const verifyCode = codeInputs.value.join('')
  if (verifyCode.length !== 6) {
    messages.error('请输入完整的验证码')
    return
  }
  
  loading.value = true
  try {
    // 开发阶段直接模拟验证通过
    emit('verify', {
      verifyCode,
      action: props.action
    })
    // 清空输入
    codeInputs.value = Array(6).fill('')
    emit('update:show', false)
  } catch (error) {
    messages.error('验证失败，请重试')
    // 清空输入
    codeInputs.value = Array(6).fill('')
    inputRefs.value[0]?.focus()
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  codeInputs.value = Array(6).fill('')
  emit('update:show', false)
}

// 监听显示状态变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 显示时自动发送验证码并聚焦第一个输入框
    handleSendCode()
    setTimeout(() => {
      inputRefs.value[0]?.focus()
    }, 100)
  } else {
    codeInputs.value = Array(6).fill('')
    countdown.value = 0
  }
})

// 处理模态框显示状态
const handleUpdateShow = (value) => {
  emit('update:show', value)
}
</script>

<template>
  <n-modal
    :show="show"
    preset="dialog"
    title="安全验证"
    positive-text="确认"
    negative-text="取消"
    :mask-closable="false"
    :closable="false"
    :keyboard="false"
    @update:show="handleUpdateShow"
    @positive-click="handleVerify"
    @negative-click="handleCancel"
    :positive-button-props="{ 
      disabled: loading || !isCodeComplete,
      loading: loading
    }"
  >
    <n-spin :show="loading">
      <div class="verify-content">
        <div class="verify-notice">
          <p class="notice-title">
            <n-icon size="18" color="#ff4d4f" style="margin-right: 8px">
              <warning-outline />
            </n-icon>
            您正在进行敏感操作：{{ props.action }}
          </p>
          <p class="notice-desc">请输入邮箱验证码进行验证</p>
        </div>
        <div class="verify-code-input">
          <n-space :size="12" justify="center">
            <input
              v-for="(code, index) in codeInputs"
              :key="index"
              :ref="el => inputRefs[index] = el"
              v-model="codeInputs[index]"
              type="text"
              maxlength="1"
              class="code-input"
              @input="e => handleCodeInput(index, e)"
              @keydown="e => handleKeydown(index, e)"
              @paste="handlePaste"
            />
          </n-space>
        </div>
        <div class="resend-button">
          <n-button 
            :disabled="sendingCode || countdown > 0"
            :loading="sendingCode"
            @click="handleSendCode"
            text
            type="primary"
          >
            {{ countdown > 0 ? `${countdown}秒后可重新发送` : '重新发送验证码' }}
          </n-button>
        </div>
      </div>
    </n-spin>
  </n-modal>
</template>

<style scoped>
.verify-content {
  padding: 8px 0;
}

.verify-notice {
  margin-bottom: 32px;
  text-align: center;
}

.notice-title {
  font-size: 14px;
  color: #262626;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.notice-desc {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
}

.verify-code-input {
  margin: 24px 0;
}

.code-input {
  width: 44px;
  height: 44px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 20px;
  text-align: center;
  color: #262626;
  background: #fff;
  transition: all 0.3s;
  outline: none;
  caret-color: #1890ff;
}

.code-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.resend-button {
  text-align: center;
  margin-top: 16px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 调整按钮内部的loading图标对齐 */
:deep(.n-button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}
</style> 