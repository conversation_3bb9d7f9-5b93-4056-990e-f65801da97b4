<script setup>
import { ref, watch, nextTick } from 'vue'
import { 
  NModal, NSpace, NInput, NButton, 
  NTable, NEmpty, NSpin, NCheckbox, NRadio 
} from 'naive-ui'
import { doGet } from '@/utils/requests'
import messages from '@/utils/messages'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择成员'
  },
  description: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: '请选择成员'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxSelected: {
    type: Number,
    default: Infinity
  }
})

const emit = defineEmits(['update:show', 'confirm'])

// 状态变量
const keyword = ref('')
const loading = ref(false)
const contacts = ref([])
const selectedContacts = ref([])

// 搜索防抖
let searchTimer = null
const handleSearch = (value) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    searchContacts(value)
  }, 300)
}

// 搜索联系人
const searchContacts = async (keyword) => {
  if (!keyword) {
    contacts.value = []
    return
  }

  loading.value = true
  try {
    const response = await doGet(`/auth-center/system/user/list?keywords=${keyword}`)
    contacts.value = response.data
  } catch (error) {
    messages.error('搜索成员失败')
  } finally {
    loading.value = false
  }
}

// 处理选择
const handleSelect = (contact) => {
  if (props.multiple) {
    const index = selectedContacts.value.findIndex(item => item.id === contact.id)
    if (index > -1) {
      selectedContacts.value.splice(index, 1)
    } else {
      if (selectedContacts.value.length >= props.maxSelected) {
        messages.warning(`最多只能选择${props.maxSelected}个成员`)
        return
      }
      selectedContacts.value.push({
        id: contact.id,
        nickname: contact.nickname
      })
    }
  } else {
    selectedContacts.value = [{
      id: contact.id,
      nickname: contact.nickname
    }]
  }
}

// 检查是否已选中
const isSelected = (contact) => {
  return selectedContacts.value.some(item => item.id === contact.id)
}

// 确认选择
const handleConfirm = () => {
  if (selectedContacts.value.length === 0) {
    messages.warning('请至少选择一个成员')
    return
  }
  emit('confirm', selectedContacts.value)
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  handleUpdateShow(false)
  // 重置状态
  keyword.value = ''
  contacts.value = []
  selectedContacts.value = []
}

// 添加处理显示状态更新的函数
const handleUpdateShow = (value) => {
  emit('update:show', value)
}

// 监听show变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 弹窗显示时，等待DOM更新后聚焦输入框
    nextTick(() => {
      const input = document.querySelector('.contact-search-input input')
      input?.focus()
    })
  } else {
    handleClose()
  }
})

// 修改表格列定义
const columns = [
  {
    title: '姓名',
    key: 'nickname',
    align: 'center'
  },
  {
    title: '邮箱',
    key: 'username',
    align: 'center'
  },
  {
    title: '部门',
    key: 'deptName',
    align: 'center'
  }
]
</script>

<template>
  <n-modal
    :show="show"
    :title="title"
    preset="dialog"
    :mask-closable="false"
    style="width: 600px"
    positive-text="确认"
    negative-text="取消"
    @positive-click="handleConfirm"
    @negative-click="handleClose"
    @update:show="handleUpdateShow"
  >
    <n-space vertical :size="16">
      <!-- 说明文字 -->
      <p v-if="description" class="description">{{ description }}</p>

      <!-- 搜索框 -->
      <n-input
        v-model:value="keyword"
        placeholder="请输入关键词搜索"
        @input="handleSearch"
        clearable
        class="contact-search-input"
      />

      <!-- 成员列表 -->
      <div class="contact-list">
        <n-spin :show="loading">
          <div class="list-wrapper">
            <div v-if="contacts.length > 0" class="list-content">
              <n-table :single-line="false">
                <thead>
                  <tr>
                    <th v-for="col in columns" :key="col.key" :style="{ textAlign: col.align }">
                      {{ col.title }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="contact in contacts"
                    :key="contact.id"
                    :class="{ 'selected-row': isSelected(contact) }"
                    style="cursor: pointer"
                    @click="handleSelect(contact)"
                  >
                    <td v-for="col in columns" :key="col.key" :style="{ textAlign: col.align }">
                      {{ contact[col.key] }}
                    </td>
                  </tr>
                </tbody>
              </n-table>
            </div>
            <div v-else class="empty-content">
              <n-empty description="暂无搜索结果" />
            </div>
          </div>
        </n-spin>
      </div>

      <!-- 已选成员区域 - 始终显示 -->
      <div class="selected-contacts">
        <div class="selected-title">已选择 {{ selectedContacts.length }} 人</div>
        <div class="selected-tags" :class="{ empty: selectedContacts.length === 0 }">
          <template v-if="selectedContacts.length > 0">
            <n-space>
              <n-tag
                v-for="contact in selectedContacts"
                :key="contact.id"
                closable
                type="primary"
                @close="handleSelect(contact)"
              >
                {{ contact.nickname }}
              </n-tag>
            </n-space>
          </template>
          <span v-else class="no-selected">未选择任何成员</span>
        </div>
      </div>
    </n-space>
  </n-modal>
</template>

<style scoped>
.description {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.contact-list {
  border: 1px solid #eee;
  border-radius: 4px;
}

.list-wrapper {
  height: 240px;
  overflow-y: auto;
  position: relative;
}

.list-content {
  min-height: 100%;
}

.empty-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-align: center;
}

.selected-contacts {
  padding-top: 8px;
  border-top: 1px solid #eee;
  min-height: 80px;
}

.selected-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.selected-tags {
  min-height: 32px;
  padding: 4px 0;
}

.selected-tags.empty {
  display: flex;
  align-items: center;
}

.no-selected {
  color: #999;
  font-size: 14px;
}

.selected-row {
  background-color: var(--primary-color, #18a058) !important;
  color: #fff;
}

.selected-row:hover {
  background-color: var(--primary-color-hover, #36ad6a) !important;
}

:deep(.n-table .n-data-table-td) {
  padding: 8px;
}

:deep(.n-table .n-data-table-th) {
  padding: 8px;
  background-color: #f5f5f5;
  height: 40px;
}

tr {
  transition: all 0.3s;
  height: 40px;
}

tr:hover {
  background-color: var(--primary-color-hover, #36ad6a);
  color: #fff;
}

/* 修改标签样式 */
.n-tag {
  cursor: pointer;
}

.n-tag:hover {
  opacity: 0.9;
}
</style> 