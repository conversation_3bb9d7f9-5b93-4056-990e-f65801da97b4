import { ref, computed, onMounted } from "vue";
import messages from "@/utils/messages";

export function useResultCards(props, emit) {
  // 图片预览相关状态
  const showPreview = ref(false);
  const previewImage = ref("");
  const scale = ref(1);
  const position = ref({ x: 0, y: 0 });
  const isDragging = ref(false);
  const dragStart = ref({ x: 0, y: 0 });
  const imageNaturalSize = ref({ width: 0, height: 0 });
  const fitToScreenScale = ref(1);

  // 下载状态
  const downloading = ref(false);

  // 计算图片样式
  const imageStyle = computed(() => {
    const baseStyle = {
      transform: `scale(${scale.value}) translate(${position.value.x}px, ${position.value.y}px)`,
      cursor: isDragging.value ? "grabbing" : "grab",
      transformOrigin: "center center",
      transition: isDragging.value ? "none" : "transform 0.1s ease",
    };

    // 如果缩放比例大于适合屏幕的比例，则移除尺寸限制
    if (scale.value > fitToScreenScale.value && imageNaturalSize.value.width > 0) {
      baseStyle.maxWidth = "none";
      baseStyle.maxHeight = "none";
      baseStyle.width = `${imageNaturalSize.value.width}px`;
      baseStyle.height = `${imageNaturalSize.value.height}px`;
    }

    return baseStyle;
  });

  // 显示图片预览
  const showImagePreview = (url) => {
    previewImage.value = url;
    showPreview.value = true;

    // 创建临时图片元素来获取原始尺寸
    const img = new Image();
    img.onload = () => {
      imageNaturalSize.value = {
        width: img.naturalWidth,
        height: img.naturalHeight
      };

      // 计算适合屏幕的缩放比例
      const screenWidth = window.innerWidth * 0.9;
      const screenHeight = window.innerHeight * 0.9;
      const scaleX = screenWidth / img.naturalWidth;
      const scaleY = screenHeight / img.naturalHeight;
      fitToScreenScale.value = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

      // 初始缩放设为适合屏幕
      scale.value = fitToScreenScale.value;
      position.value = { x: 0, y: 0 };
    };
    img.src = url;
  };

  // 处理缩放
  const handleZoom = (e) => {
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newScale = scale.value + delta;
    if (newScale >= 0.1 && newScale <= 5) {
      scale.value = newScale;
    }
  };

  // 拖动相关方法
  const startDrag = (e) => {
    isDragging.value = true;
    dragStart.value = {
      x: e.clientX - position.value.x,
      y: e.clientY - position.value.y,
    };
  };

  const onDrag = (e) => {
    if (isDragging.value) {
      position.value = {
        x: e.clientX - dragStart.value.x,
        y: e.clientY - dragStart.value.y,
      };
    }
  };

  const stopDrag = () => {
    isDragging.value = false;
  };

  // 双击重置到原始尺寸
  const handleDoubleClick = () => {
    if (imageNaturalSize.value.width > 0) {
      scale.value = 1; // 原始尺寸
      position.value = { x: 0, y: 0 };
    }
  };

  // 重置到适合屏幕尺寸
  const resetToFitScreen = () => {
    scale.value = fitToScreenScale.value;
    position.value = { x: 0, y: 0 };
  };

  const pointsLabel = ["左上", "右上", "右下", "左下"];

  // 优化后的下载遮罩数据方法
  const downloadMaskData = async (url) => {
    if (downloading.value) return;

    try {
      downloading.value = true;
      const response = await fetch(url);

      // 检查响应是否成功
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 直接获取二进制数据
      const blob = await response.blob();

      // 从响应头或URL中获取文件名，如果没有则使用默认名称
      let filename = `mask_data_${Date.now()}`;

      // 尝试从Content-Disposition头获取文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      } else {
        // 尝试从URL路径获取文件名
        const urlPath = new URL(url, window.location.origin).pathname;
        const urlFilename = urlPath.split('/').pop();
        if (urlFilename && urlFilename.includes('.')) {
          filename = urlFilename;
        } else {
          // 根据Content-Type设置默认扩展名
          const contentType = response.headers.get('Content-Type');
          if (contentType) {
            if (contentType.includes('image/')) {
              filename += '.png'; // 默认图片格式
            } else if (contentType.includes('application/zip')) {
              filename += '.zip';
            } else if (contentType.includes('application/octet-stream')) {
              filename += '.bin';
            }
          }
        }
      }

      // 创建下载链接
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      link.click();

      // 清理资源
      URL.revokeObjectURL(downloadUrl);
      downloading.value = false;
    } catch (error) {
      downloading.value = false;
      messages.error("下载遮罩数据失败");
      console.error("下载遮罩数据失败:", error);
    }
  };

  // 根据置信度返回标签类型
  const getScoreType = (score) => {
    if (score >= 0.9) return "success";
    if (score >= 0.7) return "warning";
    return "error";
  };

  // 在组件挂载时打印结果数据，方便调试
  onMounted(() => {
    // 打印完整的结果数据
    console.log("ResultCards mounted with data:", {
      result: props.result,
      boxes: props.result.boxes,
      scores: props.result.scores,
      labels: props.result.labels,
      hasBoxes: props.result.boxes && props.result.boxes.length > 0,
      hasScores: props.result.scores && props.result.scores.length > 0,
      boxesType: props.result.boxes ? typeof props.result.boxes : "undefined",
      scoresType: props.result.scores ? typeof props.result.scores : "undefined",
    });

    // 如果有数据但不显示，打印每个索引的数据
    if (props.result.labels) {
      props.result.labels.forEach((label, index) => {
        console.log(`Data for index ${index}:`, {
          label,
          score: props.result.scores?.[index],
          box: props.result.boxes?.[index],
        });
      });
    }

    emit("mounted");
  });

  // 修改判断是否为对象的方法
  const isObject = (value) => {
    return value !== null && typeof value === "object" && !Array.isArray(value);
  };

  // 修改解析 JSON 字符串的方法
  const parseAnswer = (answer) => {
    if (typeof answer === "string") {
      try {
        return JSON.parse(answer);
      } catch (e) {
        console.error("JSON parse error:", e);
        return {};
      }
    }
    return answer || {};
  };

  // 检查是否有全局图片
  const hasGlobalImages = () => {
    return props.result.angeleImage || props.result.graspImage;
  };

  // 检查指定索引是否有裁剪图片
  const hasCroppedImages = (index) => {
    return (
      props.result.croppedImagesListAngle?.[index] ||
      props.result.croppedImagesListBbox?.[index] ||
      props.result.croppedImagesListGrasp?.[index] ||
      props.result.croppedImagesListPoint?.[index] ||
      props.result.croppedImagesListSegment?.[index]
    );
  };

  // 检查是否为3D抓取点格式
  const is3DGraspPoint = (graspPoint) => {
    if (!graspPoint || !Array.isArray(graspPoint)) return false;
    // 3D格式：长度为3，每个元素是包含一个数字的数组
    // 2D格式：长度为4，每个元素是包含两个数字的数组
    return graspPoint.length === 3 &&
      graspPoint.every(point => Array.isArray(point) && point.length === 1);
  };

  // 格式化数字显示
  const formatNumber = (num) => {
    if (num === null || num === undefined) return 'N/A';
    if (typeof num === 'number') {
      return num.toFixed(4);
    }
    return num;
  };

  // 获取对象对应的抓取信息
  const getGraspForObject = (objectIndex) => {
    if (!props.result.grasps || !Array.isArray(props.result.grasps)) {
      return null;
    }

    // 如果抓取点数量与标签数量相同，直接按索引对应
    if (props.result.grasps.length === props.result.labels?.length) {
      return props.result.grasps[objectIndex];
    }

    // 如果抓取点数量多于标签数量，返回第一个可用的抓取点
    // 这是一个简化的策略，实际应用中可能需要更复杂的匹配逻辑
    if (objectIndex < props.result.grasps.length) {
      return props.result.grasps[objectIndex];
    }

    return null;
  };

  return {
    showPreview,
    previewImage,
    scale,
    position,
    isDragging,
    dragStart,
    downloading,
    imageStyle,
    showImagePreview,
    handleZoom,
    startDrag,
    onDrag,
    stopDrag,
    handleDoubleClick,
    resetToFitScreen,
    pointsLabel,
    downloadMaskData,
    getScoreType,
    isObject,
    parseAnswer,
    imageNaturalSize,
    fitToScreenScale,
    hasGlobalImages,
    hasCroppedImages,
    is3DGraspPoint,
    formatNumber,
    getGraspForObject,
  };
}
