/**
 * 时间工具函数
 */

/**
 * 将时间转换为相对时间格式
 * @param {string|Date} time - 要转换的时间
 * @returns {string} 相对时间字符串，如 "5分钟前"、"2小时前"、"3天前"等
 */
export function formatRelativeTime(time) {
  if (!time) {
    return "从未登录";
  }

  const now = new Date();
  const targetTime = new Date(time);
  
  // 检查时间是否有效
  if (isNaN(targetTime.getTime())) {
    return "时间格式错误";
  }

  // 计算时间差（毫秒）
  const diffMs = now.getTime() - targetTime.getTime();
  
  // 如果是未来时间，显示具体时间
  if (diffMs < 0) {
    return targetTime.toLocaleString();
  }

  // 转换为秒
  const diffSeconds = Math.floor(diffMs / 1000);
  
  // 小于1分钟
  if (diffSeconds < 60) {
    return "刚刚";
  }
  
  // 转换为分钟
  const diffMinutes = Math.floor(diffSeconds / 60);
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  }
  
  // 转换为小时
  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24) {
    return `${diffHours}小时前`;
  }
  
  // 转换为天
  const diffDays = Math.floor(diffHours / 24);
  if (diffDays < 7) {
    return `${diffDays}天前`;
  }
  
  // 转换为周
  const diffWeeks = Math.floor(diffDays / 7);
  if (diffWeeks < 4) {
    return `${diffWeeks}周前`;
  }
  
  // 超过4周显示为"更早前"
  return "更早前";
}

/**
 * 格式化创建时间为标准格式
 * @param {string|Date} time - 要格式化的时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatCreateTime(time) {
  if (!time) {
    return "-";
  }

  const targetTime = new Date(time);
  
  // 检查时间是否有效
  if (isNaN(targetTime.getTime())) {
    return "时间格式错误";
  }

  return targetTime.toLocaleString();
}

/**
 * 获取详细的时间信息（用于悬浮提示）
 * @param {string|Date} time - 要转换的时间
 * @returns {string} 详细时间信息
 */
export function getDetailedTimeInfo(time) {
  if (!time) {
    return "从未登录";
  }

  const targetTime = new Date(time);
  
  // 检查时间是否有效
  if (isNaN(targetTime.getTime())) {
    return "时间格式错误";
  }

  const relativeTime = formatRelativeTime(time);
  const absoluteTime = targetTime.toLocaleString();
  
  // 如果相对时间就是绝对时间，只返回一个
  if (relativeTime === absoluteTime) {
    return absoluteTime;
  }
  
  return `${relativeTime} (${absoluteTime})`;
}
