/**
 * 时间工具函数测试
 * 这个文件用于测试时间转换功能
 */

import { formatRelativeTime, formatCreateTime, getDetailedTimeInfo } from './timeUtils.js';

// 测试函数
function runTests() {
  console.log('=== 时间工具函数测试 ===');
  
  const now = new Date();
  
  // 测试不同时间差
  const testCases = [
    { desc: '30秒前', time: new Date(now.getTime() - 30 * 1000) },
    { desc: '5分钟前', time: new Date(now.getTime() - 5 * 60 * 1000) },
    { desc: '2小时前', time: new Date(now.getTime() - 2 * 60 * 60 * 1000) },
    { desc: '3天前', time: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000) },
    { desc: '2周前', time: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000) },
    { desc: '2个月前', time: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000) },
    { desc: '空值', time: null },
    { desc: '无效时间', time: 'invalid-date' },
  ];
  
  testCases.forEach(({ desc, time }) => {
    console.log(`\n${desc}:`);
    console.log(`  输入: ${time}`);
    console.log(`  相对时间: ${formatRelativeTime(time)}`);
    console.log(`  创建时间: ${formatCreateTime(time)}`);
    console.log(`  详细信息: ${getDetailedTimeInfo(time)}`);
  });
  
  // 测试您提供的具体时间格式
  console.log('\n=== 测试具体时间格式 ===');
  const testTime = '2025-02-09 18:20:51';
  console.log(`输入时间: ${testTime}`);
  console.log(`相对时间: ${formatRelativeTime(testTime)}`);
  console.log(`创建时间: ${formatCreateTime(testTime)}`);
  console.log(`详细信息: ${getDetailedTimeInfo(testTime)}`);
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testTimeUtils = runTests;
  console.log('时间工具测试已加载，在控制台运行 testTimeUtils() 来测试');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests };
}
