import { createDiscrete<PERSON><PERSON> } from 'naive-ui'
import { h } from 'vue'
import { NAlert } from 'naive-ui'

// 定义不同类型消息的标题
const MESSAGE_TITLES = {
    success: '成功',
    info: '信息',
    warning: '警告',
    error: '错误'
}

// 统一的消息渲染函数
const renderMessage = (props) => {
    const { type } = props
    return h(
        NAlert,
        {
            closable: props.closable,
            onClose: props.onClose,
            type: type === 'loading' ? 'default' : type,
            title: MESSAGE_TITLES[type] || '系统提示',
            style: {
                boxShadow: 'var(--n-box-shadow)',
                maxWidth: 'calc(100vw - 32px)',
                minWidth: '250px',
                width: 'fit-content'
            }
        },
        {
            default: () => props.content
        }
    )
}

// 创建一个全局的消息 API
const { message } = createDiscreteApi(['message'], {
    configProviderProps: {
        theme: null,
        themeOverrides: {
            Message: {
                padding: '12px 20px',
                maxWidth: '420px',
                // 为每种类型设置对应的背景色和文字颜色
                successColorSuppl: 'rgba(63, 195, 128, 0.1)',
                successTextColor: '#18a058',
                infoColorSuppl: 'rgba(24, 160, 245, 0.1)',
                infoTextColor: '#2080f0',
                warningColorSuppl: 'rgba(250, 173, 20, 0.1)',
                warningTextColor: '#f0a020',
                errorColorSuppl: 'rgba(255, 0, 0, 0.1)',
                errorTextColor: '#ff0000'
            }
        }
    },
    messageProviderProps: {
        placement: 'top-right',
        duration: 3000,
        max: 3
    }
})

// 统一的消息配置
const defaultOptions = {
    closable: false,
    duration: 3000
}

export default {
    success(content, options = {}) {
        message.success(content, {
            render: renderMessage,
            ...defaultOptions,
            ...options
        })
    },
    info(content, options = {}) {
        message.info(content, {
            render: renderMessage,
            ...defaultOptions,
            ...options
        })
    },
    warning(content, options = {}) {
        message.warning(content, {
            render: renderMessage,
            ...defaultOptions,
            ...options
        })
    },
    error(content, options = {}) {
        message.error(content, {
            render: renderMessage,
            ...defaultOptions,
            ...options
        })
    }
}
