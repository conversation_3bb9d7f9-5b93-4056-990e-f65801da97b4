import axios from 'axios'
import message from '@/utils/messages'
import { createDiscreteApi } from 'naive-ui'
import eventBus from '@/utils/eventBus'

const { loadingBar } = createDiscreteApi(['loadingBar'])

const instance = axios.create({
	baseURL: '/open-api',
	timeout: 10000,
})

// 请求拦截器
instance.interceptors.request.use(
	(config) => {
		loadingBar.start() // 开始加载进度条

		// 检查请求头中是否已经包含 Authorization
		const hasAuthHeader = config.headers && (
			config.headers['Authorization'] ||
			config.headers.Authorization
		)

		// 如果已经有 Authorization 头，直接返回配置
		if (hasAuthHeader) {
			return config
		}

		// 到这里说明没有 Authorization 头，需要设置
		// 优先从 sessionStorage 获取 access_token（用于应用凭证）
		let token = localStorage.getItem('access_token') 
		config.headers['Authorization'] = token
		return config
	},
	(error) => {
		loadingBar.error() // 加载出错
		message.error('请求配置错误')
		return Promise.reject(error)
	}
)

// 响应拦截器
instance.interceptors.response.use(
	(response) => {
		loadingBar.finish() // 完成加载进度条
		if (response.data.code !== 0) {
			message.error(response.data.message || '请求失败')
			return Promise.reject(new Error(response.data.message || '网络错误-请稍后重试'))
		}
		// 返回包含 headers 的响应数据
		return {
			code: response.data.code,
			data: response.data.data,
			message: response.data.message,
			headers: response.headers
		}
	},
	(error) => {
		loadingBar.error() // 加载出错
		if (axios.isCancel(error)) {
			message.info('请求已取消')
		} else if (error.response) {
			switch (error.response.status) {
				case 400:
					message.error('用户输入错误:' + error.response.data.msg)
					break
				case 401:
					message.error(error.response.data.msg || '授权已过期，请重新登录')
					localStorage.removeItem('access_token')
					eventBus.emit('auth:logout') // 触发登出事件
					break
				case 403:
					message.error('拒绝访问:' + error.response.data.msg)
					break
				case 404:
					message.error('请求的资源不存在')
					break
				case 500:
					message.error('服务器内部错误')
					break
				case 503:
					message.error('服务暂时不可用，请稍后再试')
					break
				default:
					message.error(`连接错误 ${error.response.status}`)
			}
		} else if (error.request) {
			message.error('网络错误，请检查您的网络连接')
		} else {
			message.error('发生未知错误，请稍后重试')
		}
		return Promise.reject(error)
	}
)

export const doGet = (url, params, headers) => {
	const config = { params }
	if (headers) {
		config.headers = headers
	}
	return instance.get(url, config)
}

export const doPost = (url, data, headers) => {
	const config = {}
	if (headers) {
		config.headers = headers
	}
	return instance.post(url, data, config)
}

export const doPut = (url, data, headers) => {
	const config = {}
	if (headers) {
		config.headers = headers
	}
	return instance.put(url, data, config)
}

export const doDelete = (url, headers) => {
	const config = {}
	if (headers) {
		config.headers = headers
	}
	return instance.delete(url, config)
}

export default instance
