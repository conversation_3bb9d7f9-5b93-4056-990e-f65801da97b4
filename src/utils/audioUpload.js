import { getUploadParams } from '@/api/base'

/**
 * 上传音频文件到COS
 * @param {Blob} audioBlob - 音频Blob对象
 * @param {string} fileName - 文件名（可选）
 * @returns {Promise<string>} 返回上传后的文件key
 */
export async function uploadAudioToCOS(audioBlob, fileName = null) {
  if (!audioBlob) {
    throw new Error('音频数据不能为空')
  }

  try {
    // 获取上传参数
    const uploadParams = await getUploadParams()
    if (uploadParams.code !== 0) {
      console.error('获取上传参数失败:', uploadParams)
      throw new Error('获取上传参数失败')
    }

    // 生成文件名（如果没有提供）
    if (!fileName) {
      const timestamp = Date.now()
      const extension = getAudioExtension(audioBlob.type)
      fileName = `voice_${timestamp}.${extension}`
    }

    console.log('上传音频文件:', {
      fileName,
      size: audioBlob.size,
      type: audioBlob.type,
      url: uploadParams.data.url,
      key: uploadParams.data.key,
    })

    // 发送PUT请求到COS
    const response = await fetch(uploadParams.data.url, {
      method: 'PUT',
      headers: {
        'Content-Type': audioBlob.type || 'audio/wav',
      },
      body: audioBlob,
    })

    console.log('音频上传响应状态:', response.status)

    // 检查响应状态码
    if (response.status !== 200) {
      const responseText = await response.text()
      console.error('音频上传失败响应内容:', responseText)
      throw new Error(`音频上传失败: ${response.status} ${responseText}`)
    }

    // 返回文件key，用于后续通知服务器
    return uploadParams.data.key
  } catch (error) {
    console.error('音频上传出错:', error)
    throw error
  }
}

/**
 * 根据MIME类型获取文件扩展名
 * @param {string} mimeType - MIME类型
 * @returns {string} 文件扩展名
 */
function getAudioExtension(mimeType) {
  const mimeToExt = {
    'audio/webm': 'webm',
    'audio/webm;codecs=opus': 'webm',
    'audio/mp4': 'mp4',
    'audio/wav': 'wav',
    'audio/mpeg': 'mp3',
    'audio/ogg': 'ogg',
  }

  return mimeToExt[mimeType] || 'webm'
}

/**
 * 将录音帧数组合并为单个Blob
 * @param {Array} recordingFrames - 录音帧数组
 * @param {string} mimeType - MIME类型
 * @returns {Blob} 合并后的音频Blob
 */
export function mergeAudioFrames(recordingFrames, mimeType = 'audio/wav') {
  if (!recordingFrames || recordingFrames.length === 0) {
    throw new Error('录音数据为空')
  }

  return new Blob(recordingFrames, { type: mimeType })
}

/**
 * 检查音频文件大小
 * @param {Blob} audioBlob - 音频Blob对象
 * @param {number} maxSize - 最大文件大小（字节），默认10MB
 * @returns {boolean} 是否符合大小要求
 */
export function validateAudioSize(audioBlob, maxSize = 10 * 1024 * 1024) {
  return audioBlob.size <= maxSize
}

/**
 * 获取音频时长（需要创建Audio元素）
 * @param {Blob} audioBlob - 音频Blob对象
 * @returns {Promise<number>} 音频时长（秒）
 */
export function getAudioDuration(audioBlob) {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(audioBlob)

    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url)
      resolve(audio.duration)
    }

    audio.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('无法获取音频时长'))
    }

    audio.src = url
  })
}

/**
 * 音频格式转换（如果需要）
 * @param {Blob} audioBlob - 原始音频Blob
 * @param {string} targetMimeType - 目标MIME类型
 * @returns {Promise<Blob>} 转换后的音频Blob
 */
export async function convertAudioFormat(audioBlob, targetMimeType = 'audio/wav') {
  // 这里可以实现音频格式转换逻辑
  // 目前直接返回原始Blob，后续可以集成音频转换库
  console.log(`音频格式转换: ${audioBlob.type} -> ${targetMimeType}`)
  return audioBlob
}

/**
 * 生成音频文件的唯一标识
 * @param {string} prefix - 前缀
 * @returns {string} 唯一标识
 */
export function generateAudioId(prefix = 'audio') {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 上传视频文件到COS
 * @param {File} videoFile - 视频文件对象
 * @param {string} fileName - 文件名（可选）
 * @returns {Promise<string>} 返回上传后的文件key
 */
export async function uploadVideoToCOS(videoFile, fileName = null) {
  if (!videoFile) {
    throw new Error('视频文件不能为空')
  }

  try {
    // 获取上传参数
    const uploadParams = await getUploadParams()
    if (uploadParams.code !== 0) {
      console.error('获取上传参数失败:', uploadParams)
      throw new Error('获取上传参数失败')
    }

    // 生成文件名（如果没有提供）
    if (!fileName) {
      const timestamp = Date.now()
      const extension = getVideoExtension(videoFile.type)
      fileName = `video_${timestamp}.${extension}`
    }

    console.log('上传视频文件:', {
      fileName,
      size: videoFile.size,
      type: videoFile.type,
      url: uploadParams.data.url,
      key: uploadParams.data.key,
    })

    // 发送PUT请求到COS
    const response = await fetch(uploadParams.data.url, {
      method: 'PUT',
      headers: {
        'Content-Type': videoFile.type || 'video/mp4',
      },
      body: videoFile,
    })

    console.log('视频上传响应状态:', response.status)

    // 检查响应状态码
    if (response.status !== 200) {
      const responseText = await response.text()
      console.error('视频上传失败响应内容:', responseText)
      throw new Error(`视频上传失败: ${response.status} ${responseText}`)
    }

    // 返回文件key，用于后续通知服务器
    return uploadParams.data.key
  } catch (error) {
    console.error('视频上传出错:', error)
    throw error
  }
}

/**
 * 根据MIME类型获取视频文件扩展名
 * @param {string} mimeType - MIME类型
 * @returns {string} 文件扩展名
 */
function getVideoExtension(mimeType) {
  const mimeToExt = {
    'video/mp4': 'mp4',
    'video/webm': 'webm',
    'video/ogg': 'ogv',
    'video/avi': 'avi',
    'video/mov': 'mov',
    'video/wmv': 'wmv',
    'video/flv': 'flv',
    'video/mkv': 'mkv',
  }

  return mimeToExt[mimeType] || 'mp4'
}

/**
 * 检查视频文件大小
 * @param {File} videoFile - 视频文件对象
 * @param {number} maxSize - 最大文件大小（字节），默认1GB
 * @returns {boolean} 是否符合大小要求
 */
export function validateVideoSize(videoFile, maxSize = 1024 * 1024 * 1024) {
  return videoFile.size <= maxSize
}

/**
 * 获取视频时长
 * @param {File} videoFile - 视频文件对象
 * @returns {Promise<number>} 视频时长（秒）
 */
export function getVideoDuration(videoFile) {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const url = URL.createObjectURL(videoFile)

    video.onloadedmetadata = () => {
      URL.revokeObjectURL(url)
      resolve(video.duration)
    }

    video.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('无法获取视频时长'))
    }

    video.src = url
  })
}

/**
 * 上传图片文件到COS
 * @param {File} imageFile - 图片文件对象
 * @param {string} fileName - 文件名（可选）
 * @returns {Promise<string>} 返回上传后的文件key
 */
export async function uploadImageToCOS(imageFile, fileName = null) {
  if (!imageFile) {
    throw new Error('图片文件不能为空')
  }

  try {
    // 获取上传参数
    const uploadParams = await getUploadParams()
    if (uploadParams.code !== 0) {
      console.error('获取上传参数失败:', uploadParams)
      throw new Error('获取上传参数失败')
    }

    // 生成文件名（如果没有提供）
    if (!fileName) {
      const timestamp = Date.now()
      const extension = getImageExtension(imageFile.type)
      fileName = `image_${timestamp}.${extension}`
    }

    console.log('上传图片文件:', {
      fileName,
      size: imageFile.size,
      type: imageFile.type,
      url: uploadParams.data.url,
      key: uploadParams.data.key,
    })

    // 发送PUT请求到COS
    const response = await fetch(uploadParams.data.url, {
      method: 'PUT',
      headers: {
        'Content-Type': imageFile.type || 'image/jpeg',
      },
      body: imageFile,
    })

    console.log('图片上传响应状态:', response.status)

    // 检查响应状态码
    if (response.status !== 200) {
      const responseText = await response.text()
      console.error('图片上传失败响应内容:', responseText)
      throw new Error(`图片上传失败: ${response.status} ${responseText}`)
    }

    // 返回文件key，用于后续通知服务器
    return uploadParams.data.key
  } catch (error) {
    console.error('图片上传出错:', error)
    throw error
  }
}

/**
 * 根据MIME类型获取图片文件扩展名
 * @param {string} mimeType - MIME类型
 * @returns {string} 文件扩展名
 */
function getImageExtension(mimeType) {
  const mimeToExt = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'image/bmp': 'bmp',
    'image/svg+xml': 'svg',
    'image/tiff': 'tiff',
  }

  return mimeToExt[mimeType] || 'jpg'
}

/**
 * 检查图片文件大小
 * @param {File} imageFile - 图片文件对象
 * @param {number} maxSize - 最大文件大小（字节），默认5MB
 * @returns {boolean} 是否符合大小要求
 */
export function validateImageSize(imageFile, maxSize = 5 * 1024 * 1024) {
  return imageFile.size <= maxSize
}

/**
 * 生成视频文件的唯一标识
 * @param {string} prefix - 前缀
 * @returns {string} 唯一标识
 */
export function generateVideoId(prefix = 'video') {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}_${timestamp}_${random}`
}
