import { ref, computed, nextTick } from 'vue'
import { useMessage } from 'naive-ui'

export function useCamera(videoElementRef, outputVideoElementRef, isPlayingRef, playbackUrlRef) {
  const message = useMessage()

  // 响应式状态
  const isStreaming = ref(false)
  const isStarting = ref(false)
  const isRecognizing = ref(false)
  const isStartingRecognition = ref(false)
  const errorMessage = ref('')
  const statusMessage = ref('')
  const streamInfo = ref(null)

  // 内部状态
  let mediaStream = null
  let srsPublisher = null
  let srsPlayer = null
  let currentStreamUrl = ''
  let currentPlaybackUrl = ''

  // SRS服务器配置
  const SRS_SERVERS = {
    local: {
      baseUrl: 'http://localhost:1985',
      app: 'live',
      whipPath: '/rtc/v1/whip/',
      secret: null // 本地服务器通常不需要密钥
    },
    remote: {
      baseUrl: 'https://issac.qj-robots.com',
      app: 'live',
      whipPath: '/rtc/v1/whip/',
      secret: '016bb6a2fa5c41daa5ce96dd710d2290' // 远程服务器密钥
    },
    demo: {
      baseUrl: 'https://d.ossrs.net',
      app: 'live',
      whipPath: '/rtc/v1/whip/',
      secret: null // 演示服务器通常不需要密钥
    }
  }

  // 当前使用的服务器配置（可以根据需要切换）
  const currentServer = 'remote' // 'local', 'remote', 'demo' - 切换到远程服务器
  const SRS_CONFIG = {
    ...SRS_SERVERS[currentServer],
    streamName: 'camera_recognition' // 固定流名称，不添加时间戳
  }

  // 获取推流URL
  const getStreamUrl = () => {
    let url = `${SRS_CONFIG.baseUrl}${SRS_CONFIG.whipPath}?app=${SRS_CONFIG.app}&stream=${SRS_CONFIG.streamName}`

    // 如果配置了密钥，添加secret参数
    if (SRS_CONFIG.secret) {
      url += `&secret=${SRS_CONFIG.secret}`
    }

    return url
  }

  // 获取播放URL (WHEP协议)
  const getPlaybackUrl = () => {
    const whepPath = SRS_CONFIG.whipPath.replace('/whip/', '/whep/')
    let url = `${SRS_CONFIG.baseUrl}${whepPath}?app=${SRS_CONFIG.app}&stream=${SRS_CONFIG.streamName}`

    // 如果配置了密钥，添加secret参数
    if (SRS_CONFIG.secret) {
      //url += `&secret=${SRS_CONFIG.secret}`
    }

    return url
  }

  // 清除错误信息
  const clearError = () => {
    errorMessage.value = ''
  }

  // 设置错误信息
  const setError = (error) => {
    console.error('Camera error:', error)
    errorMessage.value = typeof error === 'string' ? error : error.message || '未知错误'
    statusMessage.value = ''
  }

  // 设置状态信息
  const setStatus = (status) => {
    statusMessage.value = status
    clearError()
  }

  // 开启摄像头
  const startCamera = async () => {
    if (isStarting.value || isStreaming.value) return

    try {
      isStarting.value = true
      clearError()
      setStatus('正在申请摄像头权限...')

      // 请求摄像头权限
      const constraints = {
        video: {
          width: { ideal: 640, max: 1280 },
          height: { ideal: 640, max: 1280 },
          facingMode: 'user' // 前置摄像头
        },
        audio: true // 同时获取音频
      }

      mediaStream = await navigator.mediaDevices.getUserMedia(constraints)

      // 等待下一个tick确保DOM已更新
      await nextTick()

      if (videoElementRef && videoElementRef.value) {
        videoElementRef.value.srcObject = mediaStream
        await videoElementRef.value.play()

        isStreaming.value = true
        setStatus('摄像头已开启')
        message.success('摄像头开启成功')

        // 更新流信息
        const videoTrack = mediaStream.getVideoTracks()[0]
        const settings = videoTrack.getSettings()

        streamInfo.value = {
          url: '本地摄像头',
          resolution: `${settings.width}x${settings.height}`,
          status: 'connected',
          statusText: '已连接'
        }
      } else {
        throw new Error('视频元素未找到')
      }

    } catch (error) {
      console.error('启动摄像头失败:', error)

      if (error.name === 'NotAllowedError') {
        setError('摄像头权限被拒绝，请在浏览器设置中允许访问摄像头')
      } else if (error.name === 'NotFoundError') {
        setError('未找到摄像头设备')
      } else if (error.name === 'NotReadableError') {
        setError('摄像头被其他应用占用')
      } else {
        setError('启动摄像头失败: ' + error.message)
      }

      message.error('摄像头启动失败')

      // 清理资源
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
      }
    } finally {
      isStarting.value = false
    }
  }

  // 关闭摄像头
  const stopCamera = () => {
    try {
      // 先停止识别
      if (isRecognizing.value) {
        stopRecognition()
      }

      // 停止媒体流
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop())
        mediaStream = null
      }

      // 清空视频元素
      if (videoElementRef && videoElementRef.value) {
        videoElementRef.value.srcObject = null
      }

      isStreaming.value = false
      streamInfo.value = null
      setStatus('摄像头已关闭')
      message.info('摄像头已关闭')

    } catch (error) {
      console.error('关闭摄像头失败:', error)
      setError('关闭摄像头失败: ' + error.message)
    }
  }

  // 开始识别（推流到SRS）
  const startRecognition = async () => {
    if (!isStreaming.value || isStartingRecognition.value || isRecognizing.value) return

    try {
      isStartingRecognition.value = true
      clearError()
      setStatus('正在连接SRS服务器...')

      // 检查SRS SDK是否可用
      if (typeof SrsRtcWhipWhepAsync === 'undefined') {
        throw new Error('SRS RTC SDK未加载，请确保srs-rtc-sdk.js已正确引入')
      }

      // 创建SRS发布者
      srsPublisher = new SrsRtcWhipWhepAsync()

      // 设置媒体约束 - 使用与本地摄像头相同的约束
      srsPublisher.constraints = {
        audio: true,
        video: {
          width: { ideal: 640, max: 1280 },
          height: { ideal: 640, max: 1280 },
          facingMode: 'user'
        }
      }

      // 获取推流URL
      currentStreamUrl = getStreamUrl()

      setStatus('正在建立WebRTC连接...')

      // 开始推流 - SRS会自动获取媒体流
      await srsPublisher.publish(currentStreamUrl, {
        videoOnly: false,
        audioOnly: false
      })

      isRecognizing.value = true
      setStatus('识别已开始，正在推流到服务器')
      message.success('开始推流识别')

      // 更新流信息 - 显示播放地址而不是推流地址
      if (streamInfo.value) {
        streamInfo.value.url = getPlaybackUrl()
        streamInfo.value.status = 'connected'
        streamInfo.value.statusText = '推流中'
      }

      // 延迟启动播放，等待推流稳定
      setTimeout(() => {
        startPlayback()
      }, 2000)

    } catch (error) {
      console.error('开始识别失败:', error)

      let errorMsg = '连接服务器失败'
      if (error.message.includes('502')) {
        errorMsg = 'SRS服务器不可用 (502错误)，请检查服务器配置'
      } else if (error.message.includes('WHIP')) {
        errorMsg = 'WHIP协议错误，请检查推流URL格式'
      } else if (error.message.includes('network')) {
        errorMsg = '网络连接失败，请检查网络设置'
      } else {
        errorMsg = '连接服务器失败: ' + error.message
      }

      setError(errorMsg)
      message.error('开始识别失败')

      // 清理SRS发布者
      if (srsPublisher) {
        try {
          srsPublisher.close()
        } catch (e) {
          console.error('关闭SRS发布者失败:', e)
        }
        srsPublisher = null
      }
    } finally {
      isStartingRecognition.value = false
    }
  }

  // 开始播放流
  const startPlayback = async () => {
    if (!outputVideoElementRef || !outputVideoElementRef.value) {
      console.warn('输出视频元素未找到')
      return
    }

    try {
      // 检查SRS SDK是否可用
      if (typeof SrsRtcWhipWhepAsync === 'undefined') {
        throw new Error('SRS RTC SDK未加载')
      }

      // 创建SRS播放器
      srsPlayer = new SrsRtcWhipWhepAsync()

      // 获取播放URL
      currentPlaybackUrl = getPlaybackUrl()

      console.log('开始播放流:', currentPlaybackUrl)

      // 开始播放
      await srsPlayer.play(currentPlaybackUrl, {
        videoOnly: false,
        audioOnly: false
      })

      // 将流绑定到视频元素
      if (srsPlayer.stream && outputVideoElementRef.value) {
        outputVideoElementRef.value.srcObject = srsPlayer.stream
        await outputVideoElementRef.value.play()

        if (isPlayingRef) {
          isPlayingRef.value = true
        }

        if (playbackUrlRef) {
          playbackUrlRef.value = currentPlaybackUrl
        }

        console.log('播放成功')
      }

    } catch (error) {
      console.error('播放失败:', error)
      // 播放失败不影响推流，只是记录错误
    }
  }

  // 停止播放
  const stopPlayback = () => {
    try {
      if (srsPlayer) {
        srsPlayer.close()
        srsPlayer = null
      }

      if (outputVideoElementRef && outputVideoElementRef.value) {
        outputVideoElementRef.value.srcObject = null
      }

      if (isPlayingRef) {
        isPlayingRef.value = false
      }

      if (playbackUrlRef) {
        playbackUrlRef.value = ''
      }

    } catch (error) {
      console.error('停止播放失败:', error)
    }
  }

  // 停止识别
  const stopRecognition = () => {
    try {
      // 先停止播放
      stopPlayback()

      if (srsPublisher) {
        srsPublisher.close()
        srsPublisher = null
      }

      isRecognizing.value = false
      setStatus('识别已停止')
      message.info('识别已停止')

      // 更新流信息
      if (streamInfo.value) {
        streamInfo.value.url = '本地摄像头'
        streamInfo.value.status = 'connected'
        streamInfo.value.statusText = '已连接'
      }

    } catch (error) {
      console.error('停止识别失败:', error)
      setError('停止识别失败: ' + error.message)
    }
  }

  // 清理所有资源
  const cleanup = () => {
    try {
      stopRecognition()
      stopCamera()
    } catch (error) {
      console.error('清理资源失败:', error)
    }
  }

  // 获取当前推流URL
  const getCurrentStreamUrl = () => {
    return currentStreamUrl
  }

  return {
    // 响应式数据
    isStreaming,
    isStarting,
    isRecognizing,
    isStartingRecognition,
    errorMessage,
    statusMessage,
    streamInfo,

    // 方法
    startCamera,
    stopCamera,
    startRecognition,
    stopRecognition,
    startPlayback,
    stopPlayback,
    cleanup,
    getCurrentStreamUrl
  }
}
