import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import { voiceChatConfig, checkBrowserSupport, getSupportedAudioFormats } from '@/config/voice-chat.js'
import { uploadAudioToCOS, uploadImageToCOS, mergeAudioFrames, validateAudioSize, getAudioDuration, generateAudioId } from '@/utils/audioUpload.js'
import { voiceChatApi, TaskStatus, ResponseType } from '@/api/voiceChat.js'

export function useVoiceChatCOS(options = {}) {
  // 从选项中获取连接ID，默认为空
  const connId = options.connId || ''

  // 响应式数据
  const connectionStatus = ref('disconnected')
  const isRecording = ref(false)
  const recordingState = ref('idle') // idle, waiting, recording, stopping, uploading
  const currentVolume = ref(0)
  const threshold = ref(voiceChatConfig.recording.defaultThreshold)
  const patience = ref(voiceChatConfig.recording.defaultPatience)
  const messages = ref([])
  const currentTask = ref(null) // 当前任务信息
  const isProcessing = ref(false) // 是否正在处理任务
  const playingAudioId = ref(null) // 当前正在播放的音频消息ID
  const currentImageKey = ref(null) // 当前图片的key
  const currentImageFile = ref(null) // 当前选择的图片文件

  // 音频相关变量
  let mediaRecorder = null
  let audioContext = null
  let analyser = null
  let microphone = null
  let dataArray = null
  let recordingFrames = []
  let silenceCounter = 0
  let animationId = null
  let pollingTimer = null
  let recordingStartTime = null

  const message = useMessage()

  // 辅助函数：为URL添加conn_id参数
  const addConnIdToUrl = (url) => {
    if (!connId) return url

    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}conn_id=${connId}`
  }

  // 计算属性
  const canStartRecording = computed(() => {
    return connectionStatus.value === 'connected' && !isRecording.value && !isProcessing.value
  })

  const recordingDuration = computed(() => {
    if (!recordingStartTime || recordingState.value === 'idle') return 0
    return Math.floor((Date.now() - recordingStartTime) / 1000)
  })

  // 检查服务器连接状态
  const checkConnection = async () => {
    try {
      // 使用带有conn_id参数的健康检查接口
      const url = addConnIdToUrl('/open-apis/voice/chat/health')
      const result = await voiceChatApi.healthCheck(url)
      if (result.code === 0) {
        connectionStatus.value = 'connected'
        return true
      } else {
        connectionStatus.value = 'disconnected'
        return false
      }
    } catch (error) {
      console.error('健康检查失败:', error)
      connectionStatus.value = 'disconnected'
      return false
    }
  }

  // 连接到服务器
  const connect = async () => {
    try {
      // 检查浏览器支持
      const browserSupport = checkBrowserSupport()
      if (!browserSupport.isSupported) {
        message.error(`浏览器不支持以下功能: ${browserSupport.unsupported.join(', ')}`)
        return
      }

      addDebugMessage('system', '正在连接服务器...', 'status')

      const isConnected = await checkConnection()
      if (isConnected) {
        addDebugMessage('system', '服务器连接成功', 'status')
      } else {
        addDebugMessage('system', '服务器连接失败', 'status')
        message.error('连接失败，请检查网络或稍后重试')
      }
    } catch (error) {
      addDebugMessage('system', '连接失败: ' + error.message, 'status')
      message.error('连接失败: ' + error.message)
    }
  }

  // 初始化音频
  const initAudio = async () => {
    try {
      // 获取支持的音频格式
      const supportedFormats = getSupportedAudioFormats()
      if (supportedFormats.length === 0) {
        throw new Error('浏览器不支持音频录制')
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: voiceChatConfig.audio,
      })

      audioContext = new AudioContext({
        sampleRate: voiceChatConfig.audio.sampleRate,
      })

      analyser = audioContext.createAnalyser()
      analyser.fftSize = voiceChatConfig.analysis.fftSize
      analyser.smoothingTimeConstant = voiceChatConfig.analysis.smoothingTimeConstant

      microphone = audioContext.createMediaStreamSource(stream)
      microphone.connect(analyser)

      dataArray = new Uint8Array(analyser.frequencyBinCount)

      // 创建 MediaRecorder，使用支持的格式
      const mimeType = supportedFormats[0] || voiceChatConfig.recording.mimeType
      mediaRecorder = new MediaRecorder(stream, {
        mimeType: mimeType,
      })

      addDebugMessage('system', `使用音频格式: ${mimeType}`, 'status')

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordingFrames.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        handleRecordingComplete()
      }

      return true
    } catch (error) {
      message.error('音频初始化失败: ' + error.message)
      return false
    }
  }

  // 开始录音
  const startRecording = async () => {
    // 在开始新的录音前，先取消可能存在的旧任务
    await cancelCurrentTask('开始新录音')

    if (!await initAudio()) return

    isRecording.value = true
    recordingState.value = 'waiting'
    recordingFrames = []
    silenceCounter = 0
    recordingStartTime = Date.now()

    updateRecordingStatus()
    monitorVolume()

    addDebugMessage('user', '开始监听语音输入...', 'debug')
  }

  // 停止录音
  const stopRecording = () => {
    isRecording.value = false
    recordingState.value = 'idle'

    // 停止录音
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop() // 这会触发 onstop 事件，进而调用 handleRecordingComplete
    }

    if (animationId) {
      cancelAnimationFrame(animationId)
      animationId = null
    }

    currentVolume.value = 0
    recordingStartTime = null
    updateRecordingStatus()

    addDebugMessage('user', '录音已停止，准备上传...', 'debug')
  }

  // 音量监测
  const monitorVolume = () => {
    if (!isRecording.value || !analyser) return

    analyser.getByteFrequencyData(dataArray)

    // 计算平均音量
    let sum = 0
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i]
    }
    currentVolume.value = sum / dataArray.length

    // 检查录音时长限制
    const duration = recordingDuration.value
    if (duration >= voiceChatConfig.recording.maxDuration) {
      addDebugMessage('system', '录音时长已达上限，自动停止', 'status')
      stopRecording()
      return
    }

    // 根据音量阈值控制录音状态（但不自动提交）
    if (currentVolume.value > threshold.value) {
      if (recordingState.value === 'waiting') {
        recordingState.value = 'recording'
        mediaRecorder.start(voiceChatConfig.recording.chunkSize)
        addDebugMessage('user', '检测到语音，开始录音...', 'debug')
        updateRecordingStatus()
      }
      silenceCounter = 0
    } else {
      if (recordingState.value === 'recording') {
        silenceCounter++
        if (silenceCounter >= patience.value) {
          // 只是标记为暂停状态，不停止MediaRecorder
          recordingState.value = 'paused'
          addDebugMessage('user', '检测到静音，录音已暂停。继续说话或点击停止按钮', 'debug')
          updateRecordingStatus()
        }
      } else if (recordingState.value === 'paused') {
        // 如果在暂停状态下又检测到声音，恢复录音
        if (currentVolume.value > threshold.value) {
          recordingState.value = 'recording'
          addDebugMessage('user', '恢复录音...', 'debug')
          updateRecordingStatus()
          silenceCounter = 0
        }
      }
    }

    animationId = requestAnimationFrame(monitorVolume)
  }

  // 处理录音完成
  const handleRecordingComplete = async () => {
    if (recordingFrames.length === 0) {
      addDebugMessage('system', '录音数据为空，请重新录音', 'status')
      return
    }

    try {
      recordingState.value = 'uploading'
      updateRecordingStatus()

      // 合并音频帧
      const audioBlob = mergeAudioFrames(recordingFrames, mediaRecorder.mimeType)

      // 验证文件大小
      if (!validateAudioSize(audioBlob, voiceChatConfig.recording.maxFileSize)) {
        throw new Error('音频文件过大，请缩短录音时长')
      }

      // 获取音频时长
      const duration = await getAudioDuration(audioBlob)

      addDebugMessage('user', `正在上传音频文件 (${Math.round(audioBlob.size / 1024)}KB, ${duration.toFixed(1)}s)`, 'debug')

      // 串行上传音频和图片（如果有）
      let audioKey, imageKey = null

      if (currentImageFile.value) {
        addDebugMessage('user', '上传图片和音频...', 'debug')
        // 串行上传，先上传图片再上传音频，避免并发请求导致URL冲突
        imageKey = await uploadImageToCOS(currentImageFile.value)
        addDebugMessage('user', '图片上传成功，正在上传音频...', 'debug')
        audioKey = await uploadAudioToCOS(audioBlob)
        addDebugMessage('user', '音频和图片上传成功，提交处理任务...', 'debug')
      } else {
        // 只上传音频
        audioKey = await uploadAudioToCOS(audioBlob)
        addDebugMessage('user', '音频上传成功，提交处理任务...', 'debug')
      }

      // 创建音频URL用于播放
      const audioUrl = URL.createObjectURL(audioBlob)

      // 构建用户语音消息的metadata
      const messageMetadata = {
        isVoice: true,
        duration: duration,
        audioUrl: audioUrl,
        audioBlob: audioBlob,
        size: Math.round(audioBlob.size / 1024)
      }

      // 如果有图片，添加图片信息
      if (currentImageFile.value) {
        messageMetadata.hasImage = true
        messageMetadata.imageUrl = URL.createObjectURL(currentImageFile.value)
        messageMetadata.imageName = currentImageFile.value.name
        messageMetadata.imageSize = Math.round(currentImageFile.value.size / 1024)
        messageMetadata.imageType = currentImageFile.value.type
      }

      // 添加用户语音消息到聊天框
      addMessage('user', '', 'user', messageMetadata)

      // 提交处理任务（使用刚上传的图片key）
      await submitVoiceTask(audioKey, audioBlob.type, duration, imageKey)

      // 任务提交成功后，清空图片文件（但保留消息中的图片预览）
      currentImageFile.value = null

    } catch (error) {
      console.error('处理录音失败:', error)
      message.error('处理录音失败: ' + error.message)
      addDebugMessage('system', '处理录音失败: ' + error.message, 'status')

      // 处理失败时重置状态
      recordingState.value = 'idle'
      updateRecordingStatus()
    } finally {
      // 清空录音帧数据
      recordingFrames = []
      silenceCounter = 0
    }
  }

  // 提交语音处理任务
  const submitVoiceTask = async (audioKey, audioFormat, duration, imageKey = null) => {
    try {
      isProcessing.value = true

      const taskId = generateAudioId('task')
      const url = addConnIdToUrl('/open-apis/voice/chat/submit')

      // 构建请求参数
      const params = {
        audioKey,
        audioFormat,
        duration,
        metadata: {
          threshold: threshold.value,
          patience: patience.value,
          timestamp: Date.now()
        }
      }

      // 如果有图片key，添加到参数中
      if (imageKey) {
        params.imageKey = imageKey
      }

      const result = await voiceChatApi.submitVoiceTask(params, url)

      if (result.code === 0) {
        currentTask.value = {
          taskId: result.data.taskId || taskId,
          status: 'pending',
          submitTime: new Date(),
          audioKey
        }

        addDebugMessage('system', `任务已提交 (ID: ${currentTask.value.taskId})`, 'status')

        // 添加loading状态的服务器消息
        const loadingMessageId = generateAudioId('loading')
        addMessage('server', '正在思考中...', 'server', {
          isLoading: true,
          loadingMessageId: loadingMessageId
        })

        // 开始轮询任务状态
        startTaskPolling()
      } else {
        throw new Error(result.message || '提交任务失败')
      }
    } catch (error) {
      console.error('提交任务失败:', error)
      message.error('提交任务失败: ' + error.message)
      addDebugMessage('system', '提交任务失败: ' + error.message, 'status')
      isProcessing.value = false
    }
  }

  // 开始任务轮询
  const startTaskPolling = () => {
    if (!currentTask.value) return

    let pollingCount = 0
    const maxPolling = 60 // 60秒超时
    let hasReceivedFirstResponse = false // 标记是否已收到第一次回复
    const startTime = Date.now() // 记录开始时间

    pollingTimer = setInterval(async () => {
      try {
        pollingCount++
        const elapsedTime = (Date.now() - startTime) / 1000 // 已经过的时间（秒）

        // 检查是否超过60秒
        if (elapsedTime > 60) {
          clearInterval(pollingTimer)
          pollingTimer = null
          currentTask.value.status = 'finished'
          addDebugMessage('system', '轮询已停止（60秒）', 'status')
          isProcessing.value = false

          // 调用取消任务接口
          await cancelCurrentTask('超时')

          // 移除loading消息并显示超时提示
          const timeoutLoadingIndex = messages.value.findIndex(msg => msg.isLoading)
          if (timeoutLoadingIndex !== -1) {
            messages.value.splice(timeoutLoadingIndex, 1)
          }

          // 添加超时消息
          addMessage('server', 'Task timeout, please retry later', 'server')

          return
        }

        const url = addConnIdToUrl(`/open-apis/voice/chat/result?taskId=${currentTask.value.taskId}`)
        const result = await voiceChatApi.getTaskResult(currentTask.value.taskId, url)

        if (result.code === 0 && result.data && Array.isArray(result.data) && result.data.length > 0) {
          // 处理新的数据格式：data是一个数组
          await handleTaskComplete(result.data)
          hasReceivedFirstResponse = true

          // 继续轮询等待可能的后续消息
          addDebugMessage('system', `收到${result.data.length}条消息，继续轮询等待可能的后续消息...`, 'debug')
        } else if (result.code === 0 && (!result.data || (Array.isArray(result.data) && result.data.length === 0))) {
          // data为空时忽略，继续轮询
          addDebugMessage('system', 'data为空，继续轮询...', 'debug')
        } else {
          // 如果已经收到过回复，API返回错误可能意味着没有更多消息了
          if (hasReceivedFirstResponse) {
            addDebugMessage('system', '没有更多消息，停止轮询', 'debug')
            clearInterval(pollingTimer)
            pollingTimer = null
            isProcessing.value = false

            // 调用取消任务接口
            await cancelCurrentTask('没有更多消息')

            // 移除可能残留的loading消息
            const noMoreLoadingIndex = messages.value.findIndex(msg => msg.isLoading)
            if (noMoreLoadingIndex !== -1) {
              messages.value.splice(noMoreLoadingIndex, 1)
            }
          }
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
        // 如果已经收到过回复，网络错误可能意味着没有更多消息了
        if (hasReceivedFirstResponse) {
          addDebugMessage('system', '网络错误，可能没有更多消息，停止轮询', 'debug')
          clearInterval(pollingTimer)
          pollingTimer = null
          isProcessing.value = false

          // 调用取消任务接口
          await cancelCurrentTask('网络错误')

          // 移除loading消息
          const networkErrorLoadingIndex = messages.value.findIndex(msg => msg.isLoading)
          if (networkErrorLoadingIndex !== -1) {
            messages.value.splice(networkErrorLoadingIndex, 1)
          }
        }
      }
    }, voiceChatConfig.communication.http.pollingInterval)
  }

  // 处理任务完成
  const handleTaskComplete = async (dataArray) => {
    try {
      // 注意：不要设置 isProcessing.value = false，因为可能还有更多消息

      // 处理新的数据格式：dataArray是一个数组
      if (Array.isArray(dataArray)) {
        let lastVoiceMessageId = null // 记录最后一个语音消息的ID
        let hasValidResponse = false // 标记是否有有效回复

        // 按顺序处理每个数据项
        for (const item of dataArray) {
          // 只处理status为completed的项
          if (item.status === 'completed') {
            // 处理音频回复（如果有audioResponseKey）
            if (item.audioResponseKey) {
              hasValidResponse = true // 标记有有效回复
              addDebugMessage('server', `收到音频回复: ${item.audioResponseKey}`, 'debug')
              // 添加服务器语音消息到聊天框，先设置为0时长
              const messageId = generateAudioId('msg')
              lastVoiceMessageId = messageId // 记录语音消息ID

              messages.value.push({
                id: messageId,
                sender: 'server',
                content: '',
                type: 'server',
                timestamp: new Date(),
                isVoice: true,
                duration: 0, // 先设置为0，稍后获取真实时长
                audioUrl: item.audioResponseKey, // 直接使用HTTPS URL
                size: null, // 服务器语音消息不显示大小
                transcription: null // 语音转文字内容，稍后可能会设置
              })

              // 自动滚动到底部
              nextTick(() => {
                const container = document.querySelector('.messages-wrapper')
                if (container) {
                  container.scrollTop = container.scrollHeight
                }
              })

              // 异步获取音频时长并更新消息
              getRemoteAudioDuration(item.audioResponseKey).then(duration => {
                const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
                if (messageIndex !== -1) {
                  messages.value[messageIndex].duration = duration
                }
              }).catch(error => {
                addDebugMessage('server', `获取音频时长失败: ${error.message}`, 'debug')
              })
            }

            // 处理文本回复（过滤掉无效文本和"success"）
            if (item.responseText &&
              item.responseText.trim() !== '' &&
              item.responseText !== '.' &&
              item.responseText.toLowerCase() !== 'success') {

              // 检查是否收到"stop"消息，如果是则立即中止轮询
              if (item.responseText.trim().toLowerCase() === 'stop') {
                addDebugMessage('system', '收到stop消息，立即中止轮询', 'debug')
                // 立即停止轮询
                if (pollingTimer) {
                  clearInterval(pollingTimer)
                  pollingTimer = null
                  isProcessing.value = false
                }
                // 移除loading消息
                const loadingMessageIndex = messages.value.findIndex(msg => msg.isLoading)
                if (loadingMessageIndex !== -1) {
                  messages.value.splice(loadingMessageIndex, 1)
                }
                return // 直接返回，不再处理后续消息
              }

              hasValidResponse = true // 标记有有效回复

              // 如果前面有语音消息，将此文本作为语音转文字
              if (lastVoiceMessageId) {
                const voiceMessageIndex = messages.value.findIndex(msg => msg.id === lastVoiceMessageId)
                if (voiceMessageIndex !== -1) {
                  messages.value[voiceMessageIndex].transcription = item.responseText
                  addDebugMessage('server', `语音转文字: ${item.responseText}`, 'debug')
                }
                lastVoiceMessageId = null // 重置，避免影响后续消息
              } else {
                // 没有语音消息，正常添加文本消息
                addMessage('server', item.responseText, 'server')
                addDebugMessage('server', `收到回复: ${item.responseText}`, 'debug')
              }

              if (item.recognizedText) {
                addDebugMessage('server', `识别文本: ${item.recognizedText}`, 'debug')
              }
            }
          }
        }

        // 只有在收到有效回复时才移除loading状态
        if (hasValidResponse) {
          const loadingMessageIndex = messages.value.findIndex(msg => msg.isLoading)
          if (loadingMessageIndex !== -1) {
            messages.value.splice(loadingMessageIndex, 1)
            addDebugMessage('system', '收到有效回复，移除loading状态', 'debug')
          }
        }
      }

    } catch (error) {
      console.error('处理任务结果失败:', error)
      message.error('处理任务结果失败: ' + error.message)

      // 移除loading消息并显示错误
      const loadingMessageIndex = messages.value.findIndex(msg => msg.isLoading)
      if (loadingMessageIndex !== -1) {
        messages.value.splice(loadingMessageIndex, 1)
      }
      addMessage('server', '处理失败，请稍后重试。', 'server')
    }
  }

  // 播放音频回复
  const playAudioResponse = async (audioData) => {
    try {
      let audioBlob

      if (typeof audioData === 'string') {
        // 如果是URL，直接创建Audio元素
        const audio = new Audio(audioData)
        audio.onended = () => {
          addDebugMessage('server', '音频播放完成', 'debug')
        }
        await audio.play()
        return
      } else {
        // 如果是二进制数据，创建Blob
        audioBlob = new Blob([audioData], { type: 'audio/wav' })
      }

      const audioUrl = URL.createObjectURL(audioBlob)
      const audio = new Audio(audioUrl)

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl)
        addDebugMessage('server', '音频播放完成', 'debug')
      }

      await audio.play()

    } catch (error) {
      message.error('音频播放失败: ' + error.message)
    }
  }

  // 取消当前任务（内部辅助函数）
  const cancelCurrentTask = async (reason = '手动取消') => {
    if (currentTask.value && currentTask.value.taskId) {
      try {
        addDebugMessage('system', `正在取消任务: ${currentTask.value.taskId} (${reason})`, 'status')
        const url = addConnIdToUrl('/open-apis/voice/chat/cancel')
        const result = await voiceChatApi.cancelTask(currentTask.value.taskId, url)

        if (result.code === 0) {
          addDebugMessage('system', '任务取消成功', 'status')
          currentTask.value.status = 'cancelled'
        } else {
          addDebugMessage('system', `任务取消失败: ${result.message || '未知错误'}`, 'status')
        }
      } catch (error) {
        console.error('取消任务失败:', error)
        addDebugMessage('system', `取消任务失败: ${error.message}`, 'status')
      }
    } else {
      // 没有当前任务时的日志
      addDebugMessage('system', `尝试取消任务 (${reason})，但没有活跃任务`, 'debug')
    }
  }

  // 停止任务轮询
  const stopTaskPolling = async () => {
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
      isProcessing.value = false
      addDebugMessage('system', '手动停止轮询', 'status')

      // 调用取消任务接口
      await cancelCurrentTask('手动停止')

      // 移除可能残留的loading消息
      const loadingMessageIndex = messages.value.findIndex(msg => msg.isLoading)
      if (loadingMessageIndex !== -1) {
        messages.value.splice(loadingMessageIndex, 1)
      }
    }
  }

  // 切换录音状态
  const toggleRecording = () => {
    if (isRecording.value) {
      stopRecording()
    } else {
      startRecording()
    }
  }

  // 添加消息到聊天框
  const addMessage = (sender, content, type, metadata = {}) => {
    messages.value.push({
      id: generateAudioId('msg'),
      sender,
      content,
      type,
      timestamp: new Date(),
      ...metadata
    })

    // 自动滚动到底部
    nextTick(() => {
      const container = document.querySelector('.messages-wrapper')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    })
  }

  // 添加调试信息（只输出到控制台）
  const addDebugMessage = (sender, content, type) => {
    const timestamp = new Date().toLocaleTimeString()
    console.log(`[${timestamp}] [${type.toUpperCase()}] ${sender}: ${content}`)
  }

  // 清空消息
  const clearMessages = () => {
    messages.value = []
  }

  // 设置图片key
  const setImageKey = (imageKey) => {
    currentImageKey.value = imageKey
  }

  // 清空图片key
  const clearImageKey = () => {
    currentImageKey.value = null
    currentImageFile.value = null
  }

  // 设置图片文件
  const setImageFile = (imageFile) => {
    currentImageFile.value = imageFile
  }

  // 获取状态文本
  const getStatusText = () => {
    switch (recordingState.value) {
      case 'waiting': return '等待语音输入...'
      case 'recording': return '正在录音'
      case 'paused': return '录音已暂停'
      case 'stopping': return '录音结束'
      case 'uploading': return '上传中...'
      default: return '空闲'
    }
  }

  // 格式化时间
  const formatTime = (date) => {
    return date.toLocaleTimeString()
  }
  // 播放用户录制的音频
  const playUserAudio = async (audioUrl, messageId) => {
    try {
      // 如果已经在播放这个音频，则停止播放
      if (playingAudioId.value === messageId) {
        playingAudioId.value = null
        return
      }

      playingAudioId.value = messageId
      const audio = new Audio(audioUrl)

      audio.onended = () => {
        playingAudioId.value = null
        addDebugMessage('user', '用户音频播放完成', 'debug')
      }

      audio.onerror = () => {
        playingAudioId.value = null
      }

      await audio.play()
    } catch (error) {
      playingAudioId.value = null
      console.error('播放用户音频失败:', error)
      message.error('播放失败: ' + error.message)
    }
  }

  // 获取远程音频时长
  const getRemoteAudioDuration = (audioUrl) => {
    return new Promise((resolve, reject) => {
      const audio = new Audio()

      audio.onloadedmetadata = () => {
        resolve(audio.duration)
      }

      audio.onerror = () => {
        reject(new Error('无法加载音频文件'))
      }

      audio.ontimeout = () => {
        reject(new Error('加载音频超时'))
      }

      // 设置超时
      setTimeout(() => {
        if (audio.readyState === 0) {
          reject(new Error('获取音频时长超时'))
        }
      }, 10000) // 10秒超时

      audio.src = audioUrl
    })
  }

  // 播放服务器返回的音频
  const playServerAudio = async (audioUrl, messageId) => {
    try {
      // 如果已经在播放这个音频，则停止播放
      if (playingAudioId.value === messageId) {
        playingAudioId.value = null
        return
      }

      playingAudioId.value = messageId
      addDebugMessage('server', `开始播放服务器音频: ${audioUrl}`, 'debug')
      const audio = new Audio(audioUrl)

      audio.onloadstart = () => {
        addDebugMessage('server', '开始加载音频...', 'debug')
      }

      audio.oncanplay = () => {
        addDebugMessage('server', '音频可以播放', 'debug')
      }

      audio.onended = () => {
        playingAudioId.value = null
        addDebugMessage('server', '服务器音频播放完成', 'debug')
      }

      audio.onerror = (error) => {
        playingAudioId.value = null
        console.error('音频播放错误:', error)
        addDebugMessage('server', '音频播放失败', 'debug')
      }

      await audio.play()
    } catch (error) {
      playingAudioId.value = null
      console.error('播放服务器音频失败:', error)
      message.error('播放服务器音频失败: ' + error.message)
      addDebugMessage('server', `播放失败: ${error.message}`, 'debug')
    }
  }





  // 更新录音状态显示
  const updateRecordingStatus = () => {
    // 这个方法可以被外部调用来更新UI状态
  }

  // 生命周期
  onMounted(async () => {
    // 组件加载时先取消可能存在的旧任务
    await cancelCurrentTask('组件加载')
    connect()
  })

  onUnmounted(async () => {
    stopRecording()
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
      isProcessing.value = false
      // 调用取消任务接口
      await cancelCurrentTask('组件卸载')
    }
    if (audioContext) {
      audioContext.close()
    }
  })

  return {
    // 响应式数据
    connectionStatus,
    isRecording,
    recordingState,
    currentVolume,
    threshold,
    patience,
    messages,
    currentTask,
    isProcessing,
    playingAudioId,

    // 计算属性
    canStartRecording,
    recordingDuration,

    // 方法
    connect,
    toggleRecording,
    startRecording,
    stopRecording,
    stopTaskPolling,
    addMessage,
    clearMessages,
    getStatusText,
    formatTime,
    playUserAudio,
    playServerAudio,
    updateRecordingStatus,
    setImageKey,
    clearImageKey,
    setImageFile
  }
}
