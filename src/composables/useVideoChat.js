import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import { uploadVideoToCOS, validateVideoSize, getVideoDuration, generateVideoId } from '@/utils/audioUpload.js'
import { videoChatApi, TaskStatus, ResponseType } from '@/api/videoChat.js'

export function useVideoChat() {
  // 响应式数据
  const isUploading = ref(false)
  const isProcessing = ref(false)
  const messages = ref([])
  const currentTask = ref(null)

  // 表单数据
  const videoFile = ref(null)
  const videoUrl = ref('')
  const hours = ref(0)
  const minutes = ref(0)
  const seconds = ref(0)
  const question = ref('')

  // 视频缓存相关
  const cachedVideoKey = ref(null)
  const cachedVideoFile = ref(null)

  // 消息实例
  const message = useMessage()

  // 轮询相关
  let pollingTimer = null
  const pollingInterval = 1000 // 100ms轮询一次
  const maxPollingTime = 30000 // 最大轮询时间（30秒）
  let pollingStartTime = 0
  const isPolling = ref(false)
  const isTimeout = ref(false)

  // 计算属性
  const canSubmit = computed(() => {
    return videoFile.value && (hours.value > 0 || minutes.value > 0 || seconds.value > 0) && question.value.trim() && !isUploading.value && !isProcessing.value
  })

  const formattedTimestamp = computed(() => {
    const h = String(hours.value).padStart(2, '0')
    const m = String(minutes.value).padStart(2, '0')
    const s = String(seconds.value).padStart(2, '0')
    return `${h}:${m}:${s}`
  })

  const uploadProgress = computed(() => {
    if (isUploading.value) return '上传中...'
    if (isProcessing.value) return '处理中...'
    return ''
  })

  // 添加消息到聊天记录
  const addMessage = (type, content, messageType = 'text', extra = {}) => {
    const messageId = generateVideoId('msg')
    const newMessage = {
      id: messageId,
      type, // 'user' | 'server' | 'system'
      content,
      messageType, // 'text' | 'video' | 'status'
      timestamp: new Date().toLocaleTimeString(),
      extra,
      ...extra
    }
    messages.value.push(newMessage)

    // 自动滚动到底部（新消息在底部）
    nextTick(() => {
      const container = document.querySelector('.messages-wrapper')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    })

    return messageId
  }

  // 处理视频文件选择
  const handleVideoUpload = ({ file }) => {
    const videoFileObj = file.file

    // 验证文件类型
    if (!videoFileObj.type.startsWith('video/')) {
      message.error('请选择视频文件')
      return false
    }

    // 验证文件大小（1GB限制）
    if (!validateVideoSize(videoFileObj, 1024 * 1024 * 1024)) {
      message.error('视频文件大小不能超过1GB')
      return false
    }

    // 检查是否是同一个文件，如果是则不需要清除缓存
    const isSameFile = cachedVideoFile.value &&
      cachedVideoFile.value.name === videoFileObj.name &&
      cachedVideoFile.value.size === videoFileObj.size &&
      cachedVideoFile.value.lastModified === videoFileObj.lastModified

    if (!isSameFile) {
      // 新文件，清除缓存
      cachedVideoKey.value = null
      cachedVideoFile.value = videoFileObj
    }

    videoFile.value = videoFileObj
    videoUrl.value = URL.createObjectURL(videoFileObj)

    addMessage('system', `已选择视频文件: ${videoFileObj.name} (${Math.round(videoFileObj.size / 1024 / 1024)}MB)`, 'status')

    return false // 阻止默认上传行为
  }

  // 移除视频文件
  const removeVideo = () => {
    if (videoUrl.value) {
      URL.revokeObjectURL(videoUrl.value)
    }
    videoFile.value = null
    videoUrl.value = ''
    // 清除缓存
    cachedVideoKey.value = null
    cachedVideoFile.value = null
    addMessage('system', '已移除视频文件', 'status')
  }

  // 格式化时间戳（已由计算属性替代，保留用于兼容性）
  const formatTimestamp = (timeStr) => {
    if (typeof timeStr === 'string') {
      // 确保格式为 HH:MM:SS
      const parts = timeStr.split(':')
      if (parts.length === 1) {
        return `00:00:${parts[0].padStart(2, '0')}`
      } else if (parts.length === 2) {
        return `00:${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}`
      } else if (parts.length === 3) {
        return `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}:${parts[2].padStart(2, '0')}`
      }
      return timeStr
    }
    return formattedTimestamp.value
  }

  // 提交视频问答任务
  const submitVideoTask = async () => {
    if (!canSubmit.value) {
      message.warning('请完整填写所有信息')
      return
    }

    try {
      isUploading.value = true

      // 获取视频时长
      const duration = await getVideoDuration(videoFile.value)

      addMessage('user', '', 'video', {
        isVideo: true,
        videoUrl: videoUrl.value,
        question: question.value,
        timestamp: formattedTimestamp.value,
        duration: duration,
        size: Math.round(videoFile.value.size / 1024 / 1024)
      })

      // 上传视频到COS
      const videoKey = await uploadVideoToCOS(videoFile.value)

      isUploading.value = false
      isProcessing.value = true

      addMessage('system', '视频上传成功，正在处理问答...', 'status')

      // 提交处理任务
      const result = await videoChatApi.submitVideoTask({
        videoKey,
        videoFormat: videoFile.value.type,
        duration,
        question: question.value,
        timestamp: formattedTimestamp.value,
        metadata: {
          fileName: videoFile.value.name,
          fileSize: videoFile.value.size,
          submitTime: Date.now()
        }
      })

      if (result.code === 0) {
        currentTask.value = result.data
        startTaskPolling(result.data.taskId)
        addMessage('system', '任务提交成功，等待处理结果...', 'status')
      } else {
        throw new Error(result.message || '任务提交失败')
      }

    } catch (error) {
      console.error('提交视频任务失败:', error)
      message.error('提交失败: ' + error.message)
      addMessage('system', '任务提交失败: ' + error.message, 'status')

      isUploading.value = false
      isProcessing.value = false
    }
  }

  // 开始任务轮询（旧接口兼容）
  const startTaskPolling = (taskId) => {
    let pollingCount = 0
    const maxPollingCount = 150 // 最大轮询次数（5分钟）

    pollingTimer = setInterval(async () => {
      try {
        pollingCount++

        if (pollingCount > maxPollingCount) {
          stopTaskPolling()
          message.error('任务处理超时')
          addMessage('system', '任务处理超时，请重试', 'status')
          return
        }

        const result = await videoChatApi.getTaskResult(taskId)

        if (result.code === 0) {
          const taskData = result.data

          if (taskData.status === TaskStatus.COMPLETED) {
            stopTaskPolling()
            isProcessing.value = false

            // 添加服务器回复消息
            addMessage('server', taskData.result || '处理完成', 'text')
            message.success('问答处理完成')

          } else if (taskData.status === TaskStatus.FAILED) {
            stopTaskPolling()
            isProcessing.value = false

            const errorMsg = taskData.error || '处理失败'
            addMessage('server', errorMsg, 'text')
            message.error('处理失败: ' + errorMsg)
          }
          // 其他状态继续轮询
        }

      } catch (error) {
        console.error('轮询任务状态失败:', error)
        // 轮询失败不中断，继续尝试
      }
    }, 2000) // 旧接口使用2秒轮询
  }

  // 停止任务轮询
  const stopTaskPolling = () => {
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
    isPolling.value = false
  }

  // 清空聊天记录
  const clearMessages = () => {
    messages.value = []
    addMessage('system', '聊天记录已清空', 'status')
  }

  // 重置表单
  const resetForm = () => {
    removeVideo()
    hours.value = 0
    minutes.value = 0
    seconds.value = 0
    question.value = ''
    // 清除缓存
    cachedVideoKey.value = null
    cachedVideoFile.value = null
    addMessage('system', '表单已重置', 'status')
  }

  // 表单验证
  const validateForm = () => {
    if (!videoFile.value) {
      message.error('请选择视频文件')
      return false
    }

    if (hours.value === 0 && minutes.value === 0 && seconds.value === 0) {
      message.error('请输入有效的时间点')
      return false
    }

    if (!question.value || !question.value.trim()) {
      message.error('请输入问题内容')
      return false
    }

    return true
  }

  // 开始轮询结果
  const startPolling = () => {
    if (isPolling.value) return

    isPolling.value = true
    isTimeout.value = false
    pollingStartTime = Date.now()

    addMessage('system', '正在获取问答结果...', 'status')

    pollingTimer = setInterval(async () => {
      try {
        const currentTime = Date.now()
        const elapsedTime = currentTime - pollingStartTime

        // 检查是否超时
        if (elapsedTime >= maxPollingTime) {
          stopPolling()
          isTimeout.value = true
          isProcessing.value = false
          addMessage('system', '问答超时，请点击重试按钮重新获取结果', 'status')
          message.error('问答超时，请重试')
          return
        }

        // 轮询结果
        const result = await videoChatApi.pollVideoChatResult()

        if (result && result.data && result.data.trim()) {
          // 获取到结果
          stopPolling()
          isProcessing.value = false
          addMessage('server', result.data, 'text')
          message.success('问答完成')
        }

      } catch (error) {
        console.error('轮询结果失败:', error)
        // 继续轮询，不中断
      }
    }, pollingInterval)
  }

  // 停止轮询
  const stopPolling = () => {
    if (pollingTimer) {
      clearInterval(pollingTimer)
      pollingTimer = null
    }
    isPolling.value = false
  }

  // 重试轮询
  const retryPolling = () => {
    if (!isTimeout.value) return

    isTimeout.value = false
    isProcessing.value = true
    startPolling()
  }

  // 提交视频聊天（使用新接口）
  const submitVideoChat = async () => {
    // 表单验证
    if (!validateForm()) {
      return
    }

    // 防止重复提交
    if (isUploading.value || isProcessing.value) {
      message.warning('正在处理中，请稍候...')
      return
    }

    try {
      isTimeout.value = false

      // 添加用户消息
      addMessage('user', '', 'video', {
        isVideo: true,
        videoUrl: videoUrl.value,
        question: question.value,
        timestamp: formattedTimestamp.value,
        duration: await getVideoDuration(videoFile.value).catch(() => 0),
        size: Math.round(videoFile.value.size / 1024 / 1024)
      })

      let videoKey = cachedVideoKey.value

      // 检查是否需要重新上传视频
      if (!videoKey || !cachedVideoFile.value ||
        cachedVideoFile.value.name !== videoFile.value.name ||
        cachedVideoFile.value.size !== videoFile.value.size ||
        cachedVideoFile.value.lastModified !== videoFile.value.lastModified) {

        // 需要上传视频
        isUploading.value = true
        addMessage('system', '正在上传视频...', 'status')

        // 上传视频到COS
        videoKey = await uploadVideoToCOS(videoFile.value)

        // 缓存上传结果
        cachedVideoKey.value = videoKey
        cachedVideoFile.value = videoFile.value

        isUploading.value = false
        addMessage('system', '视频上传成功，正在提交问答...', 'status')
      } else {
        // 使用缓存的视频
        addMessage('system', '使用已上传的视频，正在提交问答...', 'status')
      }

      isProcessing.value = true

      // 调用新的视频聊天接口
      const result = await videoChatApi.submitVideoChat({
        video: videoKey,
        timestamp: formattedTimestamp.value,
        question: question.value.trim()
      })

      console.log('视频聊天接口响应:', result)

      // API 层面已经处理了状态码检查，如果到这里说明成功
      addMessage('system', '问答提交成功，正在处理...', 'status')
      message.success('问答提交成功')

      // 开始轮询结果
      startPolling()

    } catch (error) {
      console.error('提交视频聊天失败:', error)
      message.error('提交失败: ' + error.message)
      addMessage('system', '提交失败: ' + error.message, 'status')

      isUploading.value = false
      isProcessing.value = false
    }
  }

  // Mock数据（用于测试）
  const mockSubmit = async () => {
    if (!canSubmit.value) {
      message.warning('请完整填写所有信息')
      return
    }

    try {
      isProcessing.value = true

      // 添加用户消息
      addMessage('user', '', 'video', {
        isVideo: true,
        videoUrl: videoUrl.value,
        question: question.value,
        timestamp: formattedTimestamp.value,
        duration: 120, // Mock时长
        size: Math.round(videoFile.value.size / 1024 / 1024)
      })

      addMessage('system', '正在处理问答...', 'status')

      // 模拟处理延迟
      setTimeout(() => {
        isProcessing.value = false

        // Mock回复
        const mockResponses = [
          '根据视频内容，在您指定的时间点，我看到了一个人正在进行演示操作。',
          '在该时间段，视频中显示的是产品介绍的关键部分，主要展示了核心功能。',
          '从视频的这个时间点来看，正在展示的是用户界面的操作流程。',
          '在您询问的时间点，视频内容显示的是技术实现的详细说明。'
        ]

        const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]
        addMessage('server', randomResponse, 'text')
        message.success('问答处理完成（Mock数据）')

      }, 2000)

    } catch (error) {
      console.error('Mock提交失败:', error)
      message.error('提交失败: ' + error.message)
      isProcessing.value = false
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopPolling()
    stopTaskPolling()
    if (videoUrl.value) {
      URL.revokeObjectURL(videoUrl.value)
    }
    // 清除缓存
    cachedVideoKey.value = null
    cachedVideoFile.value = null
  })

  // 初始化
  onMounted(() => {
    // 确保时分秒输入框显示默认值
    if (hours.value === null || hours.value === undefined) hours.value = 0
    if (minutes.value === null || minutes.value === undefined) minutes.value = 0
    if (seconds.value === null || seconds.value === undefined) seconds.value = 0

    addMessage('system', '视频问答助手已就绪', 'status')
  })

  return {
    // 响应式数据
    isUploading,
    isProcessing,
    messages,
    videoFile,
    videoUrl,
    hours,
    minutes,
    seconds,
    question,
    isPolling,
    isTimeout,

    // 计算属性
    canSubmit,
    uploadProgress,
    formattedTimestamp,

    // 方法
    handleVideoUpload,
    removeVideo,
    submitVideoTask,
    submitVideoChat,
    retryPolling,
    clearMessages,
    resetForm,
    mockSubmit,
    stopTaskPolling,
    stopPolling
  }
}
