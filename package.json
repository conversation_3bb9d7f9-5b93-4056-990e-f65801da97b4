{"name": "qj-open-platform", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:prod": "vite build --mode production", "build:uat": "vite build --mode production", "build:sit": "vite build --mode production"}, "dependencies": {"@vicons/antd": "^0.12.0", "@vicons/carbon": "^0.12.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vicons/tabler": "^0.12.0", "@wecom/jssdk": "^1.3.1", "axios": "^1.7.7", "crypto-js": "^4.2.0", "hls.js": "^1.6.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "naive-ui": "^2.40.1", "path": "^0.12.7", "pinia": "^2.2.4", "socket.io-client": "^4.8.1", "vue": "^3.5.10", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "sass-embedded": "^1.85.1", "unplugin-auto-import": "^0.18.3", "vite": "^5.4.8"}}