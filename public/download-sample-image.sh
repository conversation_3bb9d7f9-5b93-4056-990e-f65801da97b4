#!/bin/bash

echo "正在下载示例全景图片..."

# 创建images目录
mkdir -p images

# 下载示例全景图片（来自免费资源）
# 这是一个小尺寸的示例图片，用于测试
curl -L -o images/panorama.jpg "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Equirectangular_projection_SW.jpg/1024px-Equirectangular_projection_SW.jpg"

# 检查下载是否成功
if [ -f "images/panorama.jpg" ] && [ -s "images/panorama.jpg" ]; then
    echo "✅ 示例全景图片下载成功！"
    echo "📁 文件位置: images/panorama.jpg"
    echo "🌐 现在可以打开 3d-simple.html 或 3d.html 查看效果"
else
    echo "❌ 下载失败，请手动下载全景图片"
    echo "💡 建议："
    echo "   1. 搜索 '360 panorama equirectangular' 下载免费全景图"
    echo "   2. 或使用手机全景模式拍摄"
    echo "   3. 将图片重命名为 panorama.jpg 并放入 images/ 目录"
fi

echo ""
echo "📋 全景图片要求："
echo "   - 格式: JPG/PNG"
echo "   - 宽高比: 2:1 (如 4096x2048)"
echo "   - 投影方式: 等距圆柱投影"
