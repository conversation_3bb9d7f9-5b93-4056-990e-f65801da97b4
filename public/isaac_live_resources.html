<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Player</title>
    <style>
        body, html { margin: 0; padding: 0; height: 100%; }
        .video-container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
            background-color: #000;
        }
        #background-video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translate(-50%, -50%);
            object-fit: cover;
        }
    </style>
    <script src="srs-rtc-sdk.min.js"></script>
</head>
<body>
    <div class="video-container">
        <video id="background-video" autoplay muted playsinline style="opacity: 0; position: fixed;"></video>
    </div>

<script>
const sdk = new SrsRtcWhipWhepAsync();
const streamUrl = 'https://issac.qj-robots.com/rtc/v1/whep/?app=live&stream=isaac_livestream';

sdk.play(streamUrl, {
    videoOnly: false,
    audioOnly: false
}).then(() => {
    const videoElement = document.getElementById('background-video');
    videoElement.srcObject = sdk.stream;
    videoElement.style.opacity = '1';
    videoElement.style.transition = 'opacity 0.5s ease-in-out';
}).catch(error => {
    console.error('播放失败:', error);
});

// 处理浏览器自动播放策略
document.addEventListener('DOMContentLoaded', () => {
    const videoElement = document.getElementById('background-video');
    videoElement.play().catch(error => {
        console.log('自动播放被阻止，尝试静音播放');
        videoElement.muted = true;
        videoElement.play();
    });
});
</script>
</body>
</html>