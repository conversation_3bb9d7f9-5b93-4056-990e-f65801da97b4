<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频问答功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #18a058;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .test-link:hover {
            background: #16a085;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-list li:before {
            content: "✓";
            color: #18a058;
            font-weight: bold;
            margin-right: 8px;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.implemented {
            background: #e8f5e8;
            color: #18a058;
        }

        .status.mock {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>视频问答功能测试页面</h1>
            <p>测试VideoChat组件的各项功能</p>
        </div>

        <div class="test-section">
            <div class="test-title">
                🎥 视频问答主页面
                <span class="status implemented">已实现</span>
            </div>
            <div class="test-description">
                完整的视频问答界面，包含左右布局、文件上传、时间输入、问题输入和结果展示。
                <br><strong>最新优化：</strong>
                <br>✅ 上传区域图标和文案完美居中对齐（使用深层CSS选择器）
                <br>✅ 时分秒输入框默认填充0，显示加减号按钮
                <br>✅ 时分秒输入框数字水平居中显示
                <br>✅ 时分秒输入框上方显示标签（时、分、秒）
                <br>✅ 消息布局优化：用户消息左侧，系统回复右侧
                <br>✅ 视频文件大小限制提升至1GB
            </div>
            <a href="/video-chat" class="test-link" target="_blank">打开视频问答页面</a>
        </div>

        <div class="test-section">
            <div class="test-title">
                📋 功能特性清单
            </div>
            <ul class="feature-list">
                <li>左右布局设计 (左3右7) <span class="status implemented">✓</span></li>
                <li>视频文件上传 (支持MP4、WebM等格式，最大1GB) <span class="status implemented">✓</span></li>
                <li>独立时分秒输入框 (带标签，默认填充0，带加减号) <span class="status implemented">✓</span></li>
                <li>多行文本问题输入 (最大500字符) <span class="status implemented">✓</span></li>
                <li>气泡式消息展示 (用户消息左侧，系统回复右侧) <span class="status implemented">✓</span></li>
                <li>视频预览功能 <span class="status implemented">✓</span></li>
                <li>文件大小和时长显示 <span class="status implemented">✓</span></li>
                <li>上传区域图标和文案居中对齐 <span class="status implemented">✓</span></li>
                <li>响应式设计 (支持移动端) <span class="status implemented">✓</span></li>
                <li>Mock数据测试 <span class="status mock">Mock</span></li>
                <li>COS文件上传 <span class="status implemented">✓</span></li>
                <li>API接口集成 <span class="status mock">待开发</span></li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                🧪 测试步骤
            </div>
            <div class="test-description">
                <ol>
                    <li>点击上方链接打开视频问答页面</li>
                    <li>上传一个视频文件 (建议使用小于100MB的MP4文件，最大支持1GB)</li>
                    <li>在时分秒输入框中设置时间点 (如: 时=0, 分=1, 秒=30)</li>
                    <li>输入问题内容</li>
                    <li>点击"提交问答"按钮</li>
                    <li>观察右侧消息展示区域的反馈</li>
                    <li>测试清空记录和重置表单功能</li>
                    <li>验证上传区域的图标和文案是否居中对齐</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                ⚙️ 技术实现
            </div>
            <div class="test-description">
                <ul class="feature-list">
                    <li>Vue 3 + Composition API</li>
                    <li>Naive UI 组件库</li>
                    <li>COS云存储上传</li>
                    <li>响应式设计</li>
                    <li>TypeScript支持</li>
                    <li>模块化组合式函数</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                📝 注意事项
            </div>
            <div class="test-description">
                <ul>
                    <li>当前使用Mock数据进行测试，实际API接口需要后端配合开发</li>
                    <li>视频文件上传到COS的功能已实现，但需要有效的上传凭证</li>
                    <li>建议使用Chrome或Firefox浏览器进行测试</li>
                    <li>移动端测试请使用开发者工具的设备模拟功能</li>
                </ul>
            </div>
        </div>
    </div>
</body>

</html>