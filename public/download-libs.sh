#!/bin/bash

# 创建目录
mkdir -p js css

echo "正在下载 Photo Sphere Viewer 文件..."

# 下载 JavaScript 文件
echo "下载 JS 文件..."
curl -L -o js/photo-sphere-viewer.min.js "https://unpkg.com/photo-sphere-viewer@4.8.1/dist/photo-sphere-viewer.min.js"

# 下载 CSS 文件
echo "下载 CSS 文件..."
curl -L -o css/photo-sphere-viewer.min.css "https://unpkg.com/photo-sphere-viewer@4.8.1/dist/photo-sphere-viewer.min.css"

# 检查文件是否下载成功
if [ -f "js/photo-sphere-viewer.min.js" ] && [ -s "js/photo-sphere-viewer.min.js" ]; then
    echo "✅ JS 文件下载成功"
else
    echo "❌ JS 文件下载失败"
fi

if [ -f "css/photo-sphere-viewer.min.css" ] && [ -s "css/photo-sphere-viewer.min.css" ]; then
    echo "✅ CSS 文件下载成功"
else
    echo "❌ CSS 文件下载失败"
fi

echo "下载完成！"
echo "现在可以在 3d.html 中取消注释本地文件的引用行"
