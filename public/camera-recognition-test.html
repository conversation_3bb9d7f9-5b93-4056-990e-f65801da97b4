<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>摄像头识别功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #18a058;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .test-link:hover {
            background: #16a085;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .feature-list li:before {
            content: "✓";
            color: #18a058;
            font-weight: bold;
            margin-right: 8px;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.implemented {
            background: #e8f5e8;
            color: #18a058;
        }

        .status.testing {
            background: #fff3cd;
            color: #856404;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 12px;
            margin: 15px 0;
            color: #856404;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>摄像头识别功能测试页面</h1>
            <p>测试CameraRecognition组件的各项功能</p>
        </div>

        <div class="test-section">
            <div class="test-title">
                📹 摄像头识别主页面
                <span class="status implemented">已实现</span>
            </div>
            <div class="test-description">
                完整的摄像头识别界面，包含正方形视频显示区域、摄像头控制按钮和WebRTC推流功能。
                <br><strong>主要特性：</strong>
                <br>✅ 渐变背景设计，美观的UI界面
                <br>✅ 双视频窗口布局（本地摄像头 + 输出流播放）
                <br>✅ 摄像头权限申请和错误处理
                <br>✅ 实时状态指示器和推流信息显示
                <br>✅ WebRTC推流到SRS服务器（WHIP协议）
                <br>✅ 自动订阅播放推送的流（WHEP协议）
                <br>✅ 显示订阅流地址和播放状态
                <br>✅ 响应式设计，支持移动端
            </div>
            <a href="/camera-recognition" class="test-link" target="_blank">打开摄像头识别页面</a>
        </div>

        <div class="test-section">
            <div class="test-title">
                📋 功能特性清单
            </div>
            <ul class="feature-list">
                <li>渐变背景设计 <span class="status implemented">✓</span></li>
                <li>双视频窗口布局 (本地摄像头 + 输出流) <span class="status implemented">✓</span></li>
                <li>摄像头权限申请 <span class="status implemented">✓</span></li>
                <li>实时视频流显示 <span class="status implemented">✓</span></li>
                <li>状态指示器 (连接/识别/播放状态) <span class="status implemented">✓</span></li>
                <li>WebRTC推流到SRS服务器 (WHIP) <span class="status implemented">✓</span></li>
                <li>自动订阅播放推送流 (WHEP) <span class="status implemented">✓</span></li>
                <li>推流密钥支持 (secret参数) <span class="status implemented">✓</span></li>
                <li>订阅流地址显示 <span class="status implemented">✓</span></li>
                <li>错误处理和用户提示 <span class="status implemented">✓</span></li>
                <li>响应式设计 (支持移动端) <span class="status implemented">✓</span></li>
                <li>资源清理和内存管理 <span class="status implemented">✓</span></li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                🧪 测试步骤
            </div>
            <div class="test-description">
                <ol>
                    <li>点击上方链接打开摄像头识别页面</li>
                    <li>观察页面显示两个视频窗口：左侧"本地摄像头"，右侧"输出流播放"</li>
                    <li>点击"开启摄像头"按钮</li>
                    <li>在浏览器弹出的权限请求中选择"允许"</li>
                    <li>确认摄像头画面正常显示在左侧窗口中</li>
                    <li>观察左侧状态指示器显示"已连接"</li>
                    <li>点击"开始识别"按钮开始推流</li>
                    <li>等待2秒后，右侧窗口应自动开始播放推送的流</li>
                    <li>观察右侧窗口下方显示的订阅流地址</li>
                    <li>测试"停止识别"和"关闭摄像头"功能</li>
                </ol>
            </div>

            <div class="warning">
                <strong>注意：</strong> WebRTC推流功能需要SRS服务器支持。如果服务器不可用，推流功能可能会失败，但摄像头本地显示功能应该正常工作。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                ⚙️ 技术实现
            </div>
            <div class="test-description">
                <ul class="feature-list">
                    <li>Vue 3 + Composition API</li>
                    <li>Naive UI 组件库</li>
                    <li>WebRTC getUserMedia API</li>
                    <li>SRS WebRTC SDK (WHIP协议)</li>
                    <li>响应式设计</li>
                    <li>模块化组合式函数</li>
                    <li>错误处理和状态管理</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                🔧 SRS服务器配置
            </div>
            <div class="test-description">
                <p><strong>可用的服务器配置：</strong></p>
                <ul>
                    <li><strong>本地服务器</strong>: http://localhost:1985 (无需密钥)</li>
                    <li><strong>远程服务器 (当前)</strong>: https://issac.qj-robots.com (需要密钥)</li>
                    <li><strong>演示服务器</strong>: https://d.ossrs.net (无需密钥)</li>
                </ul>
                <p><strong>配置说明：</strong></p>
                <ul>
                    <li>应用名称: live</li>
                    <li>推流协议: WHIP (WebRTC-HTTP Ingestion Protocol)</li>
                    <li>推流路径: /rtc/v1/whip/</li>
                    <li>推流密钥: secret=016bb6a2fa5c41daa5ce96dd710d2290 (远程服务器)</li>
                </ul>
                <p><strong>如需切换服务器，请编辑：</strong> src/composables/useCamera.js 中的 currentServer 变量</p>

                <div class="warning">
                    <strong>注意：</strong> 如果遇到502错误，说明SRS服务器不可用。请尝试：
                    <br>1. 启动本地SRS服务器 (docker run --rm -it -p 1985:1985 -p 8080:8080 ossrs/srs:5)
                    <br>2. 或切换到其他可用的服务器配置
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                📝 注意事项
            </div>
            <div class="test-description">
                <ul>
                    <li>需要HTTPS环境或localhost才能访问摄像头</li>
                    <li>首次使用需要用户授权摄像头权限</li>
                    <li>推流功能依赖SRS服务器的可用性</li>
                    <li>建议使用Chrome或Firefox浏览器进行测试</li>
                    <li>移动端测试请确保浏览器支持WebRTC</li>
                </ul>
            </div>
        </div>
    </div>
</body>

</html>