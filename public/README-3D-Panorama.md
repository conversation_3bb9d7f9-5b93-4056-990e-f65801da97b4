# 3D全景插件使用说明

## 文件结构
```
public/
├── 3d.html                 # Photo Sphere Viewer 全景页面
├── 3d-simple.html          # 简单版全景页面（推荐）
├── download-libs.sh        # 下载库文件脚本
├── js/                     # JavaScript 库文件目录
├── css/                    # CSS 样式文件目录
├── images/                 # 图片资源目录
│   └── panorama.jpg        # 全景图片（需要您提供）
└── README-3D-Panorama.md   # 本说明文件
```

## 🎯 推荐方案

### 方案1: 使用 Pannellum 全景查看器（最推荐）
- **文件**: `3d-pannellum.html`
- **优点**:
  - 轻量级，加载速度快
  - CDN稳定性好
  - 功能完整，界面美观
  - 移动端支持优秀
  - 错误处理完善
- **使用**: 直接打开 `3d-pannellum.html` 即可

### 方案2: 使用 Three.js 简单版（备选）
- **文件**: `3d-simple.html`
- **优点**:
  - 只依赖 Three.js CDN
  - 代码简单，易于自定义
  - 支持鼠标和触摸操作
  - 包含自动旋转、缩放、全屏功能
- **使用**: 直接打开 `3d-simple.html` 即可

### 方案2: 使用 Photo Sphere Viewer
- **文件**: `3d.html`
- **解决CDN访问问题的方法**:
  1. **尝试不同CDN**: 文件中已包含多个CDN选项
  2. **下载到本地**: 运行 `./download-libs.sh` 脚本
  3. **手动下载**: 从GitHub或npm下载文件

## CDN访问问题解决方案

### 方法1: 尝试不同的CDN
在 `3d.html` 中已经提供了多个CDN选项：
- unpkg.com (默认启用)
- cdnjs.cloudflare.com
- 本地文件

### 方法2: 下载到本地
```bash
cd public
./download-libs.sh
```
然后在 `3d.html` 中取消注释本地文件引用行。

### 方法3: 手动下载
如果脚本无法运行，可以手动下载：
1. 访问 https://github.com/mistic100/Photo-Sphere-Viewer/releases
2. 下载最新版本的 dist 文件
3. 将文件放入对应的 js/ 和 css/ 目录

## 需要的素材

### 1. 全景图片要求
- **文件名**: `panorama.jpg` (可自定义，需修改HTML中的路径)
- **格式**: JPG、PNG等常见格式
- **尺寸**: 建议4096x2048像素或更高分辨率
- **投影方式**: 等距圆柱投影（Equirectangular projection）
- **宽高比**: 2:1 (360度全景图标准比例)

### 2. 全景图片获取方式
1. **专业相机拍摄**: 使用360度全景相机（如Insta360、Ricoh Theta等）
2. **手机拍摄**: 使用手机全景模式或专门的全景App
3. **免费素材网站**:
   - [Pixabay](https://pixabay.com/photos/search/360%20panorama/)
   - [Unsplash](https://unsplash.com/s/photos/360-panorama)
   - [Pexels](https://www.pexels.com/search/360%20panorama/)
4. **示例全景图**: 搜索"equirectangular panorama sample"

## 使用步骤

1. **准备全景图片**
   - 将全景图片放入 `public/images/` 目录
   - 重命名为 `panorama.jpg` 或修改HTML中的图片路径

2. **启动本地服务器**
   ```bash
   # 如果使用Python
   python -m http.server 8000
   
   # 如果使用Node.js
   npx http-server
   
   # 如果使用PHP
   php -S localhost:8000
   ```

3. **访问页面**
   - 打开浏览器访问: `http://localhost:8000/3d.html`

## 插件说明

当前使用的是 **Photo Sphere Viewer** 插件，特点：
- 支持鼠标拖拽查看
- 支持滚轮缩放
- 支持自动旋转
- 支持全屏模式
- 移动端友好

## 常见问题

### 1. 图片无法加载
- 检查图片路径是否正确
- 确保图片文件存在
- 检查浏览器控制台是否有错误信息

### 2. 全景效果不佳
- 确保使用等距圆柱投影格式的图片
- 检查图片宽高比是否为2:1
- 尝试使用更高分辨率的图片

### 3. 性能问题
- 压缩图片大小（建议小于5MB）
- 使用WebP格式以获得更好的压缩率

## 自定义配置

可以修改以下参数来自定义全景效果：

```javascript
const viewer = new PhotoSphereViewer.Viewer({
    container: document.querySelector('#panorama'),
    panorama: 'images/panorama.jpg',
    autorotateDelay: 2000,        // 自动旋转延迟时间(ms)
    autorotateIdle: true,         // 空闲时自动旋转
    autorotateSpeed: '0.5rpm',    // 旋转速度
    defaultZoomLvl: 50,           // 默认缩放级别
    minFov: 30,                   // 最小视野角度
    maxFov: 90,                   // 最大视野角度
    navbar: [                     // 导航栏按钮
        'autorotate',
        'zoom',
        'fullscreen'
    ]
});
```
