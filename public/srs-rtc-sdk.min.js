'use strict';function SrsError(a,b){this.name=a,this.message=b,this.stack=new Error().stack}SrsError.prototype=Object.create(Error.prototype),SrsError.prototype.constructor=SrsError;function SrsRtcPublisherAsync(){var b={};return b.constraints={audio:!0,video:{width:{ideal:320,max:576}}},b.publish=async function(a){var c=b.__internal.prepareUrl(a);if(b.pc.addTransceiver("audio",{direction:"sendonly"}),b.pc.addTransceiver("video",{direction:"sendonly"}),!navigator.mediaDevices&&"http:"===window.location.protocol&&"localhost"!==window.location.hostname)throw new SrsError("HttpsRequiredError",`Please use HTTPS or localhost to publish, read https://github.com/ossrs/srs/issues/2762#issuecomment-983147576`);var d=await navigator.mediaDevices.getUserMedia(b.constraints);d.getTracks().forEach(function(a){b.pc.addTrack(a),b.ontrack&&b.ontrack({track:a})});var e=await b.pc.createOffer();await b.pc.setLocalDescription(e);var f=await new Promise(function(a,b){var d={api:c.apiUrl,tid:c.tid,streamurl:c.streamUrl,clientip:null,sdp:e.sdp};console.log("Generated offer: ",d);const f=new XMLHttpRequest;f.onload=function(){if(f.readyState===f.DONE){if(200!==f.status&&201!==f.status)return b(f);const c=JSON.parse(f.responseText);return console.log("Got answer: ",c),c.code?b(f):a(c)}},f.open("POST",c.apiUrl,!0),f.setRequestHeader("Content-type","application/json"),f.send(JSON.stringify(d))});return await b.pc.setRemoteDescription(new RTCSessionDescription({type:"answer",sdp:f.sdp})),f.simulator=c.schema+"//"+c.urlObject.server+":"+c.port+"/rtc/v1/nack/",f},b.close=function(){b.pc&&b.pc.close(),b.pc=null},b.ontrack=function(a){b.stream.addTrack(a.track)},b.__internal={defaultPath:"/rtc/v1/publish/",prepareUrl:function(a){var c=b.__internal.parse(a),d=c.user_query.schema;d=d?d+":":window.location.protocol;var e=c.port||1985;"https:"===d&&(e=c.port||443);var f=c.user_query.play||b.__internal.defaultPath;f.lastIndexOf("/")!==f.length-1&&(f+="/");var g=d+"//"+c.server+":"+e+f;for(var h in c.user_query)"api"!=h&&"play"!==h&&(g+="&"+h+"="+c.user_query[h]);g=g.replace(f+"&",f+"?");var i=c.url;return{apiUrl:g,streamUrl:i,schema:d,urlObject:c,port:e,tid:(+parseInt(100*(new Date().getTime()*Math.random()))).toString(16).slice(0,7)}},parse:function(c){var d=document.createElement("a");d.href=c.replace("rtmp://","http://").replace("webrtc://","http://").replace("rtc://","http://");var a=d.hostname,e=d.pathname.substring(1,d.pathname.lastIndexOf("/")),f=d.pathname.slice(d.pathname.lastIndexOf("/")+1);if(e=e.replace("...vhost...","?vhost="),0<=e.indexOf("?")){var g=e.slice(e.indexOf("?"));e=e.slice(0,e.indexOf("?")),0<g.indexOf("vhost=")&&(a=g.slice(g.indexOf("vhost=")+6),0<a.indexOf("&")&&(a=a.slice(0,a.indexOf("&"))))}if(d.hostname===a){/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/.test(d.hostname)&&(a="__defaultVhost__")}var h="rtmp";0<c.indexOf("://")&&(h=c.slice(0,c.indexOf("://")));var i=d.port;i||("webrtc"===h&&0===c.indexOf(`webrtc://${d.host}:`)&&(i=0===c.indexOf(`webrtc://${d.host}:80`)?80:443),"http"===h?i=80:"https"===h?i=443:"rtmp"===h&&(i=1935));var j={url:c,schema:h,server:d.hostname,port:i,vhost:a,app:e,stream:f};return b.__internal.fill_query(d.search,j),j.port||"webrtc"!==h&&"rtc"!==h||("https"===j.user_query.schema?j.port=443:0===window.location.href.indexOf("https://")?j.port=443:j.port=1985),j},fill_query:function(a,b){if(b.user_query={},0!==a.length){0<=a.indexOf("?")&&(a=a.split("?")[1]);for(var c=a.split("&"),d=0;d<c.length;d++){var e=c[d],f=e.split("=");b[f[0]]=f[1],b.user_query[f[0]]=f[1]}b.domain&&(b.vhost=b.domain)}}},b.pc=new RTCPeerConnection(null),b.stream=new MediaStream,b}function SrsRtcPlayerAsync(){var b={};return b.play=async function(a){var c=b.__internal.prepareUrl(a);b.pc.addTransceiver("audio",{direction:"recvonly"}),b.pc.addTransceiver("video",{direction:"recvonly"});var d=await b.pc.createOffer();await b.pc.setLocalDescription(d);var e=await new Promise(function(a,b){var e={api:c.apiUrl,tid:c.tid,streamurl:c.streamUrl,clientip:null,sdp:d.sdp};console.log("Generated offer: ",e);const f=new XMLHttpRequest;f.onload=function(){if(f.readyState===f.DONE){if(200!==f.status&&201!==f.status)return b(f);const c=JSON.parse(f.responseText);return console.log("Got answer: ",c),c.code?b(f):a(c)}},f.open("POST",c.apiUrl,!0),f.setRequestHeader("Content-type","application/json"),f.send(JSON.stringify(e))});return await b.pc.setRemoteDescription(new RTCSessionDescription({type:"answer",sdp:e.sdp})),e.simulator=c.schema+"//"+c.urlObject.server+":"+c.port+"/rtc/v1/nack/",e},b.close=function(){b.pc&&b.pc.close(),b.pc=null},b.ontrack=function(a){b.stream.addTrack(a.track)},b.__internal={defaultPath:"/rtc/v1/play/",prepareUrl:function(a){var c=b.__internal.parse(a),d=c.user_query.schema;d=d?d+":":window.location.protocol;var e=c.port||1985;"https:"===d&&(e=c.port||443);var f=c.user_query.play||b.__internal.defaultPath;f.lastIndexOf("/")!==f.length-1&&(f+="/");var g=d+"//"+c.server+":"+e+f;for(var h in c.user_query)"api"!=h&&"play"!==h&&(g+="&"+h+"="+c.user_query[h]);g=g.replace(f+"&",f+"?");var i=c.url;return{apiUrl:g,streamUrl:i,schema:d,urlObject:c,port:e,tid:(+parseInt(100*(new Date().getTime()*Math.random()))).toString(16).slice(0,7)}},parse:function(c){var d=document.createElement("a");d.href=c.replace("rtmp://","http://").replace("webrtc://","http://").replace("rtc://","http://");var a=d.hostname,e=d.pathname.substring(1,d.pathname.lastIndexOf("/")),f=d.pathname.slice(d.pathname.lastIndexOf("/")+1);if(e=e.replace("...vhost...","?vhost="),0<=e.indexOf("?")){var g=e.slice(e.indexOf("?"));e=e.slice(0,e.indexOf("?")),0<g.indexOf("vhost=")&&(a=g.slice(g.indexOf("vhost=")+6),0<a.indexOf("&")&&(a=a.slice(0,a.indexOf("&"))))}if(d.hostname===a){/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/.test(d.hostname)&&(a="__defaultVhost__")}var h="rtmp";0<c.indexOf("://")&&(h=c.slice(0,c.indexOf("://")));var i=d.port;i||("webrtc"===h&&0===c.indexOf(`webrtc://${d.host}:`)&&(i=0===c.indexOf(`webrtc://${d.host}:80`)?80:443),"http"===h?i=80:"https"===h?i=443:"rtmp"===h&&(i=1935));var j={url:c,schema:h,server:d.hostname,port:i,vhost:a,app:e,stream:f};return b.__internal.fill_query(d.search,j),j.port||"webrtc"!==h&&"rtc"!==h||("https"===j.user_query.schema?j.port=443:0===window.location.href.indexOf("https://")?j.port=443:j.port=1985),j},fill_query:function(a,b){if(b.user_query={},0!==a.length){0<=a.indexOf("?")&&(a=a.split("?")[1]);for(var c=a.split("&"),d=0;d<c.length;d++){var e=c[d],f=e.split("=");b[f[0]]=f[1],b.user_query[f[0]]=f[1]}b.domain&&(b.vhost=b.domain)}}},b.pc=new RTCPeerConnection(null),b.stream=new MediaStream,b.pc.ontrack=function(a){b.ontrack&&b.ontrack(a)},b}function SrsRtcWhipWhepAsync(){var a={};return a.constraints={audio:!0,video:{width:{ideal:320,max:576}}},a.publish=async function(b,c){if(-1===b.indexOf("/whip/"))throw new Error(`invalid WHIP url ${b}`);if(c?.videoOnly&&c?.audioOnly)throw new Error(`The videoOnly and audioOnly in options can't be true at the same time`);if(c?.videoOnly?a.constraints.audio=!1:a.pc.addTransceiver("audio",{direction:"sendonly"}),c?.audioOnly?a.constraints.video=!1:a.pc.addTransceiver("video",{direction:"sendonly"}),!navigator.mediaDevices&&"http:"===window.location.protocol&&"localhost"!==window.location.hostname)throw new SrsError("HttpsRequiredError",`Please use HTTPS or localhost to publish, read https://github.com/ossrs/srs/issues/2762#issuecomment-983147576`);var d=await navigator.mediaDevices.getUserMedia(a.constraints);d.getTracks().forEach(function(b){a.pc.addTrack(b),a.ontrack&&a.ontrack({track:b})});var e=await a.pc.createOffer();await a.pc.setLocalDescription(e);const f=await new Promise(function(a,c){console.log(`Generated offer: ${e.sdp}`);const d=new XMLHttpRequest;d.onload=function(){if(d.readyState===d.DONE){if(200!==d.status&&201!==d.status)return c(d);const b=d.responseText;return console.log("Got answer: ",b),b.code?c(d):a(b)}},d.open("POST",b,!0),d.setRequestHeader("Content-type","application/sdp"),d.send(e.sdp)});return await a.pc.setRemoteDescription(new RTCSessionDescription({type:"answer",sdp:f})),a.__internal.parseId(b,e.sdp,f)},a.play=async function(b,c){if(-1===b.indexOf("/whip-play/")&&-1===b.indexOf("/whep/"))throw new Error(`invalid WHEP url ${b}`);if(c?.videoOnly&&c?.audioOnly)throw new Error(`The videoOnly and audioOnly in options can't be true at the same time`);c?.videoOnly||a.pc.addTransceiver("audio",{direction:"recvonly"}),c?.audioOnly||a.pc.addTransceiver("video",{direction:"recvonly"});var d=await a.pc.createOffer();await a.pc.setLocalDescription(d);const e=await new Promise(function(a,c){console.log(`Generated offer: ${d.sdp}`);const e=new XMLHttpRequest;e.onload=function(){if(e.readyState===e.DONE){if(200!==e.status&&201!==e.status)return c(e);const b=e.responseText;return console.log("Got answer: ",b),b.code?c(e):a(b)}},e.open("POST",b,!0),e.setRequestHeader("Content-type","application/sdp"),e.send(d.sdp)});return await a.pc.setRemoteDescription(new RTCSessionDescription({type:"answer",sdp:e})),a.__internal.parseId(b,d.sdp,e)},a.close=function(){a.pc&&a.pc.close(),a.pc=null},a.ontrack=function(b){a.stream.addTrack(b.track)},a.pc=new RTCPeerConnection(null),a.stream=new MediaStream,a.__internal={parseId:(b,c,d)=>{let e=c.substr(c.indexOf("a=ice-ufrag:")+12);e=e.substr(0,e.indexOf("\n")-1)+":",e+=d.substr(d.indexOf("a=ice-ufrag:")+12),e=e.substr(0,e.indexOf("\n"));const f=document.createElement("a");return f.href=b,{sessionid:e,simulator:f.protocol+"//"+f.host+"/rtc/v1/nack/"}}},a.pc.ontrack=function(b){a.ontrack&&a.ontrack(b)},a}function SrsRtcFormatSenders(a,b){var d=[];return a.forEach(function(a){var c=a.getParameters();c&&c.codecs&&c.codecs.forEach(function(e){if(!(b&&a.track.kind!==b)&&!(0<e.mimeType.indexOf("/red")||0<e.mimeType.indexOf("/rtx")||0<e.mimeType.indexOf("/fec"))){var c="";c+=e.mimeType.replace("audio/","").replace("video/",""),c+=", "+e.clockRate+"HZ","audio"===a.track.kind&&(c+=", channels: "+e.channels),c+=", pt: "+e.payloadType,d.push(c)}})}),d.join(", ")}