<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音聊天系统 - COS上传版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-indicator {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .info-panel {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info-list {
            list-style: none;
            padding: 0;
        }

        .info-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .info-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }

        .workflow-panel {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .workflow-title {
            font-weight: 600;
            color: #f57c00;
            margin-bottom: 15px;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .workflow-step {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ff9800;
        }

        .step-number {
            background: #ff9800;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .step-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .step-desc {
            font-size: 12px;
            color: #666;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #4caf50;
            color: white;
        }

        .btn-success:hover {
            background: #45a049;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background: #f5f5f5;
            font-weight: 600;
        }

        .comparison-table .feature {
            font-weight: 500;
        }

        .comparison-table .old {
            color: #f44336;
        }

        .comparison-table .new {
            color: #4caf50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 语音聊天系统</h1>
            <p>基于COS云存储的语音交互方案</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="status-bar">
                <span class="status-indicator status-connected">✅ 系统已升级</span>
                <span style="font-size: 12px; color: #666;">
                    新版本使用HTTP + COS上传方式
                </span>
            </div>

            <!-- 新特性介绍 -->
            <div class="info-panel">
                <div class="info-title">🚀 新版本特性</div>
                <ul class="info-list">
                    <li>使用COS云存储上传音频文件，更稳定可靠</li>
                    <li>通过HTTP API与服务器通信，避免WebSocket连接问题</li>
                    <li>支持HTTPS环境，解决混合内容安全问题</li>
                    <li>增加文件大小和时长限制，提升用户体验</li>
                    <li>添加任务状态跟踪，实时显示处理进度</li>
                    <li>优化错误处理和重试机制</li>
                </ul>
            </div>

            <!-- 工作流程 -->
            <div class="workflow-panel">
                <div class="workflow-title">📋 工作流程</div>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <div class="step-title">录音检测</div>
                        <div class="step-desc">基于音量阈值自动开始/停止录音</div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <div class="step-title">文件上传</div>
                        <div class="step-desc">将音频文件上传到COS云存储</div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <div class="step-title">任务提交</div>
                        <div class="step-desc">通知服务器处理上传的音频文件</div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <div class="step-title">结果获取</div>
                        <div class="step-desc">轮询获取处理结果并播放回复</div>
                    </div>
                </div>
            </div>

            <!-- 对比表格 -->
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能特性</th>
                        <th>旧版本 (WebSocket)</th>
                        <th>新版本 (COS + HTTP)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="feature">通信方式</td>
                        <td class="old">WebSocket实时传输</td>
                        <td class="new">HTTP API + 文件上传</td>
                    </tr>
                    <tr>
                        <td class="feature">HTTPS支持</td>
                        <td class="old">需要WSS服务器</td>
                        <td class="new">原生支持HTTPS</td>
                    </tr>
                    <tr>
                        <td class="feature">文件大小限制</td>
                        <td class="old">内存限制</td>
                        <td class="new">10MB上限，可配置</td>
                    </tr>
                    <tr>
                        <td class="feature">错误处理</td>
                        <td class="old">连接断开重连</td>
                        <td class="new">自动重试 + 状态跟踪</td>
                    </tr>
                    <tr>
                        <td class="feature">服务器压力</td>
                        <td class="old">需要维持连接</td>
                        <td class="new">无状态，更易扩展</td>
                    </tr>
                </tbody>
            </table>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="/voice-chat" class="btn btn-primary">
                    🎯 体验新版本
                </a>
                <a href="voice-chat-static.html" class="btn btn-success">
                    📄 静态页面版本
                </a>
            </div>

            <!-- 技术说明 -->
            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <h3 style="margin-bottom: 15px; color: #333;">🔧 技术实现</h3>
                <p style="margin-bottom: 10px; color: #666; line-height: 1.6;">
                    新版本采用了更现代化的架构设计：
                </p>
                <ul style="color: #666; line-height: 1.8; padding-left: 20px;">
                    <li><strong>前端</strong>：Vue 3 + Composition API + Naive UI</li>
                    <li><strong>文件存储</strong>：腾讯云COS对象存储</li>
                    <li><strong>通信协议</strong>：RESTful API + HTTP轮询</li>
                    <li><strong>音频处理</strong>：Web Audio API + MediaRecorder</li>
                    <li><strong>状态管理</strong>：响应式数据 + 计算属性</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
