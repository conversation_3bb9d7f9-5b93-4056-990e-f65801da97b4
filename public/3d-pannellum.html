<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pannellum 全景查看器</title>
    
    <!-- 使用 Pannellum - 更轻量级和稳定的全景库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.css"/>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/pannellum@2.5.6/build/pannellum.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        #panorama {
            width: 100%;
            height: 600px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>全景图片查看器</h1>
        
        <div id="panorama"></div>
        
        <div class="controls">
            <button onclick="toggleAutoRotate()">切换自动旋转</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleFullscreen()">全屏模式</button>
        </div>
        
        <div class="info">
            <h3>操作说明</h3>
            <ul>
                <li><strong>鼠标拖拽</strong>：旋转视角</li>
                <li><strong>滚轮</strong>：缩放</li>
                <li><strong>移动端</strong>：触摸拖拽和双指缩放</li>
            </ul>
            
            <h3>图片要求</h3>
            <ul>
                <li>格式：JPG、PNG等常见格式</li>
                <li>宽高比：2:1（如4096x2048像素）</li>
                <li>投影方式：等距圆柱投影（Equirectangular）</li>
                <li>文件路径：images/panorama.jpg</li>
            </ul>
        </div>
    </div>

    <script>
        let viewer;
        let autoRotateEnabled = false;

        // 初始化全景查看器
        function initPanorama() {
            try {
                viewer = pannellum.viewer('panorama', {
                    "type": "equirectangular",
                    "panorama": "images/full.jpg",
                    "autoLoad": true,
                    "autoRotate": -2, // 自动旋转速度（负数表示逆时针）
                    "autoRotateInactivityDelay": 3000, // 3秒后开始自动旋转
                    "autoRotateStopDelay": 1000, // 交互后1秒停止自动旋转
                    "showControls": true,
                    "showFullscreenCtrl": true,
                    "showZoomCtrl": true,
                    "mouseZoom": true,
                    "doubleClickZoom": true,
                    "draggable": true,
                    "keyboardZoom": true,
                    "compass": true,
                    "northOffset": 0,
                    "preview": "", // 可以设置预览图
                    "hfov": 100, // 初始视野角度
                    "minHfov": 50, // 最小视野角度
                    "maxHfov": 120, // 最大视野角度
                    "pitch": 0, // 初始俯仰角
                    "yaw": 0, // 初始偏航角
                });

                // 监听加载完成事件
                viewer.on('load', function() {
                    console.log('全景图片加载完成');
                    autoRotateEnabled = true;
                });

                // 监听错误事件
                viewer.on('error', function(error) {
                    console.error('全景图片加载失败:', error);
                    showError('全景图片加载失败，请检查图片路径是否正确');
                });

            } catch (error) {
                console.error('Pannellum 初始化失败:', error);
                showError('全景查看器初始化失败，请检查网络连接');
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('panorama').innerHTML = 
                '<div class="error">' +
                '<h3>❌ 加载失败</h3>' +
                '<p>' + message + '</p>' +
                '<p>建议：</p>' +
                '<ul>' +
                '<li>检查 images/panorama.jpg 文件是否存在</li>' +
                '<li>尝试使用 <a href="3d-simple.html">Three.js版本</a></li>' +
                '<li>运行 ./download-sample-image.sh 下载示例图片</li>' +
                '</ul>' +
                '</div>';
        }

        // 切换自动旋转
        function toggleAutoRotate() {
            if (viewer) {
                if (autoRotateEnabled) {
                    viewer.stopAutoRotate();
                    autoRotateEnabled = false;
                } else {
                    viewer.startAutoRotate(-2);
                    autoRotateEnabled = true;
                }
            }
        }

        // 重置视角
        function resetView() {
            if (viewer) {
                viewer.setPitch(0);
                viewer.setYaw(0);
                viewer.setHfov(100);
            }
        }

        // 切换全屏
        function toggleFullscreen() {
            if (viewer) {
                if (viewer.isFullscreen()) {
                    viewer.exitFullscreen();
                } else {
                    viewer.requestFullscreen();
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查 Pannellum 是否加载成功
            if (typeof pannellum !== 'undefined') {
                initPanorama();
            } else {
                showError('Pannellum 库加载失败，请检查网络连接');
            }
        });
    </script>
</body>
</html>
