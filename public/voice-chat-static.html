<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音聊天 - 静态版本</title>
    <script src="https://cdn.socket.io/4.8.1/socket.io.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-indicator {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #dee2e6;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
        }

        .volume-display {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .volume-bar {
            width: 100%;
            height: 30px;
            background: #f1f3f4;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 15px;
            position: relative;
        }

        .volume-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.1s ease;
            border-radius: 15px;
        }

        .volume-fill.active {
            animation: pulse 0.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from {
                opacity: 0.7;
            }

            to {
                opacity: 1;
            }
        }

        .recording-status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .status-idle {
            background: #e2e3e5;
            color: #6c757d;
        }

        .status-waiting {
            background: #cce5ff;
            color: #0066cc;
        }

        .status-recording {
            background: #fff3cd;
            color: #856404;
        }

        .messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fafafa;
        }

        .message {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .message.user {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }

        .message.server {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }

        .message.status {
            background: #fff8e1;
            border-left-color: #ff9800;
        }

        .message-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-time {
            font-size: 12px;
            color: #6c757d;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .protocol-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }

        .protocol-warning p {
            margin: 5px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎤 语音聊天系统</h1>
            <p>基于音量阈值的智能语音交互</p>
        </div>

        <div class="content">
            <!-- 连接状态 -->
            <div class="status-bar">
                <span id="connectionStatus" class="status-indicator status-disconnected">未连接</span>
                <div>
                    <button id="connectBtn" class="btn btn-primary">连接服务器</button>
                    <button id="protocolBtn" class="btn btn-secondary" style="margin-left: 10px;">切换协议</button>
                </div>
            </div>

            <!-- 协议提示 -->
            <div id="protocolWarning" class="protocol-warning" style="display: none;">
                <p>⚠️ 当前使用HTTPS协议，需要连接到支持SSL的WebSocket服务器</p>
                <p>如果连接失败，请点击"切换协议"按钮尝试HTTP版本</p>
            </div>

            <!-- 控制面板 -->
            <div class="controls">
                <div class="control-group">
                    <label for="thresholdSlider">音量阈值: <span id="thresholdValue">50</span></label>
                    <input type="range" id="thresholdSlider" class="slider" min="10" max="200" value="50" step="5">
                </div>
                <div class="control-group">
                    <label for="patienceSlider">耐心值: <span id="patienceValue">20</span></label>
                    <input type="range" id="patienceSlider" class="slider" min="5" max="50" value="20" step="1">
                </div>
            </div>

            <!-- 音量显示 -->
            <div class="volume-display">
                <div class="volume-bar">
                    <div id="volumeFill" class="volume-fill" style="width: 0%"></div>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>当前音量: <span id="volumeText">0</span></span>
                    <div id="recordingStatus" class="recording-status status-idle">空闲</div>
                </div>
            </div>

            <!-- 消息区域 -->
            <div id="messages" class="messages">
                <div class="message status">
                    <div class="message-content">
                        <span>系统已就绪，请连接服务器开始使用</span>
                        <span class="message-time" id="initTime"></span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button id="recordBtn" class="btn btn-primary" disabled>开始录音</button>
                <button id="clearBtn" class="btn btn-secondary">清空消息</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let socket = null;
        let isRecording = false;
        let recordingState = 'idle'; // idle, waiting, recording, stopping
        let mediaRecorder = null;
        let audioContext = null;
        let analyser = null;
        let microphone = null;
        let dataArray = null;
        let recordingFrames = [];
        let silenceCounter = 0;
        let animationId = null;
        let threshold = 50;
        let patience = 20;
        let currentVolume = 0;

        // DOM 元素
        const elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            connectBtn: document.getElementById('connectBtn'),
            thresholdSlider: document.getElementById('thresholdSlider'),
            thresholdValue: document.getElementById('thresholdValue'),
            patienceSlider: document.getElementById('patienceSlider'),
            patienceValue: document.getElementById('patienceValue'),
            volumeFill: document.getElementById('volumeFill'),
            volumeText: document.getElementById('volumeText'),
            recordingStatus: document.getElementById('recordingStatus'),
            messages: document.getElementById('messages'),
            recordBtn: document.getElementById('recordBtn'),
            clearBtn: document.getElementById('clearBtn'),
            initTime: document.getElementById('initTime'),
            protocolBtn: document.getElementById('protocolBtn'),
            protocolWarning: document.getElementById('protocolWarning')
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            elements.initTime.textContent = new Date().toLocaleTimeString();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            elements.connectBtn.addEventListener('click', connectToServer);
            elements.recordBtn.addEventListener('click', toggleRecording);
            elements.clearBtn.addEventListener('click', clearMessages);
            elements.protocolBtn.addEventListener('click', switchProtocol);

            elements.thresholdSlider.addEventListener('input', (e) => {
                threshold = parseInt(e.target.value);
                elements.thresholdValue.textContent = threshold;
            });

            elements.patienceSlider.addEventListener('input', (e) => {
                patience = parseInt(e.target.value);
                elements.patienceValue.textContent = patience;
            });

            // 检查协议并显示警告
            checkProtocol();
        }

        // 检查协议
        function checkProtocol() {
            if (window.location.protocol === 'https:') {
                elements.protocolWarning.style.display = 'block';
            }
        }

        // 切换协议
        function switchProtocol() {
            const currentUrl = window.location.href;
            let newUrl;

            if (window.location.protocol === 'https:') {
                newUrl = currentUrl.replace('https:', 'http:');
                if (confirm('将切换到HTTP协议访问，这可能不安全。是否继续？')) {
                    window.location.href = newUrl;
                }
            } else {
                newUrl = currentUrl.replace('http:', 'https:');
                window.location.href = newUrl;
            }
        }

        // 获取服务器URL
        function getServerUrl() {
            const isHttps = window.location.protocol === 'https:';

            // 如果是HTTPS，尝试使用WSS
            if (isHttps) {
                // 首先尝试WSS，如果失败会在connect_error中处理
                return 'wss://120.48.126.4:5008'; // HTTPS版本
            }
            return 'http://120.48.126.4:5007'; // HTTP版本，使用http而不是ws
        }

        // 备用连接方案
        function getBackupServerUrl() {
            // 如果主连接失败，尝试其他端口或地址
            const isHttps = window.location.protocol === 'https:';
            if (isHttps) {
                return 'wss://120.48.126.4:443'; // 标准HTTPS端口
            }
            return 'http://120.48.126.4:80'; // 标准HTTP端口
        }

        // 连接到服务器
        function connectToServer() {
            try {
                const serverUrl = getServerUrl();
                addMessage('system', `正在连接到: ${serverUrl}`, 'status');

                socket = io(serverUrl, {
                    transports: ['websocket', 'polling'],
                    timeout: 10000,
                    forceNew: true
                });

                socket.on('connect', () => {
                    updateConnectionStatus(true);
                    addMessage('system', '服务器连接成功', 'status');
                });

                socket.on('disconnect', () => {
                    updateConnectionStatus(false);
                    addMessage('system', '服务器连接断开', 'status');
                });

                socket.on('text_response', (data) => {
                    addMessage('server', `文本回复: ${data}`, 'server');
                });

                socket.on('skill_response', (data) => {
                    addMessage('server', `技能回复: ${data}`, 'server');
                });

                socket.on('audio_response', async (data) => {
                    addMessage('server', '收到音频回复', 'server');
                    await playAudioResponse(data);
                });

                socket.on('connect_error', (error) => {
                    addMessage('system', `连接错误: ${error.message}`, 'status');

                    // 如果是HTTPS环境下的连接错误，提供解决方案
                    if (window.location.protocol === 'https:') {
                        addMessage('system', '🔒 HTTPS环境检测到连接问题', 'status');
                        addMessage('system', '💡 解决方案：', 'status');
                        addMessage('system', '1. 请联系服务器管理员启用WSS支持', 'status');
                        addMessage('system', '2. 或者通过HTTP访问此页面（不推荐生产环境）', 'status');
                        addMessage('system', `3. HTTP访问地址: ${window.location.href.replace('https:', 'http:')}`, 'status');
                    }
                });

            } catch (error) {
                addMessage('system', '连接失败: ' + error.message, 'status');
                if (window.location.protocol === 'https:') {
                    addMessage('system', '提示：HTTPS页面需要连接到支持SSL的WebSocket服务器', 'status');
                }
            }
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            if (connected) {
                elements.connectionStatus.textContent = '已连接';
                elements.connectionStatus.className = 'status-indicator status-connected';
                elements.connectBtn.style.display = 'none';
                elements.recordBtn.disabled = false;
            } else {
                elements.connectionStatus.textContent = '未连接';
                elements.connectionStatus.className = 'status-indicator status-disconnected';
                elements.connectBtn.style.display = 'inline-block';
                elements.recordBtn.disabled = true;
            }
        }

        // 初始化音频
        async function initAudio() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });

                audioContext = new AudioContext({ sampleRate: 16000 });
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 1024;
                analyser.smoothingTimeConstant = 0.3;

                microphone = audioContext.createMediaStreamSource(stream);
                microphone.connect(analyser);

                dataArray = new Uint8Array(analyser.frequencyBinCount);

                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordingFrames.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    sendAudioToServer();
                };

                return true;
            } catch (error) {
                addMessage('system', '音频初始化失败: ' + error.message, 'status');
                return false;
            }
        }

        // 切换录音状态
        async function toggleRecording() {
            if (isRecording) {
                stopRecording();
            } else {
                if (await initAudio()) {
                    startRecording();
                }
            }
        }

        // 开始录音
        function startRecording() {
            isRecording = true;
            recordingState = 'waiting';
            recordingFrames = [];
            silenceCounter = 0;

            elements.recordBtn.textContent = '停止录音';
            elements.recordBtn.className = 'btn btn-danger';

            updateRecordingStatus();
            monitorVolume();

            addMessage('user', '开始监听语音输入...', 'user');
        }

        // 停止录音
        function stopRecording() {
            isRecording = false;
            recordingState = 'idle';

            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
            }

            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }

            elements.recordBtn.textContent = '开始录音';
            elements.recordBtn.className = 'btn btn-primary';

            currentVolume = 0;
            updateVolumeDisplay();
            updateRecordingStatus();

            addMessage('user', '录音已停止', 'user');
        }

        // 音量监测
        function monitorVolume() {
            if (!isRecording || !analyser) return;

            analyser.getByteFrequencyData(dataArray);

            // 计算平均音量
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i];
            }
            currentVolume = sum / dataArray.length;

            updateVolumeDisplay();

            // 根据音量阈值控制录音状态
            if (currentVolume > threshold) {
                if (recordingState === 'waiting') {
                    recordingState = 'recording';
                    mediaRecorder.start(100);
                    addMessage('user', '检测到语音，开始录音...', 'user');
                    updateRecordingStatus();
                }
                silenceCounter = 0;
            } else {
                if (recordingState === 'recording') {
                    silenceCounter++;
                    if (silenceCounter >= patience) {
                        recordingState = 'stopping';
                        if (mediaRecorder.state === 'recording') {
                            mediaRecorder.stop();
                        }
                        addMessage('user', '录音结束，发送中...', 'user');
                        updateRecordingStatus();

                        setTimeout(() => {
                            if (isRecording) {
                                recordingState = 'waiting';
                                recordingFrames = [];
                                silenceCounter = 0;
                                updateRecordingStatus();
                            }
                        }, 1000);
                    }
                }
            }

            animationId = requestAnimationFrame(monitorVolume);
        }

        // 更新音量显示
        function updateVolumeDisplay() {
            const percentage = Math.min((currentVolume / threshold) * 100, 100);
            elements.volumeFill.style.width = percentage + '%';
            elements.volumeText.textContent = Math.round(currentVolume);

            if (currentVolume > threshold) {
                elements.volumeFill.classList.add('active');
            } else {
                elements.volumeFill.classList.remove('active');
            }
        }

        // 更新录音状态显示
        function updateRecordingStatus() {
            const statusElement = elements.recordingStatus;
            statusElement.className = 'recording-status';

            switch (recordingState) {
                case 'waiting':
                    statusElement.textContent = '等待语音输入...';
                    statusElement.classList.add('status-waiting');
                    break;
                case 'recording':
                    statusElement.textContent = '正在录音';
                    statusElement.classList.add('status-recording');
                    break;
                case 'stopping':
                    statusElement.textContent = '处理中...';
                    statusElement.classList.add('status-waiting');
                    break;
                default:
                    statusElement.textContent = '空闲';
                    statusElement.classList.add('status-idle');
            }
        }

        // 发送音频到服务器
        async function sendAudioToServer() {
            if (!socket || recordingFrames.length === 0) return;

            try {
                const audioBlob = new Blob(recordingFrames, { type: 'audio/webm;codecs=opus' });
                const arrayBuffer = await audioBlob.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);

                socket.emit('user_audio', uint8Array);
                addMessage('user', '音频已发送', 'user');

            } catch (error) {
                addMessage('system', '发送音频失败: ' + error.message, 'status');
            }

            recordingFrames = [];
        }

        // 播放服务器返回的音频
        async function playAudioResponse(audioData) {
            try {
                const audioBlob = new Blob([audioData], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);

                const audio = new Audio(audioUrl);
                audio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                    addMessage('server', '音频播放完成', 'server');
                };

                await audio.play();

            } catch (error) {
                addMessage('system', '音频播放失败: ' + error.message, 'status');
            }
        }

        // 添加消息
        function addMessage(sender, content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div class="message-content">
                    <span>${content}</span>
                    <span class="message-time">${timestamp}</span>
                </div>
            `;

            elements.messages.appendChild(messageDiv);
            elements.messages.scrollTop = elements.messages.scrollHeight;
        }

        // 清空消息
        function clearMessages() {
            elements.messages.innerHTML = `
                <div class="message status">
                    <div class="message-content">
                        <span>消息已清空</span>
                        <span class="message-time">${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
            `;
        }
    </script>
</body>

</html>