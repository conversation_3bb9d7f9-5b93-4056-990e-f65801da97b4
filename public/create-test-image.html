<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成测试全景图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 5px;
            display: block;
            margin: 20px auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>生成测试全景图</h1>
        
        <div class="info">
            <p><strong>说明：</strong>这个工具会生成一个简单的测试全景图，用于测试全景查看器功能。</p>
            <p>生成的图片是2:1比例的等距圆柱投影格式，可以直接用于全景查看器。</p>
        </div>
        
        <canvas id="canvas" width="1024" height="512"></canvas>
        
        <div class="controls">
            <button onclick="generateImage()">生成测试图片</button>
            <button onclick="downloadImage()">下载图片</button>
        </div>
        
        <div class="info">
            <p><strong>使用步骤：</strong></p>
            <ol>
                <li>点击"生成测试图片"按钮</li>
                <li>点击"下载图片"按钮保存</li>
                <li>将下载的图片重命名为 <code>panorama.jpg</code></li>
                <li>放入 <code>images/</code> 目录</li>
                <li>打开全景查看器页面测试</li>
            </ol>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        let imageGenerated = false;

        function generateImage() {
            const width = canvas.width;
            const height = canvas.height;
            
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 0, height);
            gradient.addColorStop(0, '#87CEEB'); // 天空蓝
            gradient.addColorStop(0.7, '#98FB98'); // 浅绿色
            gradient.addColorStop(1, '#228B22'); // 森林绿
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // 添加太阳
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(width * 0.8, height * 0.2, 30, 0, Math.PI * 2);
            ctx.fill();
            
            // 添加云朵
            drawCloud(width * 0.2, height * 0.15, 40);
            drawCloud(width * 0.6, height * 0.25, 35);
            drawCloud(width * 0.9, height * 0.18, 30);
            
            // 添加山脉
            ctx.fillStyle = '#8B4513';
            ctx.beginPath();
            ctx.moveTo(0, height * 0.6);
            for (let x = 0; x <= width; x += 50) {
                const y = height * 0.6 + Math.sin(x * 0.01) * 50 + Math.random() * 30;
                ctx.lineTo(x, y);
            }
            ctx.lineTo(width, height);
            ctx.lineTo(0, height);
            ctx.closePath();
            ctx.fill();
            
            // 添加文字标识
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试全景图 - Test Panorama', width / 2, height / 2);
            
            ctx.font = '16px Arial';
            ctx.fillText('360° × 180° 等距圆柱投影', width / 2, height / 2 + 30);
            
            // 添加方向标识
            ctx.fillStyle = '#FF0000';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('北 N', width / 2, 40);
            ctx.fillText('南 S', width / 2, height - 20);
            
            ctx.fillText('东 E', width - 30, height / 2);
            ctx.fillText('西 W', 30, height / 2);
            
            imageGenerated = true;
            console.log('测试全景图生成完成');
        }
        
        function drawCloud(x, y, size) {
            ctx.fillStyle = '#FFFFFF';
            ctx.globalAlpha = 0.8;
            
            // 绘制云朵（多个圆形组成）
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.arc(x + size * 0.5, y, size * 0.8, 0, Math.PI * 2);
            ctx.arc(x - size * 0.5, y, size * 0.8, 0, Math.PI * 2);
            ctx.arc(x, y - size * 0.5, size * 0.6, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.globalAlpha = 1.0;
        }
        
        function downloadImage() {
            if (!imageGenerated) {
                alert('请先生成测试图片');
                return;
            }
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = 'test-panorama.jpg';
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('图片下载完成');
        }
        
        // 页面加载时自动生成图片
        window.addEventListener('load', function() {
            setTimeout(generateImage, 500);
        });
    </script>
</body>
</html>
