<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>jQuery Panorama Example</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 使用 Photo Sphere Viewer v5 (更稳定的版本) -->
    <script src="https://cdn.jsdelivr.net/npm/photo-sphere-viewer@5/dist/photo-sphere-viewer.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/photo-sphere-viewer@5/dist/photo-sphere-viewer.min.css">

    <!-- 备用CDN选项 -->
    <!-- <script src="https://unpkg.com/photo-sphere-viewer@5/dist/photo-sphere-viewer.min.js"></script> -->
    <!-- <link rel="stylesheet" href="https://unpkg.com/photo-sphere-viewer@5/dist/photo-sphere-viewer.min.css"> -->

    <!-- 本地文件选项 -->
    <!-- <script src="js/photo-sphere-viewer.min.js"></script> -->
    <!-- <link rel="stylesheet" href="css/photo-sphere-viewer.min.css"> -->
    <style>
        #panorama {
            width: 800px;
            height: 600px;
        }
    </style>
</head>

<body>
    <div id="panorama"></div>
    <script>
        // 使用Photo Sphere Viewer v5的示例
        document.addEventListener('DOMContentLoaded', function () {
            try {
                const viewer = new PhotoSphereViewer.Viewer({
                    container: document.querySelector('#panorama'),
                    panorama: 'images/panorama.jpg', // 请替换为您的全景图片路径
                    autorotateDelay: 2000,
                    autorotateIdle: true,
                    autorotateSpeed: '0.5rpm',
                    navbar: [
                        'autorotate',
                        'zoom',
                        'fullscreen'
                    ],
                    // 添加错误处理
                    onReady: function () {
                        console.log('全景查看器已准备就绪');
                    }
                });
            } catch (error) {
                console.error('Photo Sphere Viewer 初始化失败:', error);
                document.getElementById('panorama').innerHTML =
                    '<div style="padding: 20px; text-align: center; color: red;">' +
                    '<h3>加载失败</h3>' +
                    '<p>Photo Sphere Viewer 库加载失败</p>' +
                    '<p>请尝试使用 <a href="3d-simple.html">简单版全景查看器</a></p>' +
                    '</div>';
            }
        });

        // 如果使用jQuery全景插件，请使用以下代码：
        /*
        $(document).ready(function() {
            $('#panorama').panorama({
                image: 'images/panorama.jpg',
                autoRotate: true,
                autoRotateSpeed: 0.5
            });
        });
        */
    </script>
</body>

</html>