# 语音聊天系统使用说明

## HTTPS混合内容问题解决方案

### 问题描述
当您通过HTTPS访问语音聊天页面时，浏览器会阻止连接到HTTP的WebSocket服务器，显示"Mixed Content"错误。

### 解决方案

#### 方案1：使用HTTP访问（推荐用于开发测试）
1. 将浏览器地址栏中的 `https://` 改为 `http://`
2. 重新访问页面
3. 这样可以正常连接到HTTP的WebSocket服务器

#### 方案2：配置HTTPS WebSocket服务器（推荐用于生产环境）
1. 在服务器端配置SSL证书
2. 启用WSS（WebSocket Secure）协议
3. 确保服务器监听HTTPS端口（如5008）

#### 方案3：使用代理服务器
1. 配置反向代理（如Nginx）
2. 将HTTPS请求代理到HTTP的WebSocket服务器
3. 在代理层处理SSL终止

### 浏览器兼容性检查

系统会自动检查以下浏览器功能：
- ✅ MediaDevices API（录音功能）
- ✅ AudioContext API（音频分析）
- ✅ MediaRecorder API（音频录制）
- ✅ WebSocket API（实时通信）
- ✅ Socket.IO（WebSocket库）

### 音频格式支持

系统会自动检测并使用浏览器支持的音频格式：
1. `audio/webm;codecs=opus`（首选）
2. `audio/webm`
3. `audio/mp4`
4. `audio/wav`

### 使用步骤

1. **连接服务器**
   - 点击"连接服务器"按钮
   - 等待连接成功提示

2. **开始录音**
   - 点击"开始录音"按钮
   - 允许浏览器访问麦克风
   - 系统会自动检测语音输入

3. **语音交互**
   - 说话时音量条会显示绿色
   - 停止说话后系统自动发送音频
   - 等待服务器回复

4. **调整参数**
   - **音量阈值**：控制语音检测的敏感度
   - **耐心值**：控制静音多久后停止录音

### 故障排除

#### 连接失败
- 检查网络连接
- 确认服务器地址正确
- 尝试切换HTTP/HTTPS协议

#### 录音失败
- 检查麦克风权限
- 确认浏览器支持录音功能
- 尝试刷新页面重新授权

#### 音频播放失败
- 检查浏览器音频权限
- 确认扬声器/耳机正常工作
- 尝试调整系统音量

### 技术参数

- **采样率**：16kHz
- **声道数**：单声道
- **音频格式**：WebM/Opus（优先）
- **录音块大小**：100ms
- **默认音量阈值**：50
- **默认耐心值**：20个音频块

### 安全说明

- HTTPS环境提供更好的安全性
- HTTP环境仅建议用于开发测试
- 生产环境请使用HTTPS + WSS配置
- 音频数据传输建议使用加密连接
