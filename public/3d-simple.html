<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单全景查看器</title>
    <!-- 使用兼容的Three.js版本 -->
    <script src="https://unpkg.com/three@0.149.0/build/three.min.js"></script>

    <!-- 备用CDN -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/149/three.min.js"></script> -->
    <!-- 本地版本（如果CDN不可用） -->
    <!-- <script src="js/three.min.js"></script> -->
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }

        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
        }

        /* 图片切换控制 */
        #imageControls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            color: white;
        }

        #imageControls h4 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #4CAF50;
        }

        .image-selector {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .image-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .image-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .image-btn.active {
            background: #4CAF50;
            border-color: #4CAF50;
        }

        .auto-switch-control {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .switch-checkbox {
            width: 16px;
            height: 16px;
        }

        button {
            background: rgba(255, 255, 255, 0.8);
            border: none;
            padding: 10px 15px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background: rgba(255, 255, 255, 1);
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 100;
        }

        /* 右侧信息面板 */
        #infoPanel {
            position: absolute;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            box-sizing: border-box;
            transition: right 0.3s ease;
            z-index: 200;
            overflow-y: auto;
        }

        #infoPanel.active {
            right: 0;
        }

        #infoPanel h3 {
            margin-top: 0;
            color: #4CAF50;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        #infoPanel .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
        }

        #infoPanel .close-btn:hover {
            color: #ff4444;
        }

        /* 热点样式 */
        .hotspot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #ff4444, #cc0000);
            border: 3px solid white;
            border-radius: 50%;
            cursor: pointer;
            z-index: 150;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
        }

        .hotspot:hover {
            transform: scale(1.2);
            animation: none;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
            }

            50% {
                box-shadow: 0 0 20px rgba(255, 68, 68, 1), 0 0 30px rgba(255, 68, 68, 0.6);
            }

            100% {
                box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
            }
        }
    </style>
</head>

<body>
    <div id="container">
        <div id="loading">正在加载全景图片...</div>
        <div id="info">
            <h3>全景查看器</h3>
            <p>鼠标拖拽：旋转视角</p>
            <p>滚轮：缩放</p>
        </div>
        <div id="controls">
            <button onclick="toggleAutoRotate()">自动旋转</button>
            <button onclick="resetView()">重置视角</button>
            <button onclick="toggleFullscreen()">全屏</button>
        </div>

        <!-- 图片切换控制 -->
        <div id="imageControls">
            <h4>场景切换</h4>
            <div class="image-selector">
                <button class="image-btn active" onclick="switchToImage(0)">主场景</button>
                <button class="image-btn" onclick="switchToImage(1)">场景一</button>
                <button class="image-btn" onclick="switchToImage(2)">场景二</button>
            </div>
            <div class="auto-switch-control">
                <input type="checkbox" id="autoSwitch" class="switch-checkbox" onchange="toggleAutoSwitch()">
                <label for="autoSwitch">自动切换 (10s)</label>
            </div>
        </div>

        <!-- 右侧信息面板 -->
        <div id="infoPanel">
            <button class="close-btn" onclick="closeInfoPanel()">&times;</button>
            <div id="infoPanelContent">
                <h3>选择一个热点</h3>
                <p>点击全景图中的红色热点查看详细信息</p>
            </div>
        </div>
    </div>

    <script>
        let scene, camera, renderer, sphere, texture;
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let targetRotationX = 0, targetRotationY = 0;
        let rotationX = 0, rotationY = 0;
        let autoRotate = false;
        let autoRotateSpeed = 0.002;
        let hotspots = [];
        let raycaster, mouse;

        // 图片切换相关变量
        let currentImageIndex = 0;
        let autoSwitchEnabled = false;
        let autoSwitchInterval = 10000; // 10秒切换一次
        let switchTimer = null;
        let isLoading = false;

        // 图片数据配置
        const imagesData = [
            {
                id: 'full',
                name: '主场景',
                path: 'images/full.jpg',
                hotspots: [
                    {
                        id: 1,
                        position: { x: 200, y: 50, z: -300 },
                        title: "景点介绍 - 主建筑",
                        content: `
                            <h3>主建筑介绍</h3>
                            <p><strong>建筑年代：</strong>明清时期</p>
                            <p><strong>建筑风格：</strong>传统中式建筑</p>
                            <p><strong>主要特色：</strong></p>
                            <ul>
                                <li>飞檐翘角，雕梁画栋</li>
                                <li>采用传统榫卯结构</li>
                                <li>屋顶采用青瓦覆盖</li>
                                <li>整体布局严谨对称</li>
                            </ul>
                            <p><strong>历史价值：</strong>该建筑是当地重要的文化遗产，见证了数百年的历史变迁。</p>
                        `
                    },
                    {
                        id: 2,
                        position: { x: -250, y: 0, z: 200 },
                        title: "景观介绍 - 古树名木",
                        content: `
                            <h3>古树名木</h3>
                            <p><strong>树种：</strong>千年古槐</p>
                            <p><strong>树龄：</strong>约800-1000年</p>
                            <p><strong>特征描述：</strong></p>
                            <ul>
                                <li>树高约25米，胸径2.3米</li>
                                <li>树冠覆盖面积约400平方米</li>
                                <li>枝叶繁茂，四季常青</li>
                                <li>被当地人视为神树</li>
                            </ul>
                            <p><strong>文化意义：</strong>这棵古槐见证了这里的历史变迁，是当地重要的文化地标。</p>
                        `
                    }
                ]
            },
            {
                id: 'f1',
                name: '场景一',
                path: 'images/f1.jpg',
                hotspots: [
                    {
                        id: 3,
                        position: { x: 300, y: 100, z: -200 },
                        title: "场景一 - 特色景观",
                        content: `
                            <h3>场景一特色景观</h3>
                            <p><strong>景观类型：</strong>自然风光</p>
                            <p><strong>最佳观赏时间：</strong>春夏季节</p>
                            <p><strong>景观特色：</strong></p>
                            <ul>
                                <li>山水相映，景色宜人</li>
                                <li>植被丰富，生态良好</li>
                                <li>空气清新，环境优美</li>
                                <li>适合休闲观光</li>
                            </ul>
                            <p><strong>游览建议：</strong>建议游客在此处停留拍照，感受大自然的美好。</p>
                        `
                    },
                    {
                        id: 4,
                        position: { x: -200, y: -50, z: 350 },
                        title: "场景一 - 休息区域",
                        content: `
                            <h3>休息区域</h3>
                            <p><strong>设施配置：</strong>完善的休息设施</p>
                            <p><strong>服务项目：</strong></p>
                            <ul>
                                <li>休息座椅和凉亭</li>
                                <li>饮水设施</li>
                                <li>垃圾回收点</li>
                                <li>指示标牌</li>
                            </ul>
                            <p><strong>温馨提示：</strong>请保持环境整洁，爱护公共设施。</p>
                        `
                    }
                ]
            },
            {
                id: 'f2',
                name: '场景二',
                path: 'images/f2.jpg',
                hotspots: [
                    {
                        id: 5,
                        position: { x: 150, y: 200, z: -400 },
                        title: "场景二 - 文化展示",
                        content: `
                            <h3>文化展示区</h3>
                            <p><strong>展示主题：</strong>传统文化与现代艺术</p>
                            <p><strong>展示内容：</strong></p>
                            <ul>
                                <li>历史文物展览</li>
                                <li>传统工艺演示</li>
                                <li>文化艺术表演</li>
                                <li>互动体验项目</li>
                            </ul>
                            <p><strong>开放时间：</strong>每日9:00-17:00，节假日延长至18:00。</p>
                        `
                    },
                    {
                        id: 6,
                        position: { x: -300, y: 0, z: 250 },
                        title: "场景二 - 观景平台",
                        content: `
                            <h3>观景平台</h3>
                            <p><strong>平台高度：</strong>海拔约200米</p>
                            <p><strong>观景范围：</strong>360度全景视野</p>
                            <p><strong>平台特色：</strong></p>
                            <ul>
                                <li>视野开阔，景色壮观</li>
                                <li>配备望远镜设备</li>
                                <li>安全防护设施完善</li>
                                <li>夜间灯光照明</li>
                            </ul>
                            <p><strong>安全提醒：</strong>请注意安全，不要攀爬护栏。</p>
                        `
                    }
                ]
            }
        ];

        // 当前热点数据（根据当前图片动态设置）
        let currentHotspotsData = [];

        // 初始化
        function init() {
            // 创建场景
            scene = new THREE.Scene();

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 0);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById('container').appendChild(renderer.domElement);

            // 初始化射线检测
            raycaster = new THREE.Raycaster();
            mouse = new THREE.Vector2();

            // 加载初始全景图片
            loadImage(currentImageIndex);

            // 启动动画循环
            animate();

            // 添加事件监听
            addEventListeners();
        }

        // 创建球体
        function createSphere(texture) {
            const geometry = new THREE.SphereGeometry(500, 60, 40);

            // 翻转纹理坐标，使其从内部可见
            geometry.scale(-1, 1, 1);

            const material = new THREE.MeshBasicMaterial({ map: texture });
            sphere = new THREE.Mesh(geometry, material);
            scene.add(sphere);
        }

        // 创建热点
        function createHotspots() {
            // 清除现有热点
            clearHotspots();

            currentHotspotsData.forEach(hotspotData => {
                // 创建热点几何体
                const geometry = new THREE.SphereGeometry(8, 16, 16);
                const material = new THREE.MeshBasicMaterial({
                    color: 0xff4444,
                    transparent: true,
                    opacity: 0.8
                });

                const hotspot = new THREE.Mesh(geometry, material);
                hotspot.position.set(
                    hotspotData.position.x,
                    hotspotData.position.y,
                    hotspotData.position.z
                );

                // 添加用户数据
                hotspot.userData = {
                    id: hotspotData.id,
                    title: hotspotData.title,
                    content: hotspotData.content,
                    isHotspot: true
                };

                scene.add(hotspot);
                hotspots.push(hotspot);
            });
        }

        // 清除热点
        function clearHotspots() {
            hotspots.forEach(hotspot => {
                scene.remove(hotspot);
            });
            hotspots = [];
        }

        // 添加事件监听
        function addEventListeners() {
            // 鼠标事件
            renderer.domElement.addEventListener('mousedown', onMouseDown, false);
            renderer.domElement.addEventListener('mousemove', onMouseMove, false);
            renderer.domElement.addEventListener('mouseup', onMouseUp, false);
            renderer.domElement.addEventListener('wheel', onMouseWheel, false);
            renderer.domElement.addEventListener('click', onMouseClick, false);

            // 触摸事件（移动端支持）
            renderer.domElement.addEventListener('touchstart', onTouchStart, false);
            renderer.domElement.addEventListener('touchmove', onTouchMove, false);
            renderer.domElement.addEventListener('touchend', onTouchEnd, false);

            // 窗口大小变化
            window.addEventListener('resize', onWindowResize, false);
        }

        // 鼠标按下
        function onMouseDown(event) {
            isMouseDown = true;
            mouseX = event.clientX;
            mouseY = event.clientY;
        }

        // 鼠标移动
        function onMouseMove(event) {
            if (!isMouseDown) return;

            const deltaX = event.clientX - mouseX;
            const deltaY = event.clientY - mouseY;

            targetRotationY += deltaX * 0.005;
            targetRotationX += deltaY * 0.005;

            // 限制垂直旋转角度
            targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, targetRotationX));

            mouseX = event.clientX;
            mouseY = event.clientY;
        }

        // 鼠标释放
        function onMouseUp() {
            isMouseDown = false;
        }

        // 鼠标点击
        function onMouseClick(event) {
            // 防止拖拽时触发点击
            if (Math.abs(event.clientX - mouseX) > 5 || Math.abs(event.clientY - mouseY) > 5) {
                return;
            }

            // 计算鼠标位置
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // 射线检测
            raycaster.setFromCamera(mouse, camera);
            const intersects = raycaster.intersectObjects(hotspots);

            if (intersects.length > 0) {
                const clickedHotspot = intersects[0].object;
                if (clickedHotspot.userData.isHotspot) {
                    showHotspotInfo(clickedHotspot.userData);
                }
            }
        }

        // 鼠标滚轮
        function onMouseWheel(event) {
            const fov = camera.fov + event.deltaY * 0.05;
            camera.fov = Math.max(10, Math.min(100, fov));
            camera.updateProjectionMatrix();
        }

        // 触摸开始
        function onTouchStart(event) {
            if (event.touches.length === 1) {
                mouseX = event.touches[0].clientX;
                mouseY = event.touches[0].clientY;
                isMouseDown = true;
            }
        }

        // 触摸移动
        function onTouchMove(event) {
            if (event.touches.length === 1 && isMouseDown) {
                const deltaX = event.touches[0].clientX - mouseX;
                const deltaY = event.touches[0].clientY - mouseY;

                targetRotationY += deltaX * 0.005;
                targetRotationX += deltaY * 0.005;

                targetRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, targetRotationX));

                mouseX = event.touches[0].clientX;
                mouseY = event.touches[0].clientY;
            }
            event.preventDefault();
        }

        // 触摸结束
        function onTouchEnd() {
            isMouseDown = false;
        }

        // 窗口大小变化
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 自动旋转
            if (autoRotate) {
                targetRotationY += autoRotateSpeed;
            }

            // 平滑旋转
            rotationX += (targetRotationX - rotationX) * 0.1;
            rotationY += (targetRotationY - rotationY) * 0.1;

            // 应用旋转
            camera.rotation.x = rotationX;
            camera.rotation.y = rotationY;

            renderer.render(scene, camera);
        }

        // 切换自动旋转
        function toggleAutoRotate() {
            autoRotate = !autoRotate;
        }

        // 重置视角
        function resetView() {
            targetRotationX = 0;
            targetRotationY = 0;
            camera.fov = 75;
            camera.updateProjectionMatrix();
        }

        // 切换全屏
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 显示热点信息
        function showHotspotInfo(hotspotData) {
            const infoPanel = document.getElementById('infoPanel');
            const infoPanelContent = document.getElementById('infoPanelContent');

            infoPanelContent.innerHTML = hotspotData.content;
            infoPanel.classList.add('active');
        }

        // 关闭信息面板
        function closeInfoPanel() {
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.classList.remove('active');
        }

        // 加载图片
        function loadImage(imageIndex) {
            if (isLoading) return;

            console.log('开始加载图片:', imageIndex, imagesData[imageIndex]);

            isLoading = true;
            document.getElementById('loading').style.display = 'block';
            document.getElementById('loading').innerHTML = '正在加载全景图片...';

            const imageData = imagesData[imageIndex];
            currentHotspotsData = imageData.hotspots;

            console.log('图片路径:', imageData.path);
            console.log('热点数据:', currentHotspotsData);

            const loader = new THREE.TextureLoader();
            loader.load(
                imageData.path,
                function (texture) {
                    // 加载成功
                    console.log('图片加载成功:', texture);
                    document.getElementById('loading').style.display = 'none';

                    // 更新球体纹理
                    if (sphere) {
                        console.log('更新现有球体纹理');
                        sphere.material.map = texture;
                        sphere.material.needsUpdate = true;
                    } else {
                        console.log('创建新球体');
                        createSphere(texture);
                    }

                    // 重新创建热点
                    createHotspots();

                    // 更新按钮状态
                    updateImageButtons(imageIndex);

                    console.log('图片加载完成');
                    isLoading = false;
                },
                function (progress) {
                    // 加载进度
                    if (progress.total > 0) {
                        console.log('加载进度:', (progress.loaded / progress.total * 100) + '%');
                    }
                },
                function (error) {
                    // 加载失败
                    console.error('图片加载失败:', error);
                    document.getElementById('loading').innerHTML = '加载失败: ' + imageData.path;
                    isLoading = false;
                }
            );
        }

        // 切换到指定图片
        function switchToImage(imageIndex) {
            if (imageIndex === currentImageIndex || isLoading) return;

            currentImageIndex = imageIndex;
            loadImage(imageIndex);

            // 关闭信息面板
            closeInfoPanel();
        }

        // 更新图片按钮状态
        function updateImageButtons(activeIndex) {
            const buttons = document.querySelectorAll('.image-btn');
            buttons.forEach((btn, index) => {
                if (index === activeIndex) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        // 切换自动切换功能
        function toggleAutoSwitch() {
            const checkbox = document.getElementById('autoSwitch');
            autoSwitchEnabled = checkbox.checked;

            if (autoSwitchEnabled) {
                startAutoSwitch();
            } else {
                stopAutoSwitch();
            }
        }

        // 开始自动切换
        function startAutoSwitch() {
            stopAutoSwitch(); // 先停止现有的定时器
            switchTimer = setInterval(() => {
                if (!isLoading) {
                    const nextIndex = (currentImageIndex + 1) % imagesData.length;
                    switchToImage(nextIndex);
                }
            }, autoSwitchInterval);
        }

        // 停止自动切换
        function stopAutoSwitch() {
            if (switchTimer) {
                clearInterval(switchTimer);
                switchTimer = null;
            }
        }

        // 启动应用
        init();
    </script>
</body>

</html>