<!DOCTYPE html>
<html>

<head>
    <title>FLV Live Player</title>
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            height: 100%;
        }

        .video-container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
            background-color: #000;
        }

        #background-video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translate(-50%, -50%);
            object-fit: cover;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-family: Arial, sans-serif;
            font-size: 18px;
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: red;
            font-family: Arial, sans-serif;
            font-size: 16px;
            text-align: center;
        }
    </style>
    <script src="/flv.min.js"></script>
</head>

<body>
    <div class="video-container">
        <div id="loading" class="loading">LOADING...</div>
        <div id="error" class="error" style="display: none;"></div>
        <video id="background-video" autoplay muted playsinline controls style="opacity: 0;"></video>
    </div>

    <script>
        // FLV流地址 - 根据您的OBS推流配置调整
        const flvUrl = 'https://issac.qj-robots.com/live/isaac_livestream.flv';
        // 备用地址，如果上面的不工作，可以尝试这些格式：
        // const flvUrl = 'http://issac.qj-robots.com/live/isaac_livestream/016bb6a2fa5c41daa5ce96dd710d2290.flv';
        // const flvUrl = 'ws://issac.qj-robots.com/live/isaac_livestream.flv'; // WebSocket FLV

        const videoElement = document.getElementById('background-video');
        const loadingElement = document.getElementById('loading');
        const errorElement = document.getElementById('error');

        function showError(message) {
            loadingElement.style.display = 'none';
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            console.error('播放错误:', message);
        }

        function hideLoading() {
            loadingElement.style.display = 'none';
        }

        function showVideo() {
            hideLoading();
            videoElement.style.opacity = '1';
            videoElement.style.transition = 'opacity 0.5s ease-in-out';
        }

        // 检查flv.js支持
        if (flvjs.isSupported()) {
            const flvPlayer = flvjs.createPlayer({
                type: 'flv',
                url: flvUrl,
                isLive: true
            }, {
                enableWorker: false,
                enableStashBuffer: false,
                stashInitialSize: 128,
                autoCleanupSourceBuffer: true,
                autoCleanupMaxBackwardDuration: 30,
                autoCleanupMinBackwardDuration: 10,
                fixAudioTimestampGap: true,
                accurateSeek: false,
                seekType: 'range',
                seekParamStart: 'bstart',
                seekParamEnd: 'bend',
                rangeLoadZeroStart: false,
                lazyLoad: true,
                lazyLoadMaxDuration: 3 * 60,
                lazyLoadRecoverDuration: 30,
                deferLoadAfterSourceOpen: true,
                reuseRedirectedURL: false,
                referrerPolicy: 'no-referrer-when-downgrade'
            });

            flvPlayer.attachMediaElement(videoElement);

            // 事件监听
            flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
                console.log('FLV加载完成');
                hideLoading();
            });

            flvPlayer.on(flvjs.Events.CANPLAY, () => {
                console.log('可以开始播放');
                showVideo();
            });

            flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
                console.error('FLV播放错误:', errorType, errorDetail, errorInfo);
                showError(`播放失败: ${errorDetail}`);

                // 尝试重连
                setTimeout(() => {
                    console.log('尝试重新连接...');
                    flvPlayer.unload();
                    flvPlayer.load();
                }, 3000);
            });

            // 视频元素事件
            videoElement.addEventListener('loadstart', () => {
                console.log('开始加载视频');
            });

            videoElement.addEventListener('canplay', () => {
                console.log('视频可以播放');
                showVideo();
            });

            videoElement.addEventListener('error', (e) => {
                console.error('视频元素错误:', e);
                showError('视频播放出错，请检查流地址是否正确');
            });

            // 开始加载和播放
            try {
                flvPlayer.load();
                flvPlayer.play().catch(error => {
                    console.log('自动播放被阻止，尝试静音播放');
                    videoElement.muted = true;
                    flvPlayer.play().catch(err => {
                        showError('无法播放视频，请手动点击播放按钮');
                    });
                });
            } catch (error) {
                showError('初始化播放器失败: ' + error.message);
            }

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                if (flvPlayer) {
                    flvPlayer.pause();
                    flvPlayer.unload();
                    flvPlayer.detachMediaElement();
                    flvPlayer.destroy();
                }
            });

        } else {
            showError('您的浏览器不支持FLV播放，请使用Chrome、Firefox或Edge浏览器');
        }

        // 处理浏览器自动播放策略
        document.addEventListener('DOMContentLoaded', () => {
            // 点击页面任意位置尝试播放
            document.addEventListener('click', () => {
                if (videoElement.paused) {
                    videoElement.play().catch(error => {
                        console.log('手动播放失败:', error);
                    });
                }
            }, { once: true });
        });
    </script>
</body>

</html>