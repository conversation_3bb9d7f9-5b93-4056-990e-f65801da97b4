# 语音聊天取消功能实现

## 概述

在语音聊天系统中实现了任务取消功能，当停止任务轮询时会自动调用服务器接口 `POST /voice/chat/cancel` 来取消正在进行的任务。

## 修改内容

### 1. API 接口

在 `src/api/voiceChat.js` 中已经存在 `cancelTask` 方法：

```javascript
/**
 * 取消语音处理任务
 * @param {string} taskId - 任务ID
 * @returns {Promise} 返回操作结果
 */
cancelTask: (taskId) => {
  return doPost('/open-apis/voice/chat/cancel', { taskId })
}
```

### 2. 核心逻辑修改

在 `src/composables/useVoiceChatCOS.js` 中添加了以下功能：

#### 新增辅助函数

```javascript
// 取消当前任务（内部辅助函数）
const cancelCurrentTask = async (reason = '手动取消') => {
  if (currentTask.value && currentTask.value.taskId) {
    try {
      addDebugMessage('system', `正在取消任务: ${currentTask.value.taskId} (${reason})`, 'status')
      const result = await voiceChatApi.cancelTask(currentTask.value.taskId)

      if (result.code === 0) {
        addDebugMessage('system', '任务取消成功', 'status')
        currentTask.value.status = 'cancelled'
      } else {
        addDebugMessage('system', `任务取消失败: ${result.message || '未知错误'}`, 'status')
      }
    } catch (error) {
      console.error('取消任务失败:', error)
      addDebugMessage('system', `取消任务失败: ${error.message}`, 'status')
    }
  } else {
    // 没有当前任务时的日志
    addDebugMessage('system', `尝试取消任务 (${reason})，但没有活跃任务`, 'debug')
  }
}
```

#### 修改开始录音函数

```javascript
// 开始录音
const startRecording = async () => {
  // 在开始新的录音前，先取消可能存在的旧任务
  await cancelCurrentTask('开始新录音')

  if (!await initAudio()) return

  isRecording.value = true
  recordingState.value = 'waiting'
  recordingFrames = []
  silenceCounter = 0
  recordingStartTime = Date.now()

  updateRecordingStatus()
  monitorVolume()

  addDebugMessage('user', '开始监听语音输入...', 'debug')
}
```

#### 修改组件生命周期

```javascript
// 生命周期
onMounted(async () => {
  // 组件加载时先取消可能存在的旧任务
  await cancelCurrentTask('组件加载')
  connect()
})
```

#### 修改停止轮询函数

```javascript
// 停止任务轮询
const stopTaskPolling = async () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
    isProcessing.value = false
    addDebugMessage('system', '手动停止轮询', 'status')

    // 调用取消任务接口
    await cancelCurrentTask('手动停止')

    // 移除可能残留的loading消息
    const loadingMessageIndex = messages.value.findIndex(msg => msg.isLoading)
    if (loadingMessageIndex !== -1) {
      messages.value.splice(loadingMessageIndex, 1)
    }
  }
}
```

### 3. 触发取消的场景

系统会在以下情况下自动调用取消接口：

1. **组件加载**: 页面加载时清理可能存在的旧任务
2. **开始新录音**: 点击说话按钮时清理可能存在的旧任务
3. **手动停止**: 用户点击停止按钮时
4. **超时**: 轮询超过60秒时
5. **没有更多消息**: API返回错误且已收到回复时
6. **网络错误**: 网络异常且已收到回复时
7. **组件卸载**: 页面关闭或组件销毁时

### 4. UI 交互

在 `src/views/chat/VoiceChat.vue` 中已经存在停止按钮：

```vue
<button
  v-if="isProcessing"
  @click="stopTaskPolling"
  class="stop-btn"
  title="停止等待更多消息"
>
  <svg class="stop-icon" viewBox="0 0 24 24" fill="currentColor">
    <path d="M6 6h12v12H6z" />
  </svg>
</button>
```

## 使用方式

### 自动取消场景

1. **页面加载时**: 组件自动取消可能存在的旧任务
2. **开始新录音时**: 点击说话按钮自动取消旧任务，确保新录音正常进行
3. **页面关闭时**: 组件卸载时自动取消正在进行的任务

### 手动取消场景

1. 用户开始语音录制并提交任务
2. 系统开始轮询任务状态，UI 显示停止按钮
3. 用户可以随时点击停止按钮来取消任务
4. 系统会调用 `POST /voice/chat/cancel` 接口通知服务器取消任务
5. 清理本地状态并移除 loading 消息

## 日志输出

系统会在控制台输出详细的调试信息：

- `正在取消任务: task_xxx (原因)`
- `任务取消成功`
- `任务取消失败: 错误信息`
- `尝试取消任务 (原因)，但没有活跃任务`

### 常见日志示例

```
[14:30:15] [DEBUG] system: 尝试取消任务 (组件加载)，但没有活跃任务
[14:30:20] [DEBUG] system: 尝试取消任务 (开始新录音)，但没有活跃任务
[14:30:25] [STATUS] system: 正在取消任务: task_1234567890 (手动停止)
[14:30:25] [STATUS] system: 任务取消成功
```

## 错误处理

- 如果取消接口调用失败，会在控制台输出错误信息但不影响 UI 状态清理
- 即使取消失败，本地状态仍会正确清理，确保用户界面正常
- 所有异常都会被捕获并记录，不会影响应用稳定性

## 兼容性

- 该功能向后兼容，不影响现有的语音聊天流程
- 如果服务器不支持取消接口，客户端仍能正常工作
- 取消操作是异步的，不会阻塞 UI 响应
