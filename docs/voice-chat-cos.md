# 语音聊天系统 - COS上传版本

## 概述

基于COS云存储的语音聊天系统，采用HTTP + 文件上传的方式替代传统的WebSocket实时传输，解决了HTTPS环境下的混合内容问题，提供更稳定可靠的语音交互体验。

## 🚀 新特性

### 1. 通信架构升级
- **从WebSocket到HTTP**: 使用RESTful API + 轮询机制
- **COS云存储**: 音频文件上传到腾讯云对象存储
- **任务队列**: 异步处理音频任务，支持高并发

### 2. HTTPS兼容性
- **原生HTTPS支持**: 无需配置WSS服务器
- **混合内容解决**: 避免浏览器安全限制
- **跨域友好**: 标准HTTP请求，易于配置

### 3. 用户体验优化
- **智能录音检测**: 基于音量阈值自动开始/停止
- **实时状态显示**: 录音时长、任务进度可视化
- **错误处理增强**: 详细的错误提示和重试机制

## 📁 文件结构

```
src/
├── views/
│   ├── VoiceChat.vue          # 主要语音聊天界面
│   └── VoiceChatTest.vue      # 系统测试页面
├── composables/
│   └── useVoiceChatCOS.js     # 语音聊天组合式函数
├── utils/
│   └── audioUpload.js         # 音频上传工具函数
├── api/
│   └── voiceChat.js           # 语音聊天API接口
├── config/
│   └── voice-chat.js          # 配置文件
└── docs/
    └── voice-chat-cos.md      # 本文档
```

## 🔧 技术架构

### 前端技术栈
- **Vue 3** + Composition API
- **Naive UI** 组件库
- **Web Audio API** 音频处理
- **MediaRecorder API** 音频录制

### 通信流程
1. **录音检测**: 基于音量阈值自动开始/停止录音
2. **文件上传**: 将音频文件上传到COS云存储
3. **任务提交**: 通过API通知服务器处理音频文件
4. **状态轮询**: 定期查询任务处理状态
5. **结果获取**: 接收并播放服务器回复

### 配置参数
```javascript
// 音频配置
audio: {
  sampleRate: 16000,        // 采样率
  channelCount: 1,          // 声道数
  echoCancellation: true,   // 回声消除
  noiseSuppression: true    // 噪声抑制
}

// 录音配置
recording: {
  defaultThreshold: 50,     // 默认音量阈值
  defaultPatience: 20,      // 默认耐心值
  maxDuration: 60,          // 最大录音时长(秒)
  maxFileSize: 10MB         // 最大文件大小
}

// 通信配置
communication: {
  mode: 'http',             // 通信模式
  pollingInterval: 2000,    // 轮询间隔(毫秒)
  maxPollingCount: 150      // 最大轮询次数
}
```

## 🛠️ 使用方法

### 1. 访问页面
- **主要功能**: `/voice-chat`
- **系统测试**: `/voice-chat-test`

### 2. 功能测试
访问测试页面进行系统检查：
```bash
# 浏览器访问
http://localhost:3000/voice-chat-test
```

测试项目包括：
- 浏览器兼容性检查
- 麦克风权限测试
- COS上传功能测试
- API连接测试

### 3. 开始使用
1. 点击"连接服务器"按钮
2. 点击"开始录音"并允许麦克风权限
3. 说话时系统自动检测并上传音频
4. 等待服务器处理并播放回复

## 📋 API接口

### 1. 提交语音任务
```javascript
POST /open-apis/voice/chat/submit
{
  "audioKey": "voice_**********.webm",
  "audioFormat": "webm",
  "duration": 3.5,
  "metadata": {
    "threshold": 50,
    "patience": 20,
    "timestamp": **********
  }
}
```

### 2. 查询任务结果
```javascript
GET /open-apis/voice/chat/result?taskId=task_**********

Response:
{
  "code": 0,
  "data": {
    "taskStatus": "COMPLETED",
    "taskResult": {
      "textResponse": "你好，我是AI助手",
      "audioResponse": "https://cos.example.com/audio/reply.wav"
    }
  }
}
```

### 3. 健康检查
```javascript
GET /open-apis/voice/chat/health

Response:
{
  "code": 0,
  "message": "Service is healthy"
}
```

## 🔍 故障排除

### 常见问题

1. **麦克风权限被拒绝**
   - 检查浏览器权限设置
   - 确保使用HTTPS或localhost访问
   - 尝试刷新页面重新授权

2. **COS上传失败**
   - 检查网络连接
   - 确认COS配置正确
   - 查看浏览器控制台错误信息

3. **API连接失败**
   - 检查服务器状态
   - 确认API地址配置
   - 查看网络请求响应

4. **音频录制问题**
   - 确认浏览器支持MediaRecorder
   - 检查音频格式兼容性
   - 调整音量阈值参数

### 调试方法

1. **开启控制台日志**
   ```javascript
   // 在浏览器控制台中查看详细日志
   localStorage.setItem('debug', 'true')
   ```

2. **检查浏览器支持**
   ```javascript
   // 访问测试页面查看兼容性报告
   /voice-chat-test
   ```

3. **网络请求监控**
   - 打开浏览器开发者工具
   - 查看Network标签页
   - 监控API请求和响应

## 🔄 从WebSocket版本迁移

### 主要变化
1. **通信方式**: WebSocket → HTTP + 轮询
2. **数据传输**: 实时流 → 文件上传
3. **状态管理**: 连接状态 → 任务状态
4. **错误处理**: 重连机制 → 重试机制

### 迁移步骤
1. 更新前端代码使用新的组合式函数
2. 配置COS上传参数
3. 部署新的API接口
4. 测试完整流程
5. 更新用户文档

## 📈 性能优化

### 前端优化
- 音频数据压缩
- 上传进度显示
- 错误重试机制
- 内存使用优化

### 后端优化
- 异步任务处理
- 文件存储优化
- 缓存机制
- 负载均衡

## 🔐 安全考虑

### 数据安全
- HTTPS传输加密
- 音频文件临时存储
- 访问权限控制
- 敏感信息脱敏

### 隐私保护
- 录音权限明确提示
- 音频文件自动清理
- 用户数据匿名化
- 合规性检查

## 📞 技术支持

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 文档: https://docs.qj-robots.com
- 问题反馈: https://github.com/qj-robots/issues
